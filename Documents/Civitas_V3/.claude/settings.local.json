{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(chmod:*)", "Bash(for route in auth users posts messages emergency events exchange notifications search media community settings analytics)", "Bash(do if [ -f \"$route.js\" ])", "Bash([ -f \"$route.ts\" ])", "Bash(then echo \"✓ $route - EXISTS\")", "Bash(else echo \"✗ $route - MISSING\")", "Bash(fi)", "Bash(done)", "Bash(brew services:*)", "Bash(./setup-database.sh:*)", "Bash(./test-db-connection.sh:*)", "Bash(./setup-database-fixed.sh:*)", "Bash(psql:*)", "Bash(rm:*)", "Bash(brew install:*)", "Bash(brew list:*)", "Bash(for:*)", "Bash(do echo \"Running $file...\")", "Bash(/dev/null)", "Bash(npm install:*)", "Bash(npm view:*)", "Bash(node:*)", "<PERSON><PERSON>(cat:*)", "Bash(DB_NAME=civitas DB_USER=postgres node test-db-connection.js)"], "deny": []}}