const { Pool } = require('pg');
const logger = require('./logger');

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5433,
  database: process.env.DB_NAME || 'civitas',
  user: process.env.DB_USER || 'civitas_user',
  password: process.env.DB_PASSWORD,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle
  connectionTimeoutMillis: 2000, // How long to wait for connection
});

// Log pool errors
pool.on('error', (err, client) => {
  logger.error('Unexpected database pool error', err);
});

// Helper function for transactions
const withTransaction = async (callback) => {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
};

// Helper function for single queries
const query = async (text, params) => {
  const start = Date.now();
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;
    
    // Log slow queries
    if (duration > 1000) {
      logger.warn('Slow query detected', {
        text,
        duration,
        rows: result.rowCount
      });
    }
    
    return result;
  } catch (error) {
    logger.error('Database query error', {
      text,
      error: error.message
    });
    throw error;
  }
};

// Helper for geospatial queries
const geoQuery = {
  // Find neighbors within radius
  nearbyUsers: (lat, lon, radiusMiles = 1.0) => {
    return `
      SELECT u.*, 
        ST_Distance(u.location::geography, ST_MakePoint($1, $2)::geography) / 1609.34 as distance_miles
      FROM users u
      WHERE ST_DWithin(
        u.location::geography,
        ST_MakePoint($1, $2)::geography,
        $3 * 1609.34
      )
      AND u.status = 'active'
      ORDER BY distance_miles ASC
    `;
  },
  
  // Check if user is in neighborhood
  isInNeighborhood: () => {
    return `
      SELECT EXISTS (
        SELECT 1 
        FROM neighborhoods n
        WHERE ST_DWithin(
          n.center_location::geography,
          ST_MakePoint($1, $2)::geography,
          n.radius_miles * 1609.34
        )
        AND n.id = $3
      )
    `;
  }
};

// Helper for full-text search
const searchQuery = {
  // Search posts
  posts: () => {
    return `
      SELECT p.*, 
        ts_rank_cd(
          to_tsvector('english', p.content || ' ' || COALESCE(p.title, '')),
          plainto_tsquery('english', $1)
        ) as rank
      FROM posts p
      WHERE to_tsvector('english', p.content || ' ' || COALESCE(p.title, '')) 
        @@ plainto_tsquery('english', $1)
      AND p.status = 'active'
      ORDER BY rank DESC, p.created_at DESC
      LIMIT $2 OFFSET $3
    `;
  },
  
  // Search users
  users: () => {
    return `
      SELECT u.*,
        ts_rank_cd(
          to_tsvector('english', u.first_name || ' ' || u.last_name || ' ' || COALESCE(u.bio, '')),
          plainto_tsquery('english', $1)
        ) as rank
      FROM users u
      WHERE to_tsvector('english', u.first_name || ' ' || u.last_name || ' ' || COALESCE(u.bio, ''))
        @@ plainto_tsquery('english', $1)
      AND u.status = 'active'
      ORDER BY rank DESC
      LIMIT $2 OFFSET $3
    `;
  }
};

module.exports = {
  pool,
  query,
  withTransaction,
  geoQuery,
  searchQuery
};