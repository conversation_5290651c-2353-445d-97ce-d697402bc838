const Redis = require('ioredis');
const logger = require('./logger');

const redisClient = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD,
  retryStrategy: (times) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  },
  maxRetriesPerRequest: 3,
  enableReadyCheck: true,
  enableOfflineQueue: true
});

// Redis event handlers
redisClient.on('connect', () => {
  logger.info('Redis client connected');
});

redisClient.on('error', (err) => {
  logger.error('Redis client error:', err);
});

redisClient.on('ready', () => {
  logger.info('Redis client ready');
});

// Cache helpers
const cache = {
  // Generic get with JSON parsing
  get: async (key) => {
    try {
      const value = await redisClient.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Cache get error:', error);
      return null;
    }
  },

  // Generic set with JSON stringification
  set: async (key, value, expirySeconds = 3600) => {
    try {
      await redisClient.setex(key, expirySeconds, JSON.stringify(value));
      return true;
    } catch (error) {
      logger.error('Cache set error:', error);
      return false;
    }
  },

  // Delete key
  del: async (key) => {
    try {
      await redisClient.del(key);
      return true;
    } catch (error) {
      logger.error('Cache delete error:', error);
      return false;
    }
  },

  // Delete keys by pattern
  delByPattern: async (pattern) => {
    try {
      const keys = await redisClient.keys(pattern);
      if (keys.length > 0) {
        await redisClient.del(...keys);
      }
      return true;
    } catch (error) {
      logger.error('Cache delete by pattern error:', error);
      return false;
    }
  }
};

// Session store helpers
const sessions = {
  // Store user session
  set: async (userId, sessionData) => {
    const key = `session:${userId}`;
    const expiry = 24 * 60 * 60; // 24 hours
    return cache.set(key, sessionData, expiry);
  },

  // Get user session
  get: async (userId) => {
    const key = `session:${userId}`;
    return cache.get(key);
  },

  // Delete user session
  del: async (userId) => {
    const key = `session:${userId}`;
    return cache.del(key);
  },

  // Delete all user sessions (logout from all devices)
  delAll: async (userId) => {
    const pattern = `session:${userId}:*`;
    return cache.delByPattern(pattern);
  }
};

// Rate limiting helpers
const rateLimiter = {
  // Check if action is allowed
  check: async (identifier, limit = 100, windowSeconds = 3600) => {
    const key = `rate:${identifier}`;
    const current = await redisClient.incr(key);
    
    if (current === 1) {
      await redisClient.expire(key, windowSeconds);
    }
    
    return {
      allowed: current <= limit,
      remaining: Math.max(0, limit - current),
      resetAt: Date.now() + (windowSeconds * 1000)
    };
  },

  // Reset rate limit
  reset: async (identifier) => {
    const key = `rate:${identifier}`;
    return cache.del(key);
  }
};

// Real-time location tracking for emergencies
const locationTracking = {
  // Update user location during emergency
  update: async (emergencyId, userId, location) => {
    const key = `emergency:${emergencyId}:location:${userId}`;
    const data = {
      ...location,
      timestamp: Date.now()
    };
    return cache.set(key, data, 3600); // 1 hour expiry
  },

  // Get all responder locations for an emergency
  getAll: async (emergencyId) => {
    const pattern = `emergency:${emergencyId}:location:*`;
    const keys = await redisClient.keys(pattern);
    const locations = {};
    
    for (const key of keys) {
      const userId = key.split(':').pop();
      const location = await cache.get(key);
      if (location) {
        locations[userId] = location;
      }
    }
    
    return locations;
  },

  // Clean up emergency location data
  cleanup: async (emergencyId) => {
    const pattern = `emergency:${emergencyId}:*`;
    return cache.delByPattern(pattern);
  }
};

// WebSocket connection management
const socketConnections = {
  // Store socket connection info
  add: async (userId, socketId, metadata = {}) => {
    const key = `socket:${userId}:${socketId}`;
    const data = {
      socketId,
      connectedAt: Date.now(),
      ...metadata
    };
    return cache.set(key, data, 86400); // 24 hours
  },

  // Remove socket connection
  remove: async (userId, socketId) => {
    const key = `socket:${userId}:${socketId}`;
    return cache.del(key);
  },

  // Get all socket connections for a user
  getAll: async (userId) => {
    const pattern = `socket:${userId}:*`;
    const keys = await redisClient.keys(pattern);
    const connections = [];
    
    for (const key of keys) {
      const connection = await cache.get(key);
      if (connection) {
        connections.push(connection);
      }
    }
    
    return connections;
  },

  // Check if user is online
  isOnline: async (userId) => {
    const connections = await socketConnections.getAll(userId);
    return connections.length > 0;
  }
};

module.exports = {
  redisClient,
  cache,
  sessions,
  rateLimiter,
  locationTracking,
  socketConnections
};