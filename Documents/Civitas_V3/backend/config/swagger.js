const swaggerJsdoc = require('swagger-jsdoc');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Civitas API',
      version: '1.0.0',
      description: 'Civitas Backend API - Where Neighbors Save Lives',
      contact: {
        name: 'Civitas Team',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:3000',
        description: 'Development server'
      },
      {
        url: 'https://api.civitas.app',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Enter your JWT token'
        }
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            error: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Error message'
                },
                code: {
                  type: 'string',
                  example: 'ERROR_CODE'
                },
                details: {
                  type: 'array',
                  items: {
                    type: 'object'
                  }
                }
              }
            }
          }
        },
        Success: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            data: {
              type: 'object'
            }
          }
        },
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid'
            },
            email: {
              type: 'string',
              format: 'email'
            },
            first_name: {
              type: 'string'
            },
            last_name: {
              type: 'string'
            },
            profile_photo_url: {
              type: 'string',
              nullable: true
            },
            bio: {
              type: 'string',
              nullable: true
            },
            is_verified: {
              type: 'boolean'
            },
            is_emergency_responder: {
              type: 'boolean'
            },
            trust_score: {
              type: 'integer'
            },
            created_at: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        Post: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid'
            },
            user_id: {
              type: 'string',
              format: 'uuid'
            },
            post_type: {
              type: 'string',
              enum: ['general', 'need_help', 'offer_help', 'safety_alert', 'lost_found', 'recommendation']
            },
            content: {
              type: 'string'
            },
            status: {
              type: 'string',
              enum: ['draft', 'active', 'resolved', 'expired', 'hidden']
            },
            created_at: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        Emergency: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid'
            },
            requester_id: {
              type: 'string',
              format: 'uuid'
            },
            emergency_type: {
              type: 'string',
              enum: ['medical', 'fire', 'safety', 'natural_disaster', 'other']
            },
            description: {
              type: 'string'
            },
            status: {
              type: 'string',
              enum: ['active', 'responding', 'resolved', 'cancelled', 'test']
            },
            location: {
              type: 'object',
              properties: {
                latitude: {
                  type: 'number'
                },
                longitude: {
                  type: 'number'
                }
              }
            },
            created_at: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        LoginRequest: {
          type: 'object',
          required: ['email', 'password'],
          properties: {
            email: {
              type: 'string',
              format: 'email',
              example: '<EMAIL>'
            },
            password: {
              type: 'string',
              format: 'password',
              example: 'SecurePassword123!'
            }
          }
        },
        RegisterRequest: {
          type: 'object',
          required: ['email', 'password', 'first_name', 'last_name'],
          properties: {
            email: {
              type: 'string',
              format: 'email',
              example: '<EMAIL>'
            },
            password: {
              type: 'string',
              format: 'password',
              minLength: 8,
              example: 'SecurePassword123!'
            },
            first_name: {
              type: 'string',
              example: 'John'
            },
            last_name: {
              type: 'string',
              example: 'Doe'
            },
            bio: {
              type: 'string',
              example: 'Friendly neighbor who loves to help'
            }
          }
        },
        TokenResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            data: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                  example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
                },
                refresh_token: {
                  type: 'string',
                  example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
                },
                expires_in: {
                  type: 'integer',
                  example: 86400
                },
                user: {
                  $ref: '#/components/schemas/User'
                }
              }
            }
          }
        }
      },
      parameters: {
        limitParam: {
          name: 'limit',
          in: 'query',
          description: 'Number of items to return',
          schema: {
            type: 'integer',
            minimum: 1,
            maximum: 100,
            default: 20
          }
        },
        offsetParam: {
          name: 'offset',
          in: 'query',
          description: 'Number of items to skip',
          schema: {
            type: 'integer',
            minimum: 0,
            default: 0
          }
        },
        sortParam: {
          name: 'sort',
          in: 'query',
          description: 'Sort order',
          schema: {
            type: 'string',
            enum: ['asc', 'desc'],
            default: 'desc'
          }
        }
      },
      responses: {
        UnauthorizedError: {
          description: 'Authentication required',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                success: false,
                error: {
                  message: 'Not authorized to access this route',
                  code: 'UNAUTHORIZED'
                }
              }
            }
          }
        },
        NotFoundError: {
          description: 'Resource not found',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                success: false,
                error: {
                  message: 'Resource not found',
                  code: 'NOT_FOUND'
                }
              }
            }
          }
        },
        ValidationError: {
          description: 'Validation failed',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                success: false,
                error: {
                  message: 'Validation failed',
                  code: 'VALIDATION_ERROR',
                  details: [
                    {
                      field: 'email',
                      message: 'Invalid email format'
                    }
                  ]
                }
              }
            }
          }
        },
        ServerError: {
          description: 'Internal server error',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                success: false,
                error: {
                  message: 'Internal server error',
                  code: 'SERVER_ERROR'
                }
              }
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ],
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and authorization'
      },
      {
        name: 'Users',
        description: 'User profile and account management'
      },
      {
        name: 'Emergency',
        description: '🚨 Emergency response system'
      },
      {
        name: 'Posts',
        description: 'Neighborhood posts and feed'
      },
      {
        name: 'Messages',
        description: 'Direct messaging between neighbors'
      },
      {
        name: 'Events',
        description: 'Community events management'
      },
      {
        name: 'Exchange',
        description: 'Item sharing and exchange'
      },
      {
        name: 'Notifications',
        description: 'Push notifications and alerts'
      },
      {
        name: 'Search',
        description: 'Search functionality'
      },
      {
        name: 'Media',
        description: 'File uploads and media management'
      },
      {
        name: 'Community',
        description: 'Community features and metrics'
      }
    ]
  },
  apis: ['./routes/*.js', './routes/**/*.js'] // Path to the API routes
};

const swaggerSpec = swaggerJsdoc(options);

module.exports = swaggerSpec;