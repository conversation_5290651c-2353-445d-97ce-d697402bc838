const multer = require('multer');
const multerS3 = require('multer-s3');
const AWS = require('aws-sdk');
const { AppError } = require('./errorHandler');

// Configure AWS S3
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION
});

// File filter
const fileFilter = (req, file, cb) => {
  // Allowed file types
  const allowedTypes = process.env.ALLOWED_FILE_TYPES?.split(',') || 
    ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'mp3', 'm4a', 'wav'];
  
  const fileExtension = file.originalname.split('.').pop().toLowerCase();
  
  if (allowedTypes.includes(fileExtension)) {
    cb(null, true);
  } else {
    cb(new AppError(`File type .${fileExtension} not allowed`, 400), false);
  }
};

// Storage configuration for different file types
const createStorage = (folder) => {
  return multerS3({
    s3: s3,
    bucket: process.env.AWS_S3_BUCKET,
    acl: 'public-read',
    contentType: multerS3.AUTO_CONTENT_TYPE,
    key: function (req, file, cb) {
      const userId = req.user?.id || 'anonymous';
      const timestamp = Date.now();
      const fileExtension = file.originalname.split('.').pop();
      cb(null, `${folder}/${userId}/${timestamp}.${fileExtension}`);
    }
  });
};

// Create multer instances for different upload types
const uploadConfig = {
  // Profile photos
  profilePhoto: multer({
    storage: createStorage('profile-photos'),
    fileFilter: fileFilter,
    limits: {
      fileSize: 5 * 1024 * 1024 // 5MB
    }
  }),

  // Cover photos
  coverPhoto: multer({
    storage: createStorage('cover-photos'),
    fileFilter: fileFilter,
    limits: {
      fileSize: 10 * 1024 * 1024 // 10MB
    }
  }),

  // Post media
  postMedia: multer({
    storage: createStorage('post-media'),
    fileFilter: fileFilter,
    limits: {
      fileSize: parseInt(process.env.MAX_FILE_SIZE_MB) * 1024 * 1024 || 10 * 1024 * 1024
    }
  }),

  // Message attachments
  messageAttachment: multer({
    storage: createStorage('message-attachments'),
    fileFilter: fileFilter,
    limits: {
      fileSize: 10 * 1024 * 1024 // 10MB
    }
  }),

  // Voice messages
  voiceMessage: multer({
    storage: createStorage('voice-messages'),
    fileFilter: (req, file, cb) => {
      const allowedTypes = ['mp3', 'm4a', 'wav', 'webm'];
      const fileExtension = file.originalname.split('.').pop().toLowerCase();
      
      if (allowedTypes.includes(fileExtension)) {
        cb(null, true);
      } else {
        cb(new AppError('Invalid audio format', 400), false);
      }
    },
    limits: {
      fileSize: 5 * 1024 * 1024 // 5MB
    }
  })
};

// For development/testing without S3
const localStorageConfig = {
  storage: multer.diskStorage({
    destination: function (req, file, cb) {
      cb(null, 'uploads/')
    },
    filename: function (req, file, cb) {
      const userId = req.user?.id || 'anonymous';
      const timestamp = Date.now();
      const fileExtension = file.originalname.split('.').pop();
      cb(null, `${userId}-${timestamp}.${fileExtension}`);
    }
  }),
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024
  }
};

// Export the appropriate upload middleware based on environment
const upload = process.env.NODE_ENV === 'production' || process.env.AWS_S3_BUCKET
  ? uploadConfig.postMedia // Default to post media config
  : multer(localStorageConfig);

// Export specific upload types
module.exports = {
  single: upload.single.bind(upload),
  array: upload.array.bind(upload),
  fields: upload.fields.bind(upload),
  none: upload.none.bind(upload),
  any: upload.any.bind(upload),
  
  // Specific upload configs
  profilePhoto: process.env.NODE_ENV === 'production' 
    ? uploadConfig.profilePhoto 
    : multer(localStorageConfig),
  coverPhoto: process.env.NODE_ENV === 'production'
    ? uploadConfig.coverPhoto
    : multer(localStorageConfig),
  postMedia: process.env.NODE_ENV === 'production'
    ? uploadConfig.postMedia
    : multer(localStorageConfig),
  messageAttachment: process.env.NODE_ENV === 'production'
    ? uploadConfig.messageAttachment
    : multer(localStorageConfig),
  voiceMessage: process.env.NODE_ENV === 'production'
    ? uploadConfig.voiceMessage
    : multer(localStorageConfig)
};