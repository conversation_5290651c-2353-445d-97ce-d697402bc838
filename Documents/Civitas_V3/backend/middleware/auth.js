const jwt = require('jsonwebtoken');
const { query } = require('../config/database');
const { sessions } = require('../config/redis');
const { AppError, asyncHandler } = require('./errorHandler');

// Verify JWT token
const verifyToken = (token) => {
  return jwt.verify(token, process.env.JWT_SECRET);
};

// Protect routes - require authentication
const protect = asyncHandler(async (req, res, next) => {
  let token;

  // Check for token in header
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  // Check for token in cookies (for web clients)
  if (!token && req.cookies?.token) {
    token = req.cookies.token;
  }

  if (!token) {
    throw new AppError('Not authorized to access this route', 401, 'NO_TOKEN');
  }

  try {
    // Verify token
    const decoded = verifyToken(token);

    // Check if session exists in Redis
    const session = await sessions.get(decoded.id);
    if (!session || session.token !== token) {
      throw new AppError('Session expired or invalid', 401, 'INVALID_SESSION');
    }

    // Get user from database
    const result = await query(
      `SELECT id, email, first_name, last_name, role, status, neighborhood_id, 
              is_verified, is_emergency_responder
       FROM users 
       WHERE id = $1 AND status = 'active'`,
      [decoded.id]
    );

    if (result.rows.length === 0) {
      throw new AppError('User not found or inactive', 401, 'USER_NOT_FOUND');
    }

    // Attach user to request
    req.user = result.rows[0];
    req.token = token;

    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new AppError('Token expired', 401, 'TOKEN_EXPIRED');
    }
    if (error.name === 'JsonWebTokenError') {
      throw new AppError('Invalid token', 401, 'INVALID_TOKEN');
    }
    throw error;
  }
});

// Check if user is verified
const requireVerified = asyncHandler(async (req, res, next) => {
  if (!req.user.is_verified) {
    throw new AppError('Please verify your email address', 403, 'EMAIL_NOT_VERIFIED');
  }
  next();
});

// Check if user has verified address
const requireAddress = asyncHandler(async (req, res, next) => {
  if (!req.user.neighborhood_id) {
    throw new AppError('Please verify your address', 403, 'ADDRESS_NOT_VERIFIED');
  }
  next();
});

// Check if user is emergency responder
const requireEmergencyResponder = asyncHandler(async (req, res, next) => {
  if (!req.user.is_emergency_responder) {
    throw new AppError('Emergency responder access required', 403, 'NOT_EMERGENCY_RESPONDER');
  }
  next();
});

// Check if user has specific role
const requireRole = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      throw new AppError('Insufficient permissions', 403, 'INSUFFICIENT_PERMISSIONS');
    }
    next();
  };
};

// Optional authentication - attach user if token exists but don't require it
const optionalAuth = asyncHandler(async (req, res, next) => {
  let token;

  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  if (!token && req.cookies?.token) {
    token = req.cookies.token;
  }

  if (token) {
    try {
      const decoded = verifyToken(token);
      const session = await sessions.get(decoded.id);
      
      if (session && session.token === token) {
        const result = await query(
          `SELECT id, email, first_name, last_name, role, status, neighborhood_id, 
                  is_verified, is_emergency_responder
           FROM users 
           WHERE id = $1 AND status = 'active'`,
          [decoded.id]
        );

        if (result.rows.length > 0) {
          req.user = result.rows[0];
          req.token = token;
        }
      }
    } catch (error) {
      // Ignore errors for optional auth
    }
  }

  next();
});

// Rate limiting by user (in addition to IP-based rate limiting)
const userRateLimit = (limit = 100, windowMinutes = 15) => {
  return asyncHandler(async (req, res, next) => {
    if (!req.user) {
      return next();
    }

    const { rateLimiter } = require('../config/redis');
    const identifier = `user:${req.user.id}:${req.path}`;
    const windowSeconds = windowMinutes * 60;

    const { allowed, remaining, resetAt } = await rateLimiter.check(
      identifier,
      limit,
      windowSeconds
    );

    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', limit);
    res.setHeader('X-RateLimit-Remaining', remaining);
    res.setHeader('X-RateLimit-Reset', new Date(resetAt).toISOString());

    if (!allowed) {
      throw new AppError('Rate limit exceeded', 429, 'RATE_LIMIT_EXCEEDED');
    }

    next();
  });
};

// Check resource ownership
const checkOwnership = (resourceType) => {
  return asyncHandler(async (req, res, next) => {
    const resourceId = req.params.id;
    const userId = req.user.id;
    let isOwner = false;

    switch (resourceType) {
      case 'post':
        const postResult = await query(
          'SELECT user_id FROM posts WHERE id = $1',
          [resourceId]
        );
        isOwner = postResult.rows[0]?.user_id === userId;
        break;

      case 'event':
        const eventResult = await query(
          'SELECT organizer_id FROM events WHERE id = $1',
          [resourceId]
        );
        isOwner = eventResult.rows[0]?.organizer_id === userId;
        break;

      case 'exchange_item':
        const itemResult = await query(
          'SELECT user_id FROM exchange_items WHERE id = $1',
          [resourceId]
        );
        isOwner = itemResult.rows[0]?.user_id === userId;
        break;

      default:
        throw new AppError('Invalid resource type', 400);
    }

    if (!isOwner && req.user.role !== 'admin') {
      throw new AppError('Not authorized to modify this resource', 403, 'NOT_OWNER');
    }

    next();
  });
};

module.exports = {
  protect,
  requireVerified,
  requireAddress,
  requireEmergencyResponder,
  requireRole,
  optionalAuth,
  userRateLimit,
  checkOwnership
};