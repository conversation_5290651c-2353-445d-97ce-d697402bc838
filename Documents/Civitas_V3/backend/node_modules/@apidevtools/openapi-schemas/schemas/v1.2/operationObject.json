{"id": "https://raw.githubusercontent.com/OAI/OpenAPI-Specification/master/schemas/v1.2/operationObject.json#", "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "allOf": [{"$ref": "dataTypeBase.json#"}, {"required": ["method", "nickname", "parameters"], "properties": {"method": {"enum": ["GET", "HEAD", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"]}, "summary": {"type": "string", "maxLength": 120}, "notes": {"type": "string"}, "nickname": {"type": "string", "pattern": "^[a-zA-Z0-9_]+$"}, "authorizations": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "authorizationObject.json#/definitions/oauth2Scope"}}}, "parameters": {"type": "array", "items": {"$ref": "parameterObject.json#"}}, "responseMessages": {"type": "array", "items": {"$ref": "#/definitions/responseMessageObject"}}, "produces": {"$ref": "#/definitions/mimeTypeArray"}, "consumes": {"$ref": "#/definitions/mimeTypeArray"}, "deprecated": {"enum": ["true", "false"]}}}], "definitions": {"responseMessageObject": {"type": "object", "required": ["code", "message"], "properties": {"code": {"$ref": "#/definitions/rfc2616section10"}, "message": {"type": "string"}, "responseModel": {"type": "string"}}}, "rfc2616section10": {"type": "integer", "minimum": 100, "maximum": 600, "exclusiveMaximum": true}, "mimeTypeArray": {"type": "array", "items": {"type": "string", "format": "mime-type"}, "uniqueItems": true}}}