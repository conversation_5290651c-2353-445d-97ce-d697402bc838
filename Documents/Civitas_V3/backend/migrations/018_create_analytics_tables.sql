-- Analytics System Tables

-- Analytics events for tracking user actions
CREATE TABLE IF NOT EXISTS analytics_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    neighborhood_id UUID REFERENCES neighborhoods(id),
    event_type VARCHAR(100) NOT NULL,
    event_category VARCHAR(50),
    event_action VARCHAR(100),
    event_label VARCHAR(255),
    event_value NUMERIC,
    event_data JSONB DEFAULT '{}',
    session_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Community metrics aggregated daily
CREATE TABLE IF NOT EXISTS community_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    metric_date DATE NOT NULL,
    metrics JSONB NOT NULL DEFAULT '{}',
    -- Specific metric columns for indexing
    total_users INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    new_users INTEGER DEFAULT 0,
    total_posts INTEGER DEFAULT 0,
    total_events INTEGER DEFAULT 0,
    total_exchanges INTEGER DEFAULT 0,
    engagement_rate NUMERIC(5,2),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(neighborhood_id, metric_date)
);

-- Content/user reporting system
CREATE TABLE IF NOT EXISTS reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    reporter_id UUID NOT NULL REFERENCES users(id),
    reported_type VARCHAR(50) NOT NULL CHECK (reported_type IN ('user', 'post', 'event', 'listing', 'comment', 'message')),
    reported_id UUID NOT NULL,
    reason VARCHAR(50) NOT NULL CHECK (reason IN ('spam', 'harassment', 'hate_speech', 'violence', 'inappropriate_content', 'false_information', 'scam', 'other')),
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'investigating', 'resolved', 'dismissed')),
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Actions taken on reports
CREATE TABLE IF NOT EXISTS report_actions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    report_id UUID NOT NULL REFERENCES reports(id) ON DELETE CASCADE,
    moderator_id UUID NOT NULL REFERENCES users(id),
    action_type VARCHAR(50) CHECK (action_type IN ('warning', 'content_removed', 'user_suspended', 'user_banned', 'no_action')),
    resolution TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Environmental impact tracking
CREATE TABLE IF NOT EXISTS environmental_impact (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    date DATE NOT NULL,
    exchanges_count INTEGER DEFAULT 0,
    items_reused INTEGER DEFAULT 0,
    estimated_weight_kg NUMERIC(10,2) DEFAULT 0,
    co2_saved_kg NUMERIC(10,2) DEFAULT 0,
    water_saved_liters NUMERIC(10,2) DEFAULT 0,
    landfill_diverted_kg NUMERIC(10,2) DEFAULT 0,
    carpool_trips INTEGER DEFAULT 0,
    carpool_people INTEGER DEFAULT 0,
    carpool_distance_km NUMERIC(10,2) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(neighborhood_id, date)
);

-- User activity summaries for analytics
CREATE TABLE IF NOT EXISTS user_activity_summaries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    summary_date DATE NOT NULL,
    posts_created INTEGER DEFAULT 0,
    comments_made INTEGER DEFAULT 0,
    likes_given INTEGER DEFAULT 0,
    events_created INTEGER DEFAULT 0,
    events_attended INTEGER DEFAULT 0,
    exchanges_created INTEGER DEFAULT 0,
    exchanges_completed INTEGER DEFAULT 0,
    messages_sent INTEGER DEFAULT 0,
    help_requests INTEGER DEFAULT 0,
    help_responses INTEGER DEFAULT 0,
    points_earned INTEGER DEFAULT 0,
    engagement_score NUMERIC(5,2),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, summary_date)
);

-- Content performance tracking
CREATE TABLE IF NOT EXISTS content_performance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_type VARCHAR(50) NOT NULL CHECK (content_type IN ('post', 'event', 'listing')),
    content_id UUID NOT NULL,
    view_count INTEGER DEFAULT 0,
    unique_views INTEGER DEFAULT 0,
    engagement_count INTEGER DEFAULT 0,
    share_count INTEGER DEFAULT 0,
    click_through_rate NUMERIC(5,2),
    average_view_time INTEGER, -- in seconds
    last_viewed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(content_type, content_id)
);

-- Funnel tracking for conversion analytics
CREATE TABLE IF NOT EXISTS funnel_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    funnel_name VARCHAR(100) NOT NULL,
    step_name VARCHAR(100) NOT NULL,
    step_number INTEGER NOT NULL,
    session_id VARCHAR(255),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Dashboard configuration for admins
CREATE TABLE IF NOT EXISTS analytics_dashboards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    config JSONB NOT NULL DEFAULT '{}',
    is_default BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX idx_analytics_events_event_type ON analytics_events(event_type);
CREATE INDEX idx_analytics_events_created_at ON analytics_events(created_at);
CREATE INDEX idx_analytics_events_neighborhood_id ON analytics_events(neighborhood_id);

CREATE INDEX idx_community_metrics_neighborhood_date ON community_metrics(neighborhood_id, metric_date DESC);
CREATE INDEX idx_community_metrics_date ON community_metrics(metric_date DESC);

CREATE INDEX idx_reports_reporter_id ON reports(reporter_id);
CREATE INDEX idx_reports_status ON reports(status) WHERE status IN ('pending', 'investigating');
CREATE INDEX idx_reports_reported ON reports(reported_type, reported_id);
CREATE INDEX idx_reports_created_at ON reports(created_at DESC);

CREATE INDEX idx_environmental_impact_neighborhood_date ON environmental_impact(neighborhood_id, date DESC);

CREATE INDEX idx_user_activity_summaries_user_date ON user_activity_summaries(user_id, summary_date DESC);
CREATE INDEX idx_user_activity_summaries_date ON user_activity_summaries(summary_date DESC);

CREATE INDEX idx_content_performance_content ON content_performance(content_type, content_id);
CREATE INDEX idx_content_performance_views ON content_performance(view_count DESC);

CREATE INDEX idx_funnel_events_user_funnel ON funnel_events(user_id, funnel_name);
CREATE INDEX idx_funnel_events_session ON funnel_events(session_id);

-- Triggers
CREATE OR REPLACE FUNCTION update_report_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_report_timestamp
    BEFORE UPDATE ON reports
    FOR EACH ROW
    EXECUTE FUNCTION update_report_timestamp();

-- Function to calculate community health score
CREATE OR REPLACE FUNCTION calculate_community_health_score(p_neighborhood_id UUID, p_days INTEGER DEFAULT 30)
RETURNS TABLE(
    overall_score NUMERIC,
    engagement_score NUMERIC,
    safety_score NUMERIC,
    environmental_score NUMERIC,
    activity_score NUMERIC,
    grade VARCHAR(2)
) AS $$
DECLARE
    v_engagement NUMERIC;
    v_safety NUMERIC;
    v_environmental NUMERIC;
    v_activity NUMERIC;
    v_overall NUMERIC;
    v_grade VARCHAR(2);
BEGIN
    -- Calculate engagement score (0-100)
    SELECT 
        LEAST(100, (
            (COUNT(DISTINCT uas.user_id) * 100.0 / NULLIF(COUNT(DISTINCT u.id), 0)) * 0.4 +
            (AVG(uas.engagement_score) * 0.6)
        ))
    INTO v_engagement
    FROM users u
    LEFT JOIN user_activity_summaries uas ON u.id = uas.user_id 
        AND uas.summary_date >= CURRENT_DATE - INTERVAL '1 day' * p_days
    WHERE u.neighborhood_id = p_neighborhood_id;

    -- Calculate safety score (0-100)
    SELECT 
        GREATEST(0, 100 - (COUNT(*) * 5)) -- Deduct 5 points per unresolved emergency
    INTO v_safety
    FROM emergency_alerts ea
    WHERE ea.neighborhood_id = p_neighborhood_id
    AND ea.status IN ('active', 'responding')
    AND ea.created_at >= CURRENT_DATE - INTERVAL '1 day' * p_days;

    -- Calculate environmental score (0-100)
    SELECT 
        LEAST(100, (
            (SUM(ei.exchanges_count) * 0.5) +
            (SUM(ei.carpool_trips) * 0.3) +
            (SUM(ei.co2_saved_kg) * 0.2)
        ))
    INTO v_environmental
    FROM environmental_impact ei
    WHERE ei.neighborhood_id = p_neighborhood_id
    AND ei.date >= CURRENT_DATE - INTERVAL '1 day' * p_days;

    -- Calculate activity score (0-100)
    SELECT 
        LEAST(100, (
            COUNT(DISTINCT p.id) * 0.3 +
            COUNT(DISTINCT e.id) * 0.3 +
            COUNT(DISTINCT el.id) * 0.2 +
            COUNT(DISTINCT ct.id) * 0.2
        ))
    INTO v_activity
    FROM posts p
    FULL OUTER JOIN events e ON TRUE
    FULL OUTER JOIN exchange_listings el ON TRUE
    FULL OUTER JOIN community_topics ct ON TRUE
    WHERE p.neighborhood_id = p_neighborhood_id
    OR e.neighborhood_id = p_neighborhood_id
    OR el.neighborhood_id = p_neighborhood_id
    OR ct.neighborhood_id = p_neighborhood_id;

    -- Calculate overall score
    v_overall := (
        COALESCE(v_engagement, 50) * 0.3 +
        COALESCE(v_safety, 90) * 0.3 +
        COALESCE(v_environmental, 50) * 0.2 +
        COALESCE(v_activity, 50) * 0.2
    );

    -- Assign grade
    v_grade := CASE
        WHEN v_overall >= 90 THEN 'A+'
        WHEN v_overall >= 85 THEN 'A'
        WHEN v_overall >= 80 THEN 'A-'
        WHEN v_overall >= 75 THEN 'B+'
        WHEN v_overall >= 70 THEN 'B'
        WHEN v_overall >= 65 THEN 'B-'
        WHEN v_overall >= 60 THEN 'C+'
        WHEN v_overall >= 55 THEN 'C'
        WHEN v_overall >= 50 THEN 'C-'
        WHEN v_overall >= 45 THEN 'D'
        ELSE 'F'
    END;

    RETURN QUERY
    SELECT 
        ROUND(v_overall, 2),
        ROUND(COALESCE(v_engagement, 50), 2),
        ROUND(COALESCE(v_safety, 90), 2),
        ROUND(COALESCE(v_environmental, 50), 2),
        ROUND(COALESCE(v_activity, 50), 2),
        v_grade;
END;
$$ LANGUAGE plpgsql;

-- Function to track analytics event
CREATE OR REPLACE FUNCTION track_analytics_event(
    p_user_id UUID,
    p_event_type VARCHAR(100),
    p_event_data JSONB DEFAULT '{}'
) RETURNS UUID AS $$
DECLARE
    v_event_id UUID;
BEGIN
    INSERT INTO analytics_events (
        user_id,
        neighborhood_id,
        event_type,
        event_category,
        event_action,
        event_label,
        event_data
    ) VALUES (
        p_user_id,
        (SELECT neighborhood_id FROM users WHERE id = p_user_id),
        p_event_type,
        p_event_data->>'category',
        p_event_data->>'action',
        p_event_data->>'label',
        p_event_data
    ) RETURNING id INTO v_event_id;
    
    RETURN v_event_id;
END;
$$ LANGUAGE plpgsql;

-- Comments
COMMENT ON TABLE analytics_events IS 'Tracks all user actions for analytics';
COMMENT ON TABLE community_metrics IS 'Daily aggregated metrics per neighborhood';
COMMENT ON TABLE reports IS 'Content and user reporting system';
COMMENT ON TABLE report_actions IS 'Moderation actions taken on reports';
COMMENT ON TABLE environmental_impact IS 'Environmental impact metrics';
COMMENT ON TABLE user_activity_summaries IS 'Daily user activity rollups';
COMMENT ON TABLE content_performance IS 'Performance metrics for content';
COMMENT ON TABLE funnel_events IS 'Conversion funnel tracking';
COMMENT ON TABLE analytics_dashboards IS 'Custom dashboard configurations';