-- Civitas Database Setup
-- This script creates the database and enables necessary extensions

-- Create database (run this as superuser)
-- CREATE DATABASE civitas;

-- Connect to civitas database before running the rest
-- \c civitas;

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";      -- For UUID generation
CREATE EXTENSION IF NOT EXISTS "postgis";        -- For geographic data
CREATE EXTENSION IF NOT EXISTS "pg_trgm";        -- For text search
CREATE EXTENSION IF NOT EXISTS "btree_gist";     -- For exclusion constraints
CREATE EXTENSION IF NOT EXISTS "citext";         -- For case-insensitive text

-- Create custom types
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('user', 'moderator', 'admin');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE subscription_type AS ENUM ('free', 'premium', 'business');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE emergency_status AS ENUM ('active', 'responding', 'resolved', 'cancelled', 'test');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE notification_type AS ENUM (
        'post_like', 'post_comment', 'post_share',
        'follow', 'message', 'emergency',
        'event_invite', 'event_reminder', 'event_update',
        'help_request', 'help_offer', 'civic_update',
        'system', 'achievement', 'daily_kindness'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE listing_type AS ENUM ('give_away', 'lend', 'request');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE listing_status AS ENUM ('available', 'pending', 'completed', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create helper functions

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to generate random color
CREATE OR REPLACE FUNCTION generate_random_color()
RETURNS VARCHAR AS $$
BEGIN
    RETURN '#' || LPAD(TO_HEX(FLOOR(RANDOM() * 16777215)::INT), 6, '0');
END;
$$ LANGUAGE plpgsql;

-- Function to calculate distance between two points
CREATE OR REPLACE FUNCTION calculate_distance(lat1 FLOAT, lon1 FLOAT, lat2 FLOAT, lon2 FLOAT)
RETURNS FLOAT AS $$
DECLARE
    R FLOAT := 6371; -- Earth's radius in kilometers
    dLat FLOAT;
    dLon FLOAT;
    a FLOAT;
    c FLOAT;
BEGIN
    dLat := RADIANS(lat2 - lat1);
    dLon := RADIANS(lon2 - lon1);
    a := SIN(dLat/2) * SIN(dLat/2) + COS(RADIANS(lat1)) * COS(RADIANS(lat2)) * SIN(dLon/2) * SIN(dLon/2);
    c := 2 * ATAN2(SQRT(a), SQRT(1-a));
    RETURN R * c;
END;
$$ LANGUAGE plpgsql;

-- Create base schema for audit fields
COMMENT ON EXTENSION "uuid-ossp" IS 'Provides UUID generation functions';
COMMENT ON EXTENSION "postgis" IS 'Provides geographic and location-based functionality';
COMMENT ON EXTENSION "pg_trgm" IS 'Provides text similarity search';
COMMENT ON EXTENSION "btree_gist" IS 'Provides GiST index support for btree operators';
COMMENT ON EXTENSION "citext" IS 'Provides case-insensitive character string type';

-- Set default search path
SET search_path TO public;

-- Configure database settings
ALTER DATABASE civitas SET timezone TO 'UTC';

-- Create schema for app-specific functions if needed
CREATE SCHEMA IF NOT EXISTS civitas_app;

-- Grant permissions (adjust as needed)
-- GRANT ALL PRIVILEGES ON DATABASE civitas TO civitas_user;
-- GRANT ALL PRIVILEGES ON SCHEMA public TO civitas_user;
-- GRANT ALL PRIVILEGES ON SCHEMA civitas_app TO civitas_user;

-- Performance settings (adjust based on your server)
-- ALTER DATABASE civitas SET shared_buffers = '256MB';
-- ALTER DATABASE civitas SET effective_cache_size = '1GB';
-- ALTER DATABASE civitas SET work_mem = '4MB';
-- ALTER DATABASE civitas SET maintenance_work_mem = '64MB';

COMMENT ON DATABASE civitas IS 'Civitas - Where Neighbors Save Lives';