# Civitas Database Migrations

This directory contains all database migrations for the Civitas backend. The migrations create the complete database schema including tables, indexes, triggers, functions, and initial data.

## Migration Files

The migrations are numbered to ensure they run in the correct order:

- `000_create_database.sql` - Database setup and extensions
- `001_create_core_tables.sql` - Core tables (users, neighborhoods, etc.)
- `002_create_posts_tables.sql` - Posts and social interaction tables
- `003_create_messaging_tables.sql` - Direct and group messaging
- `004_create_notifications_tables.sql` - Notification system
- `005_create_emergency_tables.sql` - Emergency alert system
- `006_create_events_tables.sql` - Events and RSVP system
- `007_create_exchange_tables.sql` - Marketplace and skills exchange
- `008_create_search_tables.sql` - Search history and analytics
- `015_create_media_tables.sql` - Media storage and processing
- `016_create_community_tables.sql` - Forums and community features
- `017_create_gamification_tables.sql` - Points, badges, and achievements
- `018_create_external_tables.sql` - External integrations
- `999_create_indexes_and_triggers.sql` - Performance optimizations

## Setup Methods

### Method 1: Using the Setup Script (Recommended)

```bash
# From the backend directory
./setup-database.sh

# With custom options
./setup-database.sh --host localhost --port 5432 --database civitas --user postgres --password mypassword

# To reset and recreate the database
./setup-database.sh --reset
```

### Method 2: Using PostgreSQL Client

```bash
# Create the database first
createdb civitas

# Run all migrations
psql -U postgres -d civitas -f migrations/run-all-migrations.sql
```

### Method 3: Running Individual Migrations

```bash
# Run migrations one by one
psql -U postgres -d civitas -f migrations/000_create_database.sql
psql -U postgres -d civitas -f migrations/001_create_core_tables.sql
# ... continue for each file
```

## Environment Variables

Set these in your `.env` file:

```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=civitas
DB_USER=postgres
DB_PASSWORD=your_password
```

## Required PostgreSQL Extensions

The following extensions are required and will be installed by the migrations:
- `uuid-ossp` - UUID generation
- `postgis` - Geographic data support
- `pg_trgm` - Trigram text search
- `btree_gist` - GiST index support
- `citext` - Case-insensitive text

## Migration History

The setup tracks which migrations have been run in the `migration_history` table. This prevents running the same migration twice and allows you to add new migrations later.

## Adding New Migrations

To add a new migration:

1. Create a new SQL file with the next number in sequence
2. Include appropriate indexes and foreign keys
3. Add comments to document the tables
4. Update the `run-all-migrations.sql` file to include your migration
5. Test the migration on a fresh database

## Troubleshooting

### Connection Issues
- Ensure PostgreSQL is running: `pg_ctl status` or `systemctl status postgresql`
- Check your connection parameters in the `.env` file
- Verify the user has CREATE DATABASE privileges

### Migration Failures
- Check the PostgreSQL logs for detailed error messages
- Ensure you're running PostgreSQL 12 or higher
- Verify all required extensions are available
- Run migrations in order - later migrations depend on earlier ones

### Performance
- The full migration set creates ~100+ tables and many indexes
- Initial setup may take 30-60 seconds
- Consider using `--reset` flag during development to start fresh

## Database Schema Overview

The database is organized into several systems:

1. **Core System**: Users, neighborhoods, relationships
2. **Social System**: Posts, comments, likes, shares
3. **Messaging System**: Direct messages, group chats
4. **Notification System**: Push, email, SMS notifications
5. **Emergency System**: Alerts, responders, coordination
6. **Events System**: Community events, RSVPs
7. **Exchange System**: Marketplace, skills, services
8. **Search System**: Search history, suggestions
9. **Media System**: File uploads, processing
10. **Community System**: Forums, polls, resources
11. **Gamification System**: Points, badges, leaderboards
12. **External System**: Third-party integrations

Each system is designed to work independently while maintaining referential integrity through foreign keys.