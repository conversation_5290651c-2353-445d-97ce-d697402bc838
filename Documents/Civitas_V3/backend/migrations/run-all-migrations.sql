-- Civitas Complete Database Setup
-- Run this file to create all tables, indexes, and functions
-- 
-- Usage: psql -U postgres -d civitas -f run-all-migrations.sql

-- Start transaction
BEGIN;

-- Migration history tracking
CREATE TABLE IF NOT EXISTS migration_history (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) UNIQUE NOT NULL,
    executed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Include all migrations in order
\echo 'Running 000_create_database.sql...'
\i 000_create_database.sql
INSERT INTO migration_history (filename) VALUES ('000_create_database.sql') ON CONFLICT (filename) DO NOTHING;

\echo 'Running 001_create_core_tables.sql...'
\i 001_create_core_tables.sql
INSERT INTO migration_history (filename) VALUES ('001_create_core_tables.sql') ON CONFLICT (filename) DO NOTHING;

\echo 'Running 002_create_posts_tables.sql...'
\i 002_create_posts_tables.sql
INSERT INTO migration_history (filename) VALUES ('002_create_posts_tables.sql') ON CONFLICT (filename) DO NOTHING;

\echo 'Running 003_create_messaging_tables.sql...'
\i 003_create_messaging_tables.sql
INSERT INTO migration_history (filename) VALUES ('003_create_messaging_tables.sql') ON CONFLICT (filename) DO NOTHING;

\echo 'Running 004_create_notifications_tables.sql...'
\i 004_create_notifications_tables.sql
INSERT INTO migration_history (filename) VALUES ('004_create_notifications_tables.sql') ON CONFLICT (filename) DO NOTHING;

\echo 'Running 005_create_emergency_tables.sql...'
\i 005_create_emergency_tables.sql
INSERT INTO migration_history (filename) VALUES ('005_create_emergency_tables.sql') ON CONFLICT (filename) DO NOTHING;

\echo 'Running 006_create_events_tables.sql...'
\i 006_create_events_tables.sql
INSERT INTO migration_history (filename) VALUES ('006_create_events_tables.sql') ON CONFLICT (filename) DO NOTHING;

\echo 'Running 007_create_exchange_tables.sql...'
\i 007_create_exchange_tables.sql
INSERT INTO migration_history (filename) VALUES ('007_create_exchange_tables.sql') ON CONFLICT (filename) DO NOTHING;

\echo 'Running 008_create_search_tables.sql...'
\i 008_create_search_tables.sql
INSERT INTO migration_history (filename) VALUES ('008_create_search_tables.sql') ON CONFLICT (filename) DO NOTHING;

\echo 'Running 015_create_media_tables.sql...'
\i 015_create_media_tables.sql
INSERT INTO migration_history (filename) VALUES ('015_create_media_tables.sql') ON CONFLICT (filename) DO NOTHING;

\echo 'Running 016_create_community_tables.sql...'
\i 016_create_community_tables.sql
INSERT INTO migration_history (filename) VALUES ('016_create_community_tables.sql') ON CONFLICT (filename) DO NOTHING;

\echo 'Running 017_create_gamification_tables.sql...'
\i 017_create_gamification_tables.sql
INSERT INTO migration_history (filename) VALUES ('017_create_gamification_tables.sql') ON CONFLICT (filename) DO NOTHING;

\echo 'Running 018_create_external_tables.sql...'
\i 018_create_external_tables.sql
INSERT INTO migration_history (filename) VALUES ('018_create_external_tables.sql') ON CONFLICT (filename) DO NOTHING;

\echo 'Running 999_create_indexes_and_triggers.sql...'
\i 999_create_indexes_and_triggers.sql
INSERT INTO migration_history (filename) VALUES ('999_create_indexes_and_triggers.sql') ON CONFLICT (filename) DO NOTHING;

-- Commit transaction
COMMIT;

-- Display summary
\echo ''
\echo 'Database setup complete!'
\echo ''
\echo 'Tables created:'
SELECT COUNT(*) as table_count 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE';

\echo ''
\echo 'Indexes created:'
SELECT COUNT(*) as index_count 
FROM pg_indexes 
WHERE schemaname = 'public';

\echo ''
\echo 'Functions created:'
SELECT COUNT(*) as function_count 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_type = 'FUNCTION';

\echo ''
\echo 'Recent migrations:'
SELECT filename, executed_at 
FROM migration_history 
ORDER BY executed_at DESC 
LIMIT 10;