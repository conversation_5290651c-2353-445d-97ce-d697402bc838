-- Community forums table
CREATE TABLE IF NOT EXISTS community_forums (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    slug VARCHAR(100) NOT NULL,
    icon VARCHAR(50),
    color VARCHAR(7), -- hex color
    position INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    is_moderated BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(neighborhood_id, slug)
);

-- Community topics/threads
CREATE TABLE IF NOT EXISTS community_topics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    forum_id UUID NOT NULL REFERENCES community_forums(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    tags TEXT[] DEFAULT '{}',
    is_pinned BOOLEAN DEFAULT FALSE,
    is_locked BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    view_count INTEGER DEFAULT 0,
    reply_count INTEGER DEFAULT 0,
    last_reply_at TIMESTAMPTZ,
    last_reply_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Topic replies
CREATE TABLE IF NOT EXISTS community_replies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    topic_id UUID NOT NULL REFERENCES community_topics(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    parent_reply_id UUID REFERENCES community_replies(id),
    content TEXT NOT NULL,
    is_solution BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Community polls
CREATE TABLE IF NOT EXISTS community_polls (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    created_by UUID NOT NULL REFERENCES users(id),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    poll_type VARCHAR(50) DEFAULT 'single_choice', -- single_choice, multiple_choice, ranking
    options JSONB NOT NULL, -- [{id, text, color}]
    settings JSONB DEFAULT '{}', -- {anonymous, showResults, allowComments, etc}
    start_date TIMESTAMPTZ DEFAULT NOW(),
    end_date TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    total_votes INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Poll votes
CREATE TABLE IF NOT EXISTS community_poll_votes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    poll_id UUID NOT NULL REFERENCES community_polls(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    option_ids UUID[] NOT NULL, -- Array for multiple choice
    comment TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(poll_id, user_id)
);

-- Community announcements
CREATE TABLE IF NOT EXISTS community_announcements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    created_by UUID NOT NULL REFERENCES users(id),
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    announcement_type VARCHAR(50) DEFAULT 'general', -- general, urgent, event, maintenance
    priority VARCHAR(20) DEFAULT 'normal', -- low, normal, high, urgent
    target_audience VARCHAR(50) DEFAULT 'all', -- all, residents, businesses, etc
    is_pinned BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMPTZ,
    media_urls TEXT[] DEFAULT '{}',
    view_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Community resources
CREATE TABLE IF NOT EXISTS community_resources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    created_by UUID NOT NULL REFERENCES users(id),
    category VARCHAR(100) NOT NULL, -- emergency, health, education, government, etc
    name VARCHAR(200) NOT NULL,
    description TEXT,
    contact_info JSONB, -- {phone, email, website, address}
    hours_of_operation JSONB, -- {monday: {open, close}, ...}
    tags TEXT[] DEFAULT '{}',
    is_verified BOOLEAN DEFAULT FALSE,
    verified_by UUID REFERENCES users(id),
    verified_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Community initiatives/projects
CREATE TABLE IF NOT EXISTS community_initiatives (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    created_by UUID NOT NULL REFERENCES users(id),
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100), -- environment, safety, beautification, social, etc
    goals TEXT[] DEFAULT '{}',
    start_date DATE,
    end_date DATE,
    status VARCHAR(50) DEFAULT 'planning', -- planning, active, completed, cancelled
    location_name VARCHAR(200),
    location geography(POINT, 4326),
    volunteers_needed INTEGER DEFAULT 0,
    volunteers_count INTEGER DEFAULT 0,
    budget DECIMAL(10, 2),
    media_urls TEXT[] DEFAULT '{}',
    contact_info JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Initiative volunteers
CREATE TABLE IF NOT EXISTS community_initiative_volunteers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    initiative_id UUID NOT NULL REFERENCES community_initiatives(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    role VARCHAR(100),
    hours_committed DECIMAL(5, 2),
    status VARCHAR(50) DEFAULT 'active', -- active, completed, withdrawn
    joined_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(initiative_id, user_id)
);

-- Local services directory
CREATE TABLE IF NOT EXISTS community_services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    submitted_by UUID NOT NULL REFERENCES users(id),
    business_name VARCHAR(200) NOT NULL,
    category VARCHAR(100) NOT NULL, -- plumber, electrician, contractor, etc
    description TEXT,
    contact_info JSONB NOT NULL, -- {name, phone, email, website}
    address VARCHAR(500),
    location geography(POINT, 4326),
    service_area_radius INTEGER DEFAULT 5, -- miles
    license_number VARCHAR(100),
    insurance_info TEXT,
    years_in_business INTEGER,
    specialties TEXT[] DEFAULT '{}',
    average_rating DECIMAL(3, 2) DEFAULT 0,
    total_ratings INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT FALSE,
    verified_by UUID REFERENCES users(id),
    verified_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Service ratings
CREATE TABLE IF NOT EXISTS community_service_ratings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_id UUID NOT NULL REFERENCES community_services(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    would_recommend BOOLEAN,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(service_id, user_id)
);

-- Community guidelines/rules
CREATE TABLE IF NOT EXISTS community_guidelines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    created_by UUID NOT NULL REFERENCES users(id),
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(50), -- general, safety, events, exchange, etc
    position INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_community_forums_neighborhood ON community_forums(neighborhood_id);
CREATE INDEX idx_community_forums_slug ON community_forums(slug);

CREATE INDEX idx_community_topics_forum ON community_topics(forum_id);
CREATE INDEX idx_community_topics_user ON community_topics(user_id);
CREATE INDEX idx_community_topics_created ON community_topics(created_at DESC);
CREATE INDEX idx_community_topics_last_reply ON community_topics(last_reply_at DESC);

CREATE INDEX idx_community_replies_topic ON community_replies(topic_id);
CREATE INDEX idx_community_replies_user ON community_replies(user_id);
CREATE INDEX idx_community_replies_parent ON community_replies(parent_reply_id);

CREATE INDEX idx_community_polls_neighborhood ON community_polls(neighborhood_id);
CREATE INDEX idx_community_polls_active ON community_polls(is_active, end_date);

CREATE INDEX idx_community_announcements_neighborhood ON community_announcements(neighborhood_id);
CREATE INDEX idx_community_announcements_active ON community_announcements(is_active, expires_at);
CREATE INDEX idx_community_announcements_priority ON community_announcements(priority);

CREATE INDEX idx_community_resources_neighborhood ON community_resources(neighborhood_id);
CREATE INDEX idx_community_resources_category ON community_resources(category);

CREATE INDEX idx_community_initiatives_neighborhood ON community_initiatives(neighborhood_id);
CREATE INDEX idx_community_initiatives_status ON community_initiatives(status);

CREATE INDEX idx_community_services_neighborhood ON community_services(neighborhood_id);
CREATE INDEX idx_community_services_category ON community_services(category);
CREATE INDEX idx_community_services_location ON community_services USING GIST(location);

-- Triggers for updating counts
CREATE OR REPLACE FUNCTION update_topic_reply_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE community_topics 
        SET reply_count = reply_count + 1,
            last_reply_at = NEW.created_at,
            last_reply_by = NEW.user_id,
            updated_at = NOW()
        WHERE id = NEW.topic_id;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE community_topics 
        SET reply_count = GREATEST(reply_count - 1, 0),
            updated_at = NOW()
        WHERE id = OLD.topic_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_topic_reply_count
AFTER INSERT OR DELETE ON community_replies
FOR EACH ROW
EXECUTE FUNCTION update_topic_reply_count();

-- Trigger for updating service ratings
CREATE OR REPLACE FUNCTION update_service_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE community_services 
    SET average_rating = (
        SELECT AVG(rating)::DECIMAL(3,2) 
        FROM community_service_ratings 
        WHERE service_id = NEW.service_id
    ),
    total_ratings = (
        SELECT COUNT(*) 
        FROM community_service_ratings 
        WHERE service_id = NEW.service_id
    )
    WHERE id = NEW.service_id;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_service_rating
AFTER INSERT OR UPDATE OR DELETE ON community_service_ratings
FOR EACH ROW
EXECUTE FUNCTION update_service_rating();