-- Media files table
CREATE TABLE IF NOT EXISTS media (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    file_url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    file_type VARCHAR(50) NOT NULL, -- image, video, audio, document
    mime_type VARCHAR(100) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL, -- in bytes
    width INTEGER, -- for images/videos
    height INTEGER, -- for images/videos
    duration INTEGER, -- for videos/audio in seconds
    metadata JSONB DEFAULT '{}', -- additional metadata
    upload_status VARCHAR(50) DEFAULT 'pending', -- pending, processing, completed, failed
    moderation_status VARCHAR(50) DEFAULT 'pending', -- pending, approved, rejected, flagged
    moderation_notes TEXT,
    moderated_by UUID REFERENCES users(id),
    moderated_at TIMESTAMPTZ,
    is_deleted BO<PERSON>EA<PERSON> DEFAULT FALSE,
    deleted_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Media albums/collections
CREATE TABLE IF NOT EXISTS media_albums (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    cover_media_id UUID REFERENCES media(id),
    is_public BOOLEAN DEFAULT TRUE,
    media_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Album items
CREATE TABLE IF NOT EXISTS media_album_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    album_id UUID NOT NULL REFERENCES media_albums(id) ON DELETE CASCADE,
    media_id UUID NOT NULL REFERENCES media(id) ON DELETE CASCADE,
    position INTEGER DEFAULT 0,
    caption TEXT,
    added_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(album_id, media_id)
);

-- Media usage/references tracking
CREATE TABLE IF NOT EXISTS media_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    media_id UUID NOT NULL REFERENCES media(id) ON DELETE CASCADE,
    entity_type VARCHAR(50) NOT NULL, -- post, message, event, profile, etc.
    entity_id UUID NOT NULL,
    usage_type VARCHAR(50), -- attachment, cover_photo, profile_photo, etc.
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Media moderation queue
CREATE TABLE IF NOT EXISTS media_moderation_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    media_id UUID NOT NULL REFERENCES media(id) ON DELETE CASCADE,
    reported_by UUID REFERENCES users(id),
    reason VARCHAR(100),
    notes TEXT,
    priority VARCHAR(20) DEFAULT 'normal', -- low, normal, high, urgent
    status VARCHAR(50) DEFAULT 'pending', -- pending, reviewing, resolved
    resolved_by UUID REFERENCES users(id),
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Media processing jobs
CREATE TABLE IF NOT EXISTS media_processing_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    media_id UUID NOT NULL REFERENCES media(id) ON DELETE CASCADE,
    job_type VARCHAR(50) NOT NULL, -- thumbnail, compress, transcode, etc.
    status VARCHAR(50) DEFAULT 'queued', -- queued, processing, completed, failed
    priority INTEGER DEFAULT 5, -- 1-10, higher is more priority
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    error_message TEXT,
    result JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_media_user_id ON media(user_id);
CREATE INDEX idx_media_neighborhood_id ON media(neighborhood_id);
CREATE INDEX idx_media_file_type ON media(file_type);
CREATE INDEX idx_media_upload_status ON media(upload_status);
CREATE INDEX idx_media_moderation_status ON media(moderation_status);
CREATE INDEX idx_media_created_at ON media(created_at DESC);

CREATE INDEX idx_media_albums_user_id ON media_albums(user_id);
CREATE INDEX idx_media_albums_neighborhood_id ON media_albums(neighborhood_id);

CREATE INDEX idx_media_album_items_album_id ON media_album_items(album_id);
CREATE INDEX idx_media_album_items_media_id ON media_album_items(media_id);

CREATE INDEX idx_media_usage_media_id ON media_usage(media_id);
CREATE INDEX idx_media_usage_entity ON media_usage(entity_type, entity_id);

CREATE INDEX idx_media_moderation_queue_status ON media_moderation_queue(status);
CREATE INDEX idx_media_moderation_queue_priority ON media_moderation_queue(priority);

CREATE INDEX idx_media_processing_jobs_status ON media_processing_jobs(status);
CREATE INDEX idx_media_processing_jobs_media_id ON media_processing_jobs(media_id);

-- Trigger to update media_count in albums
CREATE OR REPLACE FUNCTION update_album_media_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE media_albums 
        SET media_count = media_count + 1,
            updated_at = NOW()
        WHERE id = NEW.album_id;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE media_albums 
        SET media_count = GREATEST(media_count - 1, 0),
            updated_at = NOW()
        WHERE id = OLD.album_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_album_media_count
AFTER INSERT OR DELETE ON media_album_items
FOR EACH ROW
EXECUTE FUNCTION update_album_media_count();