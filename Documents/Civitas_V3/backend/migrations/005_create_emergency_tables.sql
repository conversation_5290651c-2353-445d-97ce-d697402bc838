-- Emergency System Tables
-- Emergency alerts, responders, and coordination

-- Emergency alerts table
CREATE TABLE IF NOT EXISTS emergency_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    
    -- Alert details
    type VARCHAR(50) NOT NULL CHECK (type IN ('medical', 'fire', 'security', 'natural_disaster', 'accident', 'missing_person', 'other')),
    severity VARCHAR(20) NOT NULL DEFAULT 'high' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    
    -- Location
    location_name VARCHAR(255),
    location GEOGRAPHY(POINT, 4326) NOT NULL,
    location_accuracy NUMERIC(10,2), -- In meters
    address TEXT,
    
    -- Contact
    contact_name VARCHAR(255),
    contact_phone VARCHAR(20),
    safe_word VARCHAR(100),
    
    -- Status
    status emergency_status DEFAULT 'active',
    status_reason TEXT,
    
    -- Media
    media_urls TEXT[] DEFAULT '{}',
    audio_url VARCHAR(500),
    
    -- Response tracking
    responder_count INTEGER DEFAULT 0,
    nearest_responder_distance NUMERIC(10,2), -- In kilometers
    first_responder_id UUID REFERENCES users(id),
    first_response_time TIMESTAMPTZ,
    
    -- Resolution
    resolved_at TIMESTAMPTZ,
    resolved_by UUID REFERENCES users(id),
    resolution_notes TEXT,
    false_alarm BOOLEAN DEFAULT FALSE,
    
    -- Settings
    notify_radius_km NUMERIC(5,2) DEFAULT 5.0,
    requires_verification BOOLEAN DEFAULT FALSE,
    is_test_mode BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    device_info JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_emergency_alerts_user_id ON emergency_alerts(user_id);
CREATE INDEX idx_emergency_alerts_neighborhood_id ON emergency_alerts(neighborhood_id);
CREATE INDEX idx_emergency_alerts_status ON emergency_alerts(status) WHERE status IN ('active', 'responding');
CREATE INDEX idx_emergency_alerts_location ON emergency_alerts USING GIST(location);
CREATE INDEX idx_emergency_alerts_created_at ON emergency_alerts(created_at DESC);
CREATE INDEX idx_emergency_alerts_type ON emergency_alerts(type);

-- Emergency responders
CREATE TABLE IF NOT EXISTS emergency_responders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    emergency_id UUID NOT NULL REFERENCES emergency_alerts(id) ON DELETE CASCADE,
    responder_id UUID NOT NULL REFERENCES users(id),
    
    -- Response details
    response_time TIMESTAMPTZ DEFAULT NOW(),
    response_status VARCHAR(20) DEFAULT 'responding' CHECK (response_status IN ('responding', 'on_scene', 'unavailable', 'cancelled')),
    eta_minutes INTEGER,
    
    -- Location tracking
    initial_location GEOGRAPHY(POINT, 4326),
    current_location GEOGRAPHY(POINT, 4326),
    distance_km NUMERIC(10,2),
    
    -- Communication
    has_contacted BOOLEAN DEFAULT FALSE,
    last_contact_at TIMESTAMPTZ,
    notes TEXT,
    
    -- Arrival
    arrived_at TIMESTAMPTZ,
    departure_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(emergency_id, responder_id)
);

CREATE INDEX idx_emergency_responders_emergency_id ON emergency_responders(emergency_id);
CREATE INDEX idx_emergency_responders_responder_id ON emergency_responders(responder_id);
CREATE INDEX idx_emergency_responders_status ON emergency_responders(response_status);

-- Emergency messages
CREATE TABLE IF NOT EXISTS emergency_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    emergency_id UUID NOT NULL REFERENCES emergency_alerts(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES users(id),
    recipient_id UUID REFERENCES users(id), -- NULL for broadcast messages
    
    -- Message content
    message TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'audio', 'image', 'location')),
    media_url VARCHAR(500),
    
    -- Location updates
    location GEOGRAPHY(POINT, 4326),
    location_name VARCHAR(255),
    
    -- Quick replies
    is_quick_reply BOOLEAN DEFAULT FALSE,
    quick_reply_key VARCHAR(50),
    
    -- Status
    is_broadcast BOOLEAN DEFAULT FALSE,
    is_urgent BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_emergency_messages_emergency_id ON emergency_messages(emergency_id);
CREATE INDEX idx_emergency_messages_sender_id ON emergency_messages(sender_id);
CREATE INDEX idx_emergency_messages_recipient_id ON emergency_messages(recipient_id);
CREATE INDEX idx_emergency_messages_created_at ON emergency_messages(created_at);

-- Emergency services directory
CREATE TABLE IF NOT EXISTS emergency_services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    
    -- Service details
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('hospital', 'police', 'fire', 'poison_control', 'mental_health', 'other')),
    description TEXT,
    
    -- Contact
    phone_primary VARCHAR(20) NOT NULL,
    phone_secondary VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(500),
    
    -- Location
    address TEXT NOT NULL,
    location GEOGRAPHY(POINT, 4326),
    
    -- Availability
    is_24_hours BOOLEAN DEFAULT FALSE,
    hours_of_operation JSONB DEFAULT '{}',
    
    -- Capabilities
    capabilities TEXT[] DEFAULT '{}',
    languages_supported TEXT[] DEFAULT '{}',
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    verified_by UUID REFERENCES users(id),
    verified_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_emergency_services_neighborhood_id ON emergency_services(neighborhood_id);
CREATE INDEX idx_emergency_services_type ON emergency_services(type);
CREATE INDEX idx_emergency_services_location ON emergency_services USING GIST(location);

-- Responder settings and capabilities
CREATE TABLE IF NOT EXISTS responder_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Availability
    is_available BOOLEAN DEFAULT TRUE,
    available_radius_km NUMERIC(5,2) DEFAULT 5.0,
    
    -- Notification preferences
    notify_types TEXT[] DEFAULT '{"medical", "fire", "security", "accident"}',
    notify_severities TEXT[] DEFAULT '{"high", "critical"}',
    max_simultaneous_responses INTEGER DEFAULT 1,
    
    -- Quiet hours
    quiet_hours_enabled BOOLEAN DEFAULT FALSE,
    quiet_hours_start TIME DEFAULT '22:00',
    quiet_hours_end TIME DEFAULT '08:00',
    override_quiet_hours_for_critical BOOLEAN DEFAULT TRUE,
    
    -- Skills and certifications
    medical_training VARCHAR(50), -- 'none', 'first_aid', 'emt', 'paramedic', 'nurse', 'doctor'
    cpr_certified BOOLEAN DEFAULT FALSE,
    has_vehicle BOOLEAN DEFAULT FALSE,
    has_medical_supplies BOOLEAN DEFAULT FALSE,
    other_skills TEXT[] DEFAULT '{}',
    
    -- Statistics
    total_responses INTEGER DEFAULT 0,
    average_response_time_minutes NUMERIC(10,2),
    last_response_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id)
);

CREATE INDEX idx_responder_settings_user_id ON responder_settings(user_id);
CREATE INDEX idx_responder_settings_is_available ON responder_settings(is_available) WHERE is_available = TRUE;

-- Quick reply templates
CREATE TABLE IF NOT EXISTS emergency_quick_replies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key VARCHAR(50) NOT NULL UNIQUE,
    category VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    icon VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_emergency_quick_replies_category ON emergency_quick_replies(category);
CREATE INDEX idx_emergency_quick_replies_sort_order ON emergency_quick_replies(sort_order);

-- Insert default quick replies
INSERT INTO emergency_quick_replies (key, category, message, icon, sort_order) VALUES
('on_my_way', 'response', 'I''m on my way', 'directions_run', 1),
('eta_5_min', 'response', 'I''ll be there in 5 minutes', 'schedule', 2),
('eta_10_min', 'response', 'I''ll be there in 10 minutes', 'schedule', 3),
('need_exact_location', 'clarification', 'Can you share your exact location?', 'location_on', 4),
('need_more_info', 'clarification', 'Can you provide more details?', 'info', 5),
('called_911', 'action', 'I''ve called 911', 'phone', 6),
('help_arrived', 'status', 'Professional help has arrived', 'local_hospital', 7),
('situation_safe', 'status', 'The situation is now safe', 'security', 8);

-- Emergency contact lists
CREATE TABLE IF NOT EXISTS emergency_contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Contact details
    name VARCHAR(255) NOT NULL,
    relationship VARCHAR(50),
    phone_primary VARCHAR(20) NOT NULL,
    phone_secondary VARCHAR(20),
    email VARCHAR(255),
    
    -- Notification preferences
    notify_on_emergency BOOLEAN DEFAULT TRUE,
    is_primary BOOLEAN DEFAULT FALSE,
    
    -- Notes
    medical_info TEXT,
    notes TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_emergency_contacts_user_id ON emergency_contacts(user_id);

-- Emergency response analytics
CREATE TABLE IF NOT EXISTS emergency_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    emergency_id UUID NOT NULL REFERENCES emergency_alerts(id) ON DELETE CASCADE,
    
    -- Response metrics
    first_view_time TIMESTAMPTZ,
    first_response_time TIMESTAMPTZ,
    total_views INTEGER DEFAULT 0,
    total_responders INTEGER DEFAULT 0,
    
    -- Outcome metrics
    response_time_seconds INTEGER,
    resolution_time_seconds INTEGER,
    responder_arrival_times JSONB DEFAULT '[]',
    
    -- Geographic metrics
    alert_coverage_area_km2 NUMERIC(10,2),
    average_responder_distance_km NUMERIC(10,2),
    
    -- Communication metrics
    total_messages INTEGER DEFAULT 0,
    broadcast_messages INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(emergency_id)
);

CREATE INDEX idx_emergency_analytics_emergency_id ON emergency_analytics(emergency_id);

-- Functions and Triggers

-- Update responder count
CREATE OR REPLACE FUNCTION update_emergency_responder_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE emergency_alerts
    SET responder_count = (
        SELECT COUNT(*)
        FROM emergency_responders
        WHERE emergency_id = NEW.emergency_id
        AND response_status IN ('responding', 'on_scene')
    )
    WHERE id = NEW.emergency_id;
    
    -- Set first responder if not set
    IF NEW.response_status = 'responding' THEN
        UPDATE emergency_alerts
        SET first_responder_id = NEW.responder_id,
            first_response_time = NEW.response_time
        WHERE id = NEW.emergency_id
        AND first_responder_id IS NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_responder_count_trigger
    AFTER INSERT OR UPDATE ON emergency_responders
    FOR EACH ROW EXECUTE FUNCTION update_emergency_responder_count();

-- Calculate responder distance
CREATE OR REPLACE FUNCTION calculate_responder_distance(
    p_emergency_location GEOGRAPHY,
    p_responder_location GEOGRAPHY
) RETURNS NUMERIC AS $$
BEGIN
    RETURN ST_Distance(p_emergency_location, p_responder_location) / 1000.0; -- Convert to kilometers
END;
$$ LANGUAGE plpgsql;

-- Get available responders
CREATE OR REPLACE FUNCTION get_available_responders(
    p_emergency_id UUID,
    p_location GEOGRAPHY,
    p_radius_km NUMERIC
) RETURNS TABLE (
    user_id UUID,
    distance_km NUMERIC,
    medical_training VARCHAR,
    total_responses INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        ST_Distance(p_location, u.location) / 1000.0 as distance_km,
        rs.medical_training,
        rs.total_responses
    FROM users u
    JOIN responder_settings rs ON u.id = rs.user_id
    WHERE u.is_emergency_responder = TRUE
    AND rs.is_available = TRUE
    AND ST_DWithin(p_location, u.location, p_radius_km * 1000)
    AND NOT EXISTS (
        SELECT 1 FROM emergency_responders
        WHERE emergency_id = p_emergency_id
        AND responder_id = u.id
    )
    ORDER BY distance_km ASC;
END;
$$ LANGUAGE plpgsql;

-- Update statistics
CREATE OR REPLACE FUNCTION update_responder_statistics()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.response_status = 'on_scene' AND OLD.response_status != 'on_scene' THEN
        UPDATE responder_settings
        SET total_responses = total_responses + 1,
            last_response_at = NOW(),
            average_response_time_minutes = (
                SELECT AVG(EXTRACT(EPOCH FROM (arrived_at - response_time)) / 60)
                FROM emergency_responders
                WHERE responder_id = NEW.responder_id
                AND arrived_at IS NOT NULL
            )
        WHERE user_id = NEW.responder_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_responder_stats_trigger
    AFTER UPDATE ON emergency_responders
    FOR EACH ROW EXECUTE FUNCTION update_responder_statistics();

-- Update timestamps
CREATE TRIGGER update_emergency_alerts_updated_at BEFORE UPDATE ON emergency_alerts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_emergency_responders_updated_at BEFORE UPDATE ON emergency_responders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_emergency_services_updated_at BEFORE UPDATE ON emergency_services
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_responder_settings_updated_at BEFORE UPDATE ON responder_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_emergency_contacts_updated_at BEFORE UPDATE ON emergency_contacts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_emergency_analytics_updated_at BEFORE UPDATE ON emergency_analytics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments
COMMENT ON TABLE emergency_alerts IS 'Emergency alerts and incidents';
COMMENT ON TABLE emergency_responders IS 'Users responding to emergencies';
COMMENT ON TABLE emergency_messages IS 'Messages within emergency context';
COMMENT ON TABLE emergency_services IS 'Directory of emergency services';
COMMENT ON TABLE responder_settings IS 'Emergency responder preferences';
COMMENT ON TABLE emergency_quick_replies IS 'Pre-defined emergency messages';
COMMENT ON TABLE emergency_contacts IS 'User emergency contact lists';
COMMENT ON TABLE emergency_analytics IS 'Analytics for emergency responses';