-- Final Indexes and Triggers
-- Performance optimizations and cross-table constraints

-- =============================================================================
-- COMPOSITE INDEXES FOR COMMON QUERIES
-- =============================================================================

-- User activity indexes
CREATE INDEX IF NOT EXISTS idx_users_neighborhood_active ON users(neighborhood_id, is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_users_last_active ON users(last_active_at DESC) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_users_created_at_neighborhood ON users(neighborhood_id, created_at DESC);

-- Posts performance indexes
CREATE INDEX IF NOT EXISTS idx_posts_feed_query ON posts(neighborhood_id, created_at DESC) 
    WHERE is_deleted = FALSE AND visibility IN ('public', 'neighborhood');
CREATE INDEX IF NOT EXISTS idx_posts_user_timeline ON posts(user_id, created_at DESC) 
    WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_posts_help_requests ON posts(neighborhood_id, help_status, created_at DESC) 
    WHERE post_type = 'help_request' AND is_deleted = FALSE;

-- Messages performance indexes
CREATE INDEX IF NOT EXISTS idx_messages_conversation_recent ON messages(conversation_id, created_at DESC) 
    WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_conversation_participants_active ON conversation_participants(user_id, last_message_at DESC) 
    WHERE is_active = TRUE;

-- Events performance indexes
CREATE INDEX IF NOT EXISTS idx_events_upcoming ON events(neighborhood_id, start_time) 
    WHERE status = 'upcoming' AND is_cancelled = FALSE;
CREATE INDEX IF NOT EXISTS idx_event_rsvps_user_upcoming ON event_rsvps(user_id, created_at DESC) 
    WHERE status = 'going';

-- Emergency performance indexes
CREATE INDEX IF NOT EXISTS idx_emergency_alerts_active_location ON emergency_alerts 
    USING GIST(neighborhood_id, location) WHERE status IN ('active', 'responding');

-- Exchange performance indexes
CREATE INDEX IF NOT EXISTS idx_exchange_listings_browse ON exchange_listings(neighborhood_id, created_at DESC) 
    WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_exchange_listings_user_active ON exchange_listings(user_id, created_at DESC) 
    WHERE status IN ('active', 'pending');

-- Notifications performance indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_unread ON notifications(user_id, created_at DESC) 
    WHERE is_read = FALSE;
CREATE INDEX IF NOT EXISTS idx_notifications_user_priority ON notifications(user_id, priority DESC, created_at DESC) 
    WHERE is_read = FALSE;

-- =============================================================================
-- FULL TEXT SEARCH INDEXES
-- =============================================================================

-- Posts full text search
ALTER TABLE posts ADD COLUMN IF NOT EXISTS search_vector tsvector;
UPDATE posts SET search_vector = to_tsvector('english', COALESCE(content, '') || ' ' || COALESCE(array_to_string(hashtags, ' '), ''));
CREATE INDEX IF NOT EXISTS idx_posts_search_vector ON posts USING GIN(search_vector);

-- Events full text search
ALTER TABLE events ADD COLUMN IF NOT EXISTS search_vector tsvector;
UPDATE events SET search_vector = to_tsvector('english', COALESCE(title, '') || ' ' || COALESCE(description, '') || ' ' || COALESCE(array_to_string(tags, ' '), ''));
CREATE INDEX IF NOT EXISTS idx_events_search_vector ON events USING GIN(search_vector);

-- Exchange listings full text search
ALTER TABLE exchange_listings ADD COLUMN IF NOT EXISTS search_vector tsvector;
UPDATE exchange_listings SET search_vector = to_tsvector('english', COALESCE(title, '') || ' ' || COALESCE(description, '') || ' ' || COALESCE(array_to_string(tags, ' '), ''));
CREATE INDEX IF NOT EXISTS idx_exchange_listings_search_vector ON exchange_listings USING GIN(search_vector);

-- =============================================================================
-- CROSS-TABLE CONSTRAINTS AND TRIGGERS
-- =============================================================================

-- Function to update user last_active_at
CREATE OR REPLACE FUNCTION update_user_last_active()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE users 
    SET last_active_at = NOW() 
    WHERE id = COALESCE(NEW.user_id, NEW.sender_id, NEW.host_id, NEW.created_by);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply last active trigger to various tables
CREATE TRIGGER update_user_last_active_on_post
    AFTER INSERT ON posts
    FOR EACH ROW EXECUTE FUNCTION update_user_last_active();

CREATE TRIGGER update_user_last_active_on_message
    AFTER INSERT ON messages
    FOR EACH ROW EXECUTE FUNCTION update_user_last_active();

CREATE TRIGGER update_user_last_active_on_event
    AFTER INSERT ON events
    FOR EACH ROW EXECUTE FUNCTION update_user_last_active();

-- Function to update user karma/points
CREATE OR REPLACE FUNCTION update_user_karma()
RETURNS TRIGGER AS $$
DECLARE
    v_points INTEGER;
    v_action VARCHAR;
BEGIN
    -- Determine points based on action
    CASE TG_TABLE_NAME
        WHEN 'posts' THEN
            IF NEW.post_type = 'help_offer' THEN v_points := 10; v_action := 'help_offered';
            ELSE v_points := 5; v_action := 'post_created';
            END IF;
        WHEN 'help_offers' THEN
            IF NEW.status = 'completed' THEN v_points := 20; v_action := 'help_completed';
            ELSE v_points := 0;
            END IF;
        WHEN 'event_rsvps' THEN
            v_points := 3; v_action := 'event_rsvp';
        WHEN 'emergency_responders' THEN
            v_points := 25; v_action := 'emergency_response';
        ELSE
            v_points := 0;
    END CASE;
    
    -- Award points if applicable
    IF v_points > 0 THEN
        INSERT INTO points (user_id, points, action, reference_id, reference_type)
        VALUES (
            COALESCE(NEW.user_id, NEW.helper_id, NEW.responder_id),
            v_points,
            v_action,
            NEW.id,
            TG_TABLE_NAME
        );
        
        -- Update user total points
        UPDATE users 
        SET karma_points = karma_points + v_points
        WHERE id = COALESCE(NEW.user_id, NEW.helper_id, NEW.responder_id);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply karma triggers
CREATE TRIGGER award_karma_for_posts
    AFTER INSERT ON posts
    FOR EACH ROW 
    WHEN (NEW.is_deleted = FALSE)
    EXECUTE FUNCTION update_user_karma();

CREATE TRIGGER award_karma_for_help
    AFTER UPDATE OF status ON help_offers
    FOR EACH ROW 
    WHEN (OLD.status != 'completed' AND NEW.status = 'completed')
    EXECUTE FUNCTION update_user_karma();

CREATE TRIGGER award_karma_for_emergency_response
    AFTER INSERT ON emergency_responders
    FOR EACH ROW EXECUTE FUNCTION update_user_karma();

-- Function to maintain data consistency
CREATE OR REPLACE FUNCTION maintain_data_consistency()
RETURNS VOID AS $$
BEGIN
    -- Update user post counts
    UPDATE users u
    SET post_count = (
        SELECT COUNT(*) FROM posts 
        WHERE user_id = u.id AND is_deleted = FALSE
    );
    
    -- Update user follower/following counts
    UPDATE users u
    SET follower_count = (
        SELECT COUNT(*) FROM user_relationships 
        WHERE followed_id = u.id AND status = 'active'
    ),
    following_count = (
        SELECT COUNT(*) FROM user_relationships 
        WHERE follower_id = u.id AND status = 'active'
    );
    
    -- Update neighborhood member counts
    UPDATE neighborhoods n
    SET member_count = (
        SELECT COUNT(*) FROM users 
        WHERE neighborhood_id = n.id AND is_active = TRUE
    );
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- MATERIALIZED VIEWS FOR PERFORMANCE
-- =============================================================================

-- User engagement scores
CREATE MATERIALIZED VIEW IF NOT EXISTS user_engagement_scores AS
SELECT 
    u.id as user_id,
    u.neighborhood_id,
    COUNT(DISTINCT p.id) as post_count_30d,
    COUNT(DISTINCT c.id) as comment_count_30d,
    COUNT(DISTINCT hr.id) as help_responses_30d,
    COUNT(DISTINCT e.id) as events_hosted_30d,
    COUNT(DISTINCT er.id) as emergency_responses_30d,
    AVG(CASE WHEN hr.rating IS NOT NULL THEN hr.rating END) as avg_help_rating,
    u.karma_points,
    COALESCE(
        COUNT(DISTINCT p.id) * 1.0 +
        COUNT(DISTINCT c.id) * 0.5 +
        COUNT(DISTINCT hr.id) * 3.0 +
        COUNT(DISTINCT e.id) * 2.0 +
        COUNT(DISTINCT er.id) * 5.0 +
        u.karma_points * 0.1,
        0
    ) as engagement_score
FROM users u
LEFT JOIN posts p ON p.user_id = u.id 
    AND p.created_at > NOW() - INTERVAL '30 days' 
    AND p.is_deleted = FALSE
LEFT JOIN post_comments c ON c.user_id = u.id 
    AND c.created_at > NOW() - INTERVAL '30 days' 
    AND c.is_deleted = FALSE
LEFT JOIN help_offers hr ON hr.helper_id = u.id 
    AND hr.created_at > NOW() - INTERVAL '30 days'
LEFT JOIN events e ON e.host_id = u.id 
    AND e.created_at > NOW() - INTERVAL '30 days'
LEFT JOIN emergency_responders er ON er.responder_id = u.id 
    AND er.created_at > NOW() - INTERVAL '30 days'
WHERE u.is_active = TRUE
GROUP BY u.id, u.neighborhood_id, u.karma_points;

CREATE UNIQUE INDEX idx_user_engagement_scores_user_id ON user_engagement_scores(user_id);
CREATE INDEX idx_user_engagement_scores_neighborhood ON user_engagement_scores(neighborhood_id);
CREATE INDEX idx_user_engagement_scores_score ON user_engagement_scores(engagement_score DESC);

-- Neighborhood activity summary
CREATE MATERIALIZED VIEW IF NOT EXISTS neighborhood_activity_summary AS
SELECT 
    n.id as neighborhood_id,
    COUNT(DISTINCT p.id) as posts_today,
    COUNT(DISTINCT e.id) as events_this_week,
    COUNT(DISTINCT ea.id) as emergency_alerts_this_month,
    COUNT(DISTINCT hr.id) as help_requests_open,
    COUNT(DISTINCT u.id) as active_users_today,
    AVG(EXTRACT(EPOCH FROM (NOW() - u.last_active_at))/3600)::INTEGER as avg_hours_since_active
FROM neighborhoods n
LEFT JOIN posts p ON p.neighborhood_id = n.id 
    AND p.created_at > NOW() - INTERVAL '1 day' 
    AND p.is_deleted = FALSE
LEFT JOIN events e ON e.neighborhood_id = n.id 
    AND e.start_time BETWEEN NOW() AND NOW() + INTERVAL '7 days' 
    AND e.status = 'upcoming'
LEFT JOIN emergency_alerts ea ON ea.neighborhood_id = n.id 
    AND ea.created_at > NOW() - INTERVAL '30 days'
LEFT JOIN posts hr ON hr.neighborhood_id = n.id 
    AND hr.post_type = 'help_request' 
    AND hr.help_status = 'open' 
    AND hr.is_deleted = FALSE
LEFT JOIN users u ON u.neighborhood_id = n.id 
    AND u.last_active_at > NOW() - INTERVAL '1 day' 
    AND u.is_active = TRUE
WHERE n.is_active = TRUE
GROUP BY n.id;

CREATE UNIQUE INDEX idx_neighborhood_activity_summary_id ON neighborhood_activity_summary(neighborhood_id);

-- =============================================================================
-- SCHEDULED MAINTENANCE FUNCTIONS
-- =============================================================================

-- Master maintenance function
CREATE OR REPLACE FUNCTION perform_scheduled_maintenance()
RETURNS VOID AS $$
BEGIN
    -- Update event statuses
    PERFORM update_event_status();
    
    -- Expire old exchange listings
    PERFORM expire_old_listings();
    
    -- Clean up expired typing indicators
    PERFORM cleanup_expired_typing_indicators();
    
    -- Calculate daily popular searches
    PERFORM calculate_search_popularity();
    
    -- Clean up old search history
    PERFORM cleanup_old_search_history();
    
    -- Clean up expired notifications
    PERFORM cleanup_expired_notifications();
    
    -- Update materialized views
    REFRESH MATERIALIZED VIEW CONCURRENTLY user_engagement_scores;
    REFRESH MATERIALIZED VIEW CONCURRENTLY neighborhood_activity_summary;
    
    -- Update search vectors for new content
    UPDATE posts 
    SET search_vector = to_tsvector('english', COALESCE(content, '') || ' ' || COALESCE(array_to_string(hashtags, ' '), ''))
    WHERE search_vector IS NULL;
    
    UPDATE events 
    SET search_vector = to_tsvector('english', COALESCE(title, '') || ' ' || COALESCE(description, '') || ' ' || COALESCE(array_to_string(tags, ' '), ''))
    WHERE search_vector IS NULL;
    
    UPDATE exchange_listings 
    SET search_vector = to_tsvector('english', COALESCE(title, '') || ' ' || COALESCE(description, '') || ' ' || COALESCE(array_to_string(tags, ' '), ''))
    WHERE search_vector IS NULL;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- AUDIT AND SECURITY
-- =============================================================================

-- Audit log table
CREATE TABLE IF NOT EXISTS audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(100) NOT NULL,
    operation VARCHAR(20) NOT NULL,
    user_id UUID,
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_audit_log_table_name ON audit_log(table_name);
CREATE INDEX idx_audit_log_user_id ON audit_log(user_id);
CREATE INDEX idx_audit_log_record_id ON audit_log(record_id);
CREATE INDEX idx_audit_log_created_at ON audit_log(created_at DESC);

-- Generic audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_log (
        table_name,
        operation,
        user_id,
        record_id,
        old_values,
        new_values
    ) VALUES (
        TG_TABLE_NAME,
        TG_OP,
        COALESCE(NEW.user_id, OLD.user_id, current_setting('app.current_user_id', true)::UUID),
        COALESCE(NEW.id, OLD.id),
        CASE WHEN TG_OP IN ('UPDATE', 'DELETE') THEN to_jsonb(OLD) ELSE NULL END,
        CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN to_jsonb(NEW) ELSE NULL END
    );
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Apply audit triggers to sensitive tables
CREATE TRIGGER audit_users_changes
    AFTER INSERT OR UPDATE OR DELETE ON users
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_emergency_alerts_changes
    AFTER INSERT OR UPDATE OR DELETE ON emergency_alerts
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- =============================================================================
-- PERFORMANCE MONITORING
-- =============================================================================

-- Query performance stats
CREATE TABLE IF NOT EXISTS query_performance_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_hash VARCHAR(64) NOT NULL,
    query_pattern TEXT NOT NULL,
    execution_count INTEGER DEFAULT 1,
    total_time_ms NUMERIC(10,2),
    avg_time_ms NUMERIC(10,2),
    max_time_ms NUMERIC(10,2),
    last_executed_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_query_performance_stats_hash ON query_performance_stats(query_hash);
CREATE INDEX idx_query_performance_stats_avg_time ON query_performance_stats(avg_time_ms DESC);

-- =============================================================================
-- FINAL GRANTS AND PERMISSIONS (if using different roles)
-- =============================================================================

-- Example grants (adjust based on your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO civitas_app;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO civitas_app;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO civitas_app;

-- Comments
COMMENT ON MATERIALIZED VIEW user_engagement_scores IS 'Calculated user engagement metrics';
COMMENT ON MATERIALIZED VIEW neighborhood_activity_summary IS 'Neighborhood activity dashboard data';
COMMENT ON TABLE audit_log IS 'Audit trail for sensitive operations';
COMMENT ON TABLE query_performance_stats IS 'Query performance monitoring';