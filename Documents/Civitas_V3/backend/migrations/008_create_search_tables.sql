-- Search System Tables
-- Search history, saved searches, and search analytics

-- Search history
CREATE TABLE IF NOT EXISTS search_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_id VARCHAR(255), -- For anonymous users
    
    -- Search details
    search_query TEXT NOT NULL,
    search_type VARCHAR(50) NOT NULL CHECK (search_type IN ('all', 'users', 'posts', 'events', 'exchange', 'services', 'emergency', 'help')),
    
    -- Filters applied
    filters JSONB DEFAULT '{}',
    neighborhood_id UUID REFERENCES neighborhoods(id),
    location GEOGRAPHY(POINT, 4326),
    radius_km NUMERIC(5,2),
    date_from TIMESTAMPTZ,
    date_to TIMESTAMPTZ,
    
    -- Results
    result_count INTEGER DEFAULT 0,
    clicked_results JSONB DEFAULT '[]',
    first_click_position INTEGER,
    
    -- Context
    search_context VARCHAR(50), -- 'home', 'explore', 'profile', etc.
    device_type VARCHAR(20),
    ip_address INET,
    user_agent TEXT,
    
    -- Performance
    response_time_ms INTEGER,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_search_history_user_id ON search_history(user_id);
CREATE INDEX idx_search_history_session_id ON search_history(session_id);
CREATE INDEX idx_search_history_created_at ON search_history(created_at DESC);
CREATE INDEX idx_search_history_search_type ON search_history(search_type);
CREATE INDEX idx_search_history_search_query ON search_history USING GIN(to_tsvector('english', search_query));

-- Popular searches
CREATE TABLE IF NOT EXISTS popular_searches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    search_query TEXT NOT NULL,
    normalized_query TEXT NOT NULL,
    search_type VARCHAR(50) NOT NULL,
    neighborhood_id UUID REFERENCES neighborhoods(id),
    
    -- Metrics
    search_count INTEGER DEFAULT 1,
    unique_users INTEGER DEFAULT 1,
    avg_result_count NUMERIC(10,2),
    click_through_rate NUMERIC(5,2),
    
    -- Time period
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('hourly', 'daily', 'weekly', 'monthly')),
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(normalized_query, search_type, neighborhood_id, period_type, period_start)
);

CREATE INDEX idx_popular_searches_normalized_query ON popular_searches(normalized_query);
CREATE INDEX idx_popular_searches_search_type ON popular_searches(search_type);
CREATE INDEX idx_popular_searches_neighborhood_id ON popular_searches(neighborhood_id);
CREATE INDEX idx_popular_searches_period ON popular_searches(period_type, period_start DESC);
CREATE INDEX idx_popular_searches_search_count ON popular_searches(search_count DESC);

-- Search suggestions
CREATE TABLE IF NOT EXISTS search_suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    suggestion_text TEXT NOT NULL,
    suggestion_type VARCHAR(50) NOT NULL CHECK (suggestion_type IN ('query', 'user', 'hashtag', 'location', 'category')),
    
    -- Related entity
    entity_type VARCHAR(50),
    entity_id UUID,
    
    -- Metadata
    display_text TEXT,
    description TEXT,
    icon VARCHAR(50),
    image_url VARCHAR(500),
    
    -- Scoring
    base_score NUMERIC(10,2) DEFAULT 1.0,
    popularity_score NUMERIC(10,2) DEFAULT 0.0,
    personalization_score NUMERIC(10,2) DEFAULT 0.0,
    
    -- Context
    contexts TEXT[] DEFAULT '{}', -- Where this suggestion is relevant
    keywords TEXT[] DEFAULT '{}',
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_promoted BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_search_suggestions_suggestion_text ON search_suggestions USING GIN(to_tsvector('english', suggestion_text));
CREATE INDEX idx_search_suggestions_suggestion_type ON search_suggestions(suggestion_type);
CREATE INDEX idx_search_suggestions_entity ON search_suggestions(entity_type, entity_id);
CREATE INDEX idx_search_suggestions_is_active ON search_suggestions(is_active);
CREATE INDEX idx_search_suggestions_keywords ON search_suggestions USING GIN(keywords);

-- User search preferences
CREATE TABLE IF NOT EXISTS user_search_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Default filters
    default_search_type VARCHAR(50) DEFAULT 'all',
    default_radius_km NUMERIC(5,2) DEFAULT 5.0,
    include_neighboring_areas BOOLEAN DEFAULT TRUE,
    
    -- Privacy
    save_search_history BOOLEAN DEFAULT TRUE,
    share_search_data BOOLEAN DEFAULT TRUE,
    
    -- Personalization
    preferred_categories TEXT[] DEFAULT '{}',
    excluded_keywords TEXT[] DEFAULT '{}',
    
    -- Display preferences
    results_per_page INTEGER DEFAULT 20,
    show_images BOOLEAN DEFAULT TRUE,
    compact_view BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id)
);

CREATE INDEX idx_user_search_preferences_user_id ON user_search_preferences(user_id);

-- Search shortcuts
CREATE TABLE IF NOT EXISTS search_shortcuts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Shortcut details
    name VARCHAR(100) NOT NULL,
    shortcut_key VARCHAR(50) NOT NULL,
    search_type VARCHAR(50) NOT NULL,
    search_query TEXT,
    filters JSONB DEFAULT '{}',
    
    -- Usage
    use_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, shortcut_key)
);

CREATE INDEX idx_search_shortcuts_user_id ON search_shortcuts(user_id);
CREATE INDEX idx_search_shortcuts_shortcut_key ON search_shortcuts(shortcut_key);

-- Search analytics events
CREATE TABLE IF NOT EXISTS search_analytics_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    search_history_id UUID REFERENCES search_history(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_id VARCHAR(255),
    
    -- Event details
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('search', 'click', 'dwell', 'refine', 'clear', 'save')),
    event_data JSONB DEFAULT '{}',
    
    -- Click events
    result_type VARCHAR(50),
    result_id UUID,
    result_position INTEGER,
    
    -- Timing
    time_to_event_ms INTEGER,
    dwell_time_ms INTEGER,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_search_analytics_events_search_history_id ON search_analytics_events(search_history_id);
CREATE INDEX idx_search_analytics_events_user_id ON search_analytics_events(user_id);
CREATE INDEX idx_search_analytics_events_session_id ON search_analytics_events(session_id);
CREATE INDEX idx_search_analytics_events_event_type ON search_analytics_events(event_type);
CREATE INDEX idx_search_analytics_events_created_at ON search_analytics_events(created_at DESC);

-- Content search index (for better full-text search)
CREATE TABLE IF NOT EXISTS content_search_index (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_type VARCHAR(50) NOT NULL,
    content_id UUID NOT NULL,
    
    -- Searchable content
    title TEXT,
    body TEXT,
    tags TEXT[] DEFAULT '{}',
    
    -- Computed search vectors
    title_vector tsvector,
    body_vector tsvector,
    
    -- Metadata for ranking
    author_id UUID REFERENCES users(id) ON DELETE CASCADE,
    neighborhood_id UUID REFERENCES neighborhoods(id),
    engagement_score NUMERIC(10,2) DEFAULT 0.0,
    freshness_score NUMERIC(10,2) DEFAULT 1.0,
    quality_score NUMERIC(10,2) DEFAULT 1.0,
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    last_indexed_at TIMESTAMPTZ DEFAULT NOW(),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(content_type, content_id)
);

CREATE INDEX idx_content_search_index_content ON content_search_index(content_type, content_id);
CREATE INDEX idx_content_search_index_title_vector ON content_search_index USING GIN(title_vector);
CREATE INDEX idx_content_search_index_body_vector ON content_search_index USING GIN(body_vector);
CREATE INDEX idx_content_search_index_tags ON content_search_index USING GIN(tags);
CREATE INDEX idx_content_search_index_author_id ON content_search_index(author_id);
CREATE INDEX idx_content_search_index_neighborhood_id ON content_search_index(neighborhood_id);
CREATE INDEX idx_content_search_index_is_active ON content_search_index(is_active);

-- Trending searches
CREATE TABLE IF NOT EXISTS trending_searches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    search_query TEXT NOT NULL,
    normalized_query TEXT NOT NULL,
    neighborhood_id UUID REFERENCES neighborhoods(id),
    
    -- Trend metrics
    trend_score NUMERIC(10,2) NOT NULL,
    growth_rate NUMERIC(10,2),
    search_volume INTEGER,
    unique_searchers INTEGER,
    
    -- Time window
    window_start TIMESTAMPTZ NOT NULL,
    window_end TIMESTAMPTZ NOT NULL,
    
    -- Category
    category VARCHAR(50),
    is_emerging BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(normalized_query, neighborhood_id, window_start)
);

CREATE INDEX idx_trending_searches_normalized_query ON trending_searches(normalized_query);
CREATE INDEX idx_trending_searches_neighborhood_id ON trending_searches(neighborhood_id);
CREATE INDEX idx_trending_searches_window ON trending_searches(window_start DESC);
CREATE INDEX idx_trending_searches_trend_score ON trending_searches(trend_score DESC);

-- Functions and Triggers

-- Update search vectors
CREATE OR REPLACE FUNCTION update_content_search_vectors()
RETURNS TRIGGER AS $$
BEGIN
    NEW.title_vector = to_tsvector('english', COALESCE(NEW.title, ''));
    NEW.body_vector = to_tsvector('english', COALESCE(NEW.body, ''));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_search_vectors_trigger
    BEFORE INSERT OR UPDATE OF title, body ON content_search_index
    FOR EACH ROW EXECUTE FUNCTION update_content_search_vectors();

-- Log search analytics
CREATE OR REPLACE FUNCTION log_search_click(
    p_search_history_id UUID,
    p_result_type VARCHAR,
    p_result_id UUID,
    p_position INTEGER
) RETURNS VOID AS $$
BEGIN
    -- Log the click event
    INSERT INTO search_analytics_events (
        search_history_id,
        user_id,
        session_id,
        event_type,
        result_type,
        result_id,
        result_position,
        time_to_event_ms
    )
    SELECT
        sh.id,
        sh.user_id,
        sh.session_id,
        'click',
        p_result_type,
        p_result_id,
        p_position,
        EXTRACT(EPOCH FROM (NOW() - sh.created_at)) * 1000
    FROM search_history sh
    WHERE sh.id = p_search_history_id;
    
    -- Update clicked results in search history
    UPDATE search_history
    SET clicked_results = clicked_results || jsonb_build_object(
        'type', p_result_type,
        'id', p_result_id,
        'position', p_position,
        'clicked_at', NOW()
    ),
    first_click_position = COALESCE(first_click_position, p_position)
    WHERE id = p_search_history_id;
END;
$$ LANGUAGE plpgsql;

-- Calculate search popularity
CREATE OR REPLACE FUNCTION calculate_search_popularity()
RETURNS VOID AS $$
BEGIN
    -- Daily popular searches
    INSERT INTO popular_searches (
        search_query,
        normalized_query,
        search_type,
        neighborhood_id,
        search_count,
        unique_users,
        avg_result_count,
        period_type,
        period_start,
        period_end
    )
    SELECT
        search_query,
        LOWER(TRIM(search_query)),
        search_type,
        neighborhood_id,
        COUNT(*),
        COUNT(DISTINCT user_id),
        AVG(result_count),
        'daily',
        DATE_TRUNC('day', NOW() - INTERVAL '1 day'),
        DATE_TRUNC('day', NOW())
    FROM search_history
    WHERE created_at >= DATE_TRUNC('day', NOW() - INTERVAL '1 day')
    AND created_at < DATE_TRUNC('day', NOW())
    GROUP BY search_query, search_type, neighborhood_id
    ON CONFLICT (normalized_query, search_type, neighborhood_id, period_type, period_start) 
    DO UPDATE SET
        search_count = EXCLUDED.search_count,
        unique_users = EXCLUDED.unique_users,
        avg_result_count = EXCLUDED.avg_result_count,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Update search shortcut usage
CREATE OR REPLACE FUNCTION update_search_shortcut_usage()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE search_shortcuts
    SET use_count = use_count + 1,
        last_used_at = NOW()
    WHERE id = NEW.id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Clean old search history
CREATE OR REPLACE FUNCTION cleanup_old_search_history()
RETURNS INTEGER AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    DELETE FROM search_history
    WHERE created_at < NOW() - INTERVAL '90 days'
    AND user_id IS NULL; -- Only delete anonymous searches
    
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    
    -- Also clean old analytics events
    DELETE FROM search_analytics_events
    WHERE created_at < NOW() - INTERVAL '90 days'
    AND search_history_id NOT IN (SELECT id FROM search_history);
    
    RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Update timestamps
CREATE TRIGGER update_popular_searches_updated_at BEFORE UPDATE ON popular_searches
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_search_suggestions_updated_at BEFORE UPDATE ON search_suggestions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_search_preferences_updated_at BEFORE UPDATE ON user_search_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_search_shortcuts_updated_at BEFORE UPDATE ON search_shortcuts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_search_index_updated_at BEFORE UPDATE ON content_search_index
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments
COMMENT ON TABLE search_history IS 'User search history and sessions';
COMMENT ON TABLE popular_searches IS 'Aggregated popular search queries';
COMMENT ON TABLE search_suggestions IS 'Search autocomplete suggestions';
COMMENT ON TABLE user_search_preferences IS 'User search settings';
COMMENT ON TABLE search_shortcuts IS 'User-defined search shortcuts';
COMMENT ON TABLE search_analytics_events IS 'Detailed search interaction events';
COMMENT ON TABLE content_search_index IS 'Full-text search index for content';
COMMENT ON TABLE trending_searches IS 'Trending search queries by time window';