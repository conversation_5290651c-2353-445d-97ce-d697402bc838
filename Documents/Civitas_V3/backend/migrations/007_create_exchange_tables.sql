-- Exchange System Tables
-- Marketplace for goods, services, and skills exchange

-- Exchange item categories
CREATE TABLE IF NOT EXISTS exchange_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    parent_category_id UUID REFERENCES exchange_categories(id),
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(7),
    item_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_exchange_categories_parent_id ON exchange_categories(parent_category_id);
CREATE INDEX idx_exchange_categories_slug ON exchange_categories(slug);
CREATE INDEX idx_exchange_categories_is_active ON exchange_categories(is_active);

-- Insert default categories
INSERT INTO exchange_categories (name, slug, description, icon, sort_order) VALUES
('For Sale', 'for-sale', 'Items available for purchase', 'sell', 1),
('Wanted', 'wanted', 'Items being sought', 'search', 2),
('Free', 'free', 'Free items to give away', 'volunteer_activism', 3),
('Services', 'services', 'Services offered or needed', 'handyman', 4),
('Skills Exchange', 'skills', 'Exchange skills with neighbors', 'school', 5),
('Rentals', 'rentals', 'Items available for rent', 'key', 6);

-- Exchange listings
CREATE TABLE IF NOT EXISTS exchange_listings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    category_id UUID NOT NULL REFERENCES exchange_categories(id),
    
    -- Listing details
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    listing_type VARCHAR(20) NOT NULL CHECK (listing_type IN ('offer', 'request')),
    exchange_type VARCHAR(20) NOT NULL CHECK (exchange_type IN ('sale', 'free', 'trade', 'rent', 'service', 'skill')),
    
    -- Pricing
    price NUMERIC(10,2),
    price_type VARCHAR(20) DEFAULT 'fixed' CHECK (price_type IN ('fixed', 'negotiable', 'free', 'trade')),
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- Item specifics
    condition VARCHAR(20) CHECK (condition IN ('new', 'like_new', 'good', 'fair', 'poor')),
    quantity INTEGER DEFAULT 1,
    unit VARCHAR(50),
    
    -- Availability
    available_from TIMESTAMPTZ DEFAULT NOW(),
    available_until TIMESTAMPTZ,
    pickup_available BOOLEAN DEFAULT TRUE,
    delivery_available BOOLEAN DEFAULT FALSE,
    delivery_fee NUMERIC(10,2),
    delivery_radius_km NUMERIC(5,2),
    
    -- Location
    location_name VARCHAR(255),
    location GEOGRAPHY(POINT, 4326),
    hide_exact_location BOOLEAN DEFAULT FALSE,
    
    -- Contact preferences
    contact_method VARCHAR(20) DEFAULT 'in_app' CHECK (contact_method IN ('in_app', 'phone', 'email', 'all')),
    phone_number VARCHAR(20),
    contact_email VARCHAR(255),
    
    -- Media
    media_urls TEXT[] DEFAULT '{}',
    thumbnail_url VARCHAR(500),
    
    -- Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('draft', 'active', 'pending', 'sold', 'expired', 'cancelled')),
    is_featured BOOLEAN DEFAULT FALSE,
    featured_until TIMESTAMPTZ,
    
    -- Engagement
    view_count INTEGER DEFAULT 0,
    favorite_count INTEGER DEFAULT 0,
    inquiry_count INTEGER DEFAULT 0,
    
    -- Tags
    tags TEXT[] DEFAULT '{}',
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    source VARCHAR(50) DEFAULT 'app',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_exchange_listings_user_id ON exchange_listings(user_id);
CREATE INDEX idx_exchange_listings_neighborhood_id ON exchange_listings(neighborhood_id);
CREATE INDEX idx_exchange_listings_category_id ON exchange_listings(category_id);
CREATE INDEX idx_exchange_listings_status ON exchange_listings(status) WHERE status = 'active';
CREATE INDEX idx_exchange_listings_listing_type ON exchange_listings(listing_type);
CREATE INDEX idx_exchange_listings_exchange_type ON exchange_listings(exchange_type);
CREATE INDEX idx_exchange_listings_price ON exchange_listings(price);
CREATE INDEX idx_exchange_listings_location ON exchange_listings USING GIST(location);
CREATE INDEX idx_exchange_listings_created_at ON exchange_listings(created_at DESC);
CREATE INDEX idx_exchange_listings_tags ON exchange_listings USING GIN(tags);

-- Listing favorites
CREATE TABLE IF NOT EXISTS exchange_favorites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    listing_id UUID NOT NULL REFERENCES exchange_listings(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(listing_id, user_id)
);

CREATE INDEX idx_exchange_favorites_listing_id ON exchange_favorites(listing_id);
CREATE INDEX idx_exchange_favorites_user_id ON exchange_favorites(user_id);

-- Listing inquiries
CREATE TABLE IF NOT EXISTS exchange_inquiries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    listing_id UUID NOT NULL REFERENCES exchange_listings(id) ON DELETE CASCADE,
    inquirer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    seller_id UUID NOT NULL REFERENCES users(id),
    
    -- Inquiry details
    message TEXT NOT NULL,
    offer_amount NUMERIC(10,2),
    
    -- Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'responded', 'accepted', 'declined', 'withdrawn')),
    responded_at TIMESTAMPTZ,
    response_message TEXT,
    
    -- Contact sharing
    phone_shared BOOLEAN DEFAULT FALSE,
    email_shared BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_exchange_inquiries_listing_id ON exchange_inquiries(listing_id);
CREATE INDEX idx_exchange_inquiries_inquirer_id ON exchange_inquiries(inquirer_id);
CREATE INDEX idx_exchange_inquiries_seller_id ON exchange_inquiries(seller_id);
CREATE INDEX idx_exchange_inquiries_status ON exchange_inquiries(status);

-- Exchange transactions
CREATE TABLE IF NOT EXISTS exchange_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    listing_id UUID NOT NULL REFERENCES exchange_listings(id),
    buyer_id UUID NOT NULL REFERENCES users(id),
    seller_id UUID NOT NULL REFERENCES users(id),
    
    -- Transaction details
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('purchase', 'trade', 'free', 'rental', 'service')),
    amount NUMERIC(10,2),
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- Exchange specifics
    exchange_method VARCHAR(20) CHECK (exchange_method IN ('pickup', 'delivery', 'shipping')),
    exchange_date TIMESTAMPTZ,
    exchange_location TEXT,
    
    -- Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'disputed')),
    confirmed_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    cancelled_at TIMESTAMPTZ,
    cancellation_reason TEXT,
    
    -- Notes
    buyer_notes TEXT,
    seller_notes TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_exchange_transactions_listing_id ON exchange_transactions(listing_id);
CREATE INDEX idx_exchange_transactions_buyer_id ON exchange_transactions(buyer_id);
CREATE INDEX idx_exchange_transactions_seller_id ON exchange_transactions(seller_id);
CREATE INDEX idx_exchange_transactions_status ON exchange_transactions(status);
CREATE INDEX idx_exchange_transactions_exchange_date ON exchange_transactions(exchange_date);

-- Exchange reviews
CREATE TABLE IF NOT EXISTS exchange_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL REFERENCES exchange_transactions(id) ON DELETE CASCADE,
    reviewer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reviewed_user_id UUID NOT NULL REFERENCES users(id),
    
    -- Review details
    role VARCHAR(10) NOT NULL CHECK (role IN ('buyer', 'seller')),
    rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
    review_text TEXT,
    
    -- Review aspects
    communication_rating INTEGER CHECK (communication_rating BETWEEN 1 AND 5),
    accuracy_rating INTEGER CHECK (accuracy_rating BETWEEN 1 AND 5),
    punctuality_rating INTEGER CHECK (punctuality_rating BETWEEN 1 AND 5),
    overall_experience_rating INTEGER CHECK (overall_experience_rating BETWEEN 1 AND 5),
    
    -- Response
    response_text TEXT,
    response_at TIMESTAMPTZ,
    
    -- Status
    is_verified BOOLEAN DEFAULT FALSE,
    is_hidden BOOLEAN DEFAULT FALSE,
    hidden_reason TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(transaction_id, reviewer_id)
);

CREATE INDEX idx_exchange_reviews_transaction_id ON exchange_reviews(transaction_id);
CREATE INDEX idx_exchange_reviews_reviewer_id ON exchange_reviews(reviewer_id);
CREATE INDEX idx_exchange_reviews_reviewed_user_id ON exchange_reviews(reviewed_user_id);
CREATE INDEX idx_exchange_reviews_rating ON exchange_reviews(rating);

-- Skills directory
CREATE TABLE IF NOT EXISTS skills_directory (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    category VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    user_count INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_skills_directory_category ON skills_directory(category);
CREATE INDEX idx_skills_directory_user_count ON skills_directory(user_count DESC);

-- User skills for exchange
CREATE TABLE IF NOT EXISTS user_exchange_skills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    skill_id UUID NOT NULL REFERENCES skills_directory(id),
    
    -- Skill details
    proficiency_level VARCHAR(20) DEFAULT 'intermediate' CHECK (proficiency_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    years_experience INTEGER,
    description TEXT,
    
    -- Availability
    available_for_exchange BOOLEAN DEFAULT TRUE,
    availability_hours_per_week INTEGER,
    preferred_exchange_type VARCHAR(20) CHECK (preferred_exchange_type IN ('teach', 'learn', 'both')),
    
    -- Pricing
    rate_per_hour NUMERIC(10,2),
    rate_type VARCHAR(20) DEFAULT 'negotiable' CHECK (rate_type IN ('free', 'paid', 'exchange', 'negotiable')),
    
    -- Portfolio
    portfolio_urls TEXT[] DEFAULT '{}',
    certifications TEXT[] DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, skill_id)
);

CREATE INDEX idx_user_exchange_skills_user_id ON user_exchange_skills(user_id);
CREATE INDEX idx_user_exchange_skills_skill_id ON user_exchange_skills(skill_id);
CREATE INDEX idx_user_exchange_skills_available ON user_exchange_skills(available_for_exchange) WHERE available_for_exchange = TRUE;

-- Listing reports
CREATE TABLE IF NOT EXISTS exchange_listing_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    listing_id UUID NOT NULL REFERENCES exchange_listings(id) ON DELETE CASCADE,
    reported_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Report details
    reason VARCHAR(50) NOT NULL,
    description TEXT,
    
    -- Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'reviewing', 'resolved', 'dismissed')),
    resolved_by UUID REFERENCES users(id),
    resolved_at TIMESTAMPTZ,
    resolution_notes TEXT,
    action_taken VARCHAR(50),
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_exchange_listing_reports_listing_id ON exchange_listing_reports(listing_id);
CREATE INDEX idx_exchange_listing_reports_reported_by ON exchange_listing_reports(reported_by);
CREATE INDEX idx_exchange_listing_reports_status ON exchange_listing_reports(status);

-- Saved searches
CREATE TABLE IF NOT EXISTS exchange_saved_searches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Search criteria
    name VARCHAR(100) NOT NULL,
    category_ids UUID[] DEFAULT '{}',
    listing_types VARCHAR(20)[] DEFAULT '{}',
    exchange_types VARCHAR(20)[] DEFAULT '{}',
    keywords TEXT,
    min_price NUMERIC(10,2),
    max_price NUMERIC(10,2),
    location GEOGRAPHY(POINT, 4326),
    radius_km NUMERIC(5,2),
    tags TEXT[] DEFAULT '{}',
    
    -- Notifications
    notify_enabled BOOLEAN DEFAULT TRUE,
    notify_frequency VARCHAR(20) DEFAULT 'daily' CHECK (notify_frequency IN ('instant', 'daily', 'weekly')),
    last_notified_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_exchange_saved_searches_user_id ON exchange_saved_searches(user_id);
CREATE INDEX idx_exchange_saved_searches_notify_enabled ON exchange_saved_searches(notify_enabled) WHERE notify_enabled = TRUE;

-- Functions and Triggers

-- Update favorite counts
CREATE OR REPLACE FUNCTION update_exchange_favorite_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE exchange_listings
    SET favorite_count = (
        SELECT COUNT(*)
        FROM exchange_favorites
        WHERE listing_id = COALESCE(NEW.listing_id, OLD.listing_id)
    )
    WHERE id = COALESCE(NEW.listing_id, OLD.listing_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_favorite_count_trigger
    AFTER INSERT OR DELETE ON exchange_favorites
    FOR EACH ROW EXECUTE FUNCTION update_exchange_favorite_count();

-- Update inquiry counts
CREATE OR REPLACE FUNCTION update_exchange_inquiry_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE exchange_listings
    SET inquiry_count = (
        SELECT COUNT(*)
        FROM exchange_inquiries
        WHERE listing_id = NEW.listing_id
    )
    WHERE id = NEW.listing_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_inquiry_count_trigger
    AFTER INSERT ON exchange_inquiries
    FOR EACH ROW EXECUTE FUNCTION update_exchange_inquiry_count();

-- Update category item counts
CREATE OR REPLACE FUNCTION update_category_item_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Update for new category
    IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND NEW.category_id != OLD.category_id) THEN
        UPDATE exchange_categories
        SET item_count = item_count + 1
        WHERE id = NEW.category_id;
    END IF;
    
    -- Update for old category
    IF TG_OP = 'DELETE' OR (TG_OP = 'UPDATE' AND NEW.category_id != OLD.category_id) THEN
        UPDATE exchange_categories
        SET item_count = GREATEST(0, item_count - 1)
        WHERE id = OLD.category_id;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_category_count_trigger
    AFTER INSERT OR UPDATE OF category_id OR DELETE ON exchange_listings
    FOR EACH ROW
    WHEN (NEW.status = 'active' OR OLD.status = 'active')
    EXECUTE FUNCTION update_category_item_count();

-- Auto-expire listings
CREATE OR REPLACE FUNCTION expire_old_listings()
RETURNS VOID AS $$
BEGIN
    UPDATE exchange_listings
    SET status = 'expired'
    WHERE status = 'active'
    AND available_until < NOW();
END;
$$ LANGUAGE plpgsql;

-- Calculate user exchange rating
CREATE OR REPLACE FUNCTION calculate_user_exchange_rating(p_user_id UUID)
RETURNS TABLE (
    average_rating NUMERIC,
    total_reviews INTEGER,
    as_buyer_rating NUMERIC,
    as_seller_rating NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ROUND(AVG(r.rating), 2) as average_rating,
        COUNT(*)::INTEGER as total_reviews,
        ROUND(AVG(CASE WHEN r.role = 'buyer' THEN r.rating END), 2) as as_buyer_rating,
        ROUND(AVG(CASE WHEN r.role = 'seller' THEN r.rating END), 2) as as_seller_rating
    FROM exchange_reviews r
    WHERE r.reviewed_user_id = p_user_id
    AND r.is_hidden = FALSE;
END;
$$ LANGUAGE plpgsql;

-- Update timestamps
CREATE TRIGGER update_exchange_categories_updated_at BEFORE UPDATE ON exchange_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_exchange_listings_updated_at BEFORE UPDATE ON exchange_listings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_exchange_inquiries_updated_at BEFORE UPDATE ON exchange_inquiries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_exchange_transactions_updated_at BEFORE UPDATE ON exchange_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_exchange_reviews_updated_at BEFORE UPDATE ON exchange_reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_exchange_skills_updated_at BEFORE UPDATE ON user_exchange_skills
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_exchange_saved_searches_updated_at BEFORE UPDATE ON exchange_saved_searches
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments
COMMENT ON TABLE exchange_categories IS 'Categories for exchange listings';
COMMENT ON TABLE exchange_listings IS 'Items, services, and skills listings';
COMMENT ON TABLE exchange_favorites IS 'User favorites/watchlist';
COMMENT ON TABLE exchange_inquiries IS 'Inquiries about listings';
COMMENT ON TABLE exchange_transactions IS 'Completed exchanges/transactions';
COMMENT ON TABLE exchange_reviews IS 'Reviews for exchanges';
COMMENT ON TABLE skills_directory IS 'Master list of skills';
COMMENT ON TABLE user_exchange_skills IS 'User skills available for exchange';
COMMENT ON TABLE exchange_listing_reports IS 'Reported listings';
COMMENT ON TABLE exchange_saved_searches IS 'Saved search alerts';