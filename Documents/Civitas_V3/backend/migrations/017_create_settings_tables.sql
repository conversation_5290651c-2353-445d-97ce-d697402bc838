-- Settings System Tables

-- User settings with JSONB storage
CREATE TABLE IF NOT EXISTS user_settings (
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    category VARCHAR(50) NOT NULL CHECK (category IN ('notifications', 'privacy', 'display', 'accessibility')),
    settings JSONB NOT NULL DEFAULT '{}',
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (user_id, category)
);

-- Session management for multi-device support
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL UNIQUE,
    device_type VARCHAR(50),
    device_name VARCHAR(255),
    device_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    location_city VARCHAR(100),
    location_country VARCHAR(2),
    last_active TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Two-factor authentication settings
CREATE TABLE IF NOT EXISTS user_2fa_settings (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    secret VARCHAR(255) NOT NULL,
    backup_codes TEXT[] DEFAULT '{}',
    is_enabled BOOLEAN DEFAULT FALSE,
    enabled_at TIMESTAMPTZ,
    last_used_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Data export requests for GDPR compliance
CREATE TABLE IF NOT EXISTS data_export_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    export_type VARCHAR(50) NOT NULL CHECK (export_type IN ('settings', 'personal_data', 'activity', 'full')),
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'expired')),
    file_url VARCHAR(500),
    file_size BIGINT,
    expires_at TIMESTAMPTZ,
    error_message TEXT,
    requested_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- Quick toggle history for analytics
CREATE TABLE IF NOT EXISTS settings_toggle_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    toggle_type VARCHAR(50) NOT NULL,
    old_value JSONB,
    new_value JSONB,
    toggled_at TIMESTAMPTZ DEFAULT NOW()
);

-- Default settings templates
CREATE TABLE IF NOT EXISTS default_settings_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    settings JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id) WHERE is_active = TRUE;
CREATE INDEX idx_user_sessions_token_hash ON user_sessions(token_hash) WHERE is_active = TRUE;
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at) WHERE is_active = TRUE;
CREATE INDEX idx_data_export_requests_user_id ON data_export_requests(user_id);
CREATE INDEX idx_data_export_requests_status ON data_export_requests(status) WHERE status IN ('pending', 'processing');
CREATE INDEX idx_settings_toggle_history_user_id ON settings_toggle_history(user_id);
CREATE INDEX idx_settings_toggle_history_toggled_at ON settings_toggle_history(toggled_at);

-- Triggers
CREATE OR REPLACE FUNCTION update_user_settings_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_settings_timestamp
    BEFORE UPDATE ON user_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_user_settings_timestamp();

-- Function to clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions
    WHERE expires_at < NOW() OR (is_active = FALSE AND last_active < NOW() - INTERVAL '30 days');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get user's active sessions count
CREATE OR REPLACE FUNCTION get_active_sessions_count(p_user_id UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)
        FROM user_sessions
        WHERE user_id = p_user_id
        AND is_active = TRUE
        AND expires_at > NOW()
    );
END;
$$ LANGUAGE plpgsql;

-- Insert default settings templates
INSERT INTO default_settings_templates (name, category, settings) VALUES
('Default Notifications', 'notifications', '{
    "messages": true,
    "post_interactions": true,
    "event_reminders": true,
    "help_requests": true,
    "civic_updates": true,
    "push_enabled": true,
    "push_messages": true,
    "push_urgent_only": false,
    "email_enabled": true,
    "email_digest": "daily",
    "email_messages": false,
    "email_events": true,
    "sms_enabled": false,
    "sms_urgent_only": true
}'),
('Default Privacy', 'privacy', '{
    "profile_visibility": "neighbors",
    "show_online_status": true,
    "allow_messages_from": "neighbors",
    "share_location": true,
    "location_precision": "approximate",
    "show_activity_status": true,
    "allow_search_engines": false,
    "data_collection": true,
    "share_with_partners": false
}'),
('Default Display', 'display', '{
    "theme": "light",
    "language": "en",
    "timezone": "America/New_York",
    "date_format": "MM/DD/YYYY",
    "time_format": "12h",
    "feed_density": "comfortable",
    "show_images": true,
    "autoplay_videos": false,
    "font_size": "medium"
}'),
('Default Accessibility', 'accessibility', '{
    "high_contrast": false,
    "reduce_motion": false,
    "screen_reader_mode": false,
    "keyboard_navigation": true,
    "alt_text_preference": "detailed",
    "captions_enabled": true,
    "audio_descriptions": false,
    "focus_indicators": true,
    "color_blind_mode": null
}');

-- Comments
COMMENT ON TABLE user_settings IS 'Stores user preferences by category using JSONB';
COMMENT ON TABLE user_sessions IS 'Tracks active user sessions across devices';
COMMENT ON TABLE user_2fa_settings IS 'Two-factor authentication configuration';
COMMENT ON TABLE data_export_requests IS 'GDPR-compliant data export tracking';
COMMENT ON TABLE settings_toggle_history IS 'Analytics for quick toggle usage';
COMMENT ON TABLE default_settings_templates IS 'Default settings for new users';