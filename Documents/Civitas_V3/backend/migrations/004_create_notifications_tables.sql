-- Notifications System Tables
-- Push notifications, in-app notifications, and notification preferences

-- Main notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Notification content
    type notification_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    
    -- Rich content
    image_url VARCHAR(500),
    action_url VARCHAR(500),
    data JSONB DEFAULT '{}',
    
    -- Related entities
    entity_type VARCHAR(50), -- 'post', 'event', 'user', 'emergency', etc.
    entity_id UUID,
    actor_id UUID REFERENCES users(id), -- User who triggered the notification
    
    -- Status
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMPTZ,
    is_seen BOOLEAN DEFAULT FALSE,
    seen_at TIMESTAMPTZ,
    
    -- Delivery channels
    delivered_push BOOLEAN DEFAULT FALSE,
    delivered_email BOOLEAN DEFAULT FALSE,
    delivered_sms BOOLEAN DEFAULT FALSE,
    delivered_in_app BOOLEAN DEFAULT TRUE,
    
    -- Priority and expiration
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    expires_at TIMESTAMPTZ,
    
    -- Grouping
    group_key VARCHAR(100), -- For grouping similar notifications
    group_count INTEGER DEFAULT 1,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(user_id, is_read) WHERE is_read = FALSE;
CREATE INDEX idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX idx_notifications_entity ON notifications(entity_type, entity_id);
CREATE INDEX idx_notifications_actor_id ON notifications(actor_id);
CREATE INDEX idx_notifications_group_key ON notifications(user_id, group_key) WHERE group_key IS NOT NULL;

-- Notification preferences
CREATE TABLE IF NOT EXISTS notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Channel preferences
    push_enabled BOOLEAN DEFAULT TRUE,
    email_enabled BOOLEAN DEFAULT TRUE,
    sms_enabled BOOLEAN DEFAULT FALSE,
    in_app_enabled BOOLEAN DEFAULT TRUE,
    
    -- Category preferences (JSON for flexibility)
    category_preferences JSONB DEFAULT '{
        "messages": {"push": true, "email": false, "sms": false},
        "post_interactions": {"push": true, "email": false, "sms": false},
        "event_reminders": {"push": true, "email": true, "sms": false},
        "help_requests": {"push": true, "email": true, "sms": true},
        "emergency": {"push": true, "email": true, "sms": true},
        "civic_updates": {"push": true, "email": true, "sms": false},
        "daily_kindness": {"push": true, "email": false, "sms": false}
    }',
    
    -- Quiet hours
    quiet_hours_enabled BOOLEAN DEFAULT FALSE,
    quiet_hours_start TIME DEFAULT '22:00',
    quiet_hours_end TIME DEFAULT '08:00',
    quiet_hours_days INTEGER[] DEFAULT '{0,1,2,3,4,5,6}', -- 0=Sunday, 6=Saturday
    
    -- Digest preferences
    email_digest_enabled BOOLEAN DEFAULT TRUE,
    email_digest_frequency VARCHAR(20) DEFAULT 'daily' CHECK (email_digest_frequency IN ('never', 'daily', 'weekly', 'monthly')),
    last_digest_sent_at TIMESTAMPTZ,
    
    -- Other preferences
    language VARCHAR(5) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'America/New_York',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id)
);

CREATE INDEX idx_notification_preferences_user_id ON notification_preferences(user_id);

-- Push notification tokens
CREATE TABLE IF NOT EXISTS push_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Token information
    token VARCHAR(500) NOT NULL UNIQUE,
    provider VARCHAR(20) NOT NULL CHECK (provider IN ('fcm', 'apns', 'web')),
    
    -- Device information
    device_id VARCHAR(255),
    device_type VARCHAR(50), -- 'ios', 'android', 'web'
    device_name VARCHAR(255),
    device_model VARCHAR(100),
    os_version VARCHAR(50),
    app_version VARCHAR(50),
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    last_used_at TIMESTAMPTZ DEFAULT NOW(),
    failure_count INTEGER DEFAULT 0,
    last_failure_at TIMESTAMPTZ,
    last_failure_reason TEXT,
    
    -- Location info for timezone
    timezone VARCHAR(50),
    locale VARCHAR(10),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_push_tokens_user_id ON push_tokens(user_id);
CREATE INDEX idx_push_tokens_token ON push_tokens(token);
CREATE INDEX idx_push_tokens_is_active ON push_tokens(is_active) WHERE is_active = TRUE;

-- Notification templates
CREATE TABLE IF NOT EXISTS notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    type notification_type NOT NULL,
    
    -- Template content with variables
    title_template TEXT NOT NULL,
    body_template TEXT NOT NULL,
    
    -- Channel-specific templates
    email_subject_template TEXT,
    email_body_template TEXT,
    sms_template TEXT,
    
    -- Default data
    default_image_url VARCHAR(500),
    default_action_url VARCHAR(500),
    default_data JSONB DEFAULT '{}',
    
    -- Settings
    is_active BOOLEAN DEFAULT TRUE,
    priority VARCHAR(20) DEFAULT 'normal',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_notification_templates_name ON notification_templates(name);
CREATE INDEX idx_notification_templates_type ON notification_templates(type);

-- Notification queue for batch processing
CREATE TABLE IF NOT EXISTS notification_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
    
    -- Delivery channels
    channel VARCHAR(20) NOT NULL CHECK (channel IN ('push', 'email', 'sms')),
    
    -- Processing status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'sent', 'failed', 'cancelled')),
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    
    -- Scheduling
    scheduled_for TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    
    -- Error tracking
    last_error TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_notification_queue_status ON notification_queue(status, scheduled_for) WHERE status IN ('pending', 'processing');
CREATE INDEX idx_notification_queue_user_id ON notification_queue(user_id);
CREATE INDEX idx_notification_queue_notification_id ON notification_queue(notification_id);

-- Daily kindness suggestions
CREATE TABLE IF NOT EXISTS daily_kindness (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Suggestion content
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50) NOT NULL,
    difficulty VARCHAR(20) DEFAULT 'easy' CHECK (difficulty IN ('easy', 'medium', 'hard')),
    estimated_time_minutes INTEGER,
    
    -- Display settings
    icon VARCHAR(50),
    color VARCHAR(7),
    image_url VARCHAR(500),
    
    -- Targeting
    min_user_level INTEGER DEFAULT 1,
    max_user_level INTEGER,
    seasons VARCHAR(20)[] DEFAULT '{}', -- 'spring', 'summer', 'fall', 'winter'
    weather_conditions VARCHAR(50)[] DEFAULT '{}', -- 'sunny', 'rainy', 'snow', etc.
    
    -- Stats
    times_suggested INTEGER DEFAULT 0,
    times_completed INTEGER DEFAULT 0,
    average_rating NUMERIC(3,2),
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_daily_kindness_is_active ON daily_kindness(is_active);
CREATE INDEX idx_daily_kindness_category ON daily_kindness(category);

-- User daily kindness tracking
CREATE TABLE IF NOT EXISTS user_daily_kindness (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    kindness_id UUID NOT NULL REFERENCES daily_kindness(id),
    
    -- Status
    suggested_date DATE NOT NULL DEFAULT CURRENT_DATE,
    completed_at TIMESTAMPTZ,
    skipped_at TIMESTAMPTZ,
    
    -- Feedback
    rating INTEGER CHECK (rating BETWEEN 1 AND 5),
    feedback TEXT,
    
    -- Points awarded
    points_earned INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, kindness_id, suggested_date)
);

CREATE INDEX idx_user_daily_kindness_user_id ON user_daily_kindness(user_id);
CREATE INDEX idx_user_daily_kindness_suggested_date ON user_daily_kindness(suggested_date);

-- Email notification log
CREATE TABLE IF NOT EXISTS email_notification_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Email details
    to_email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    
    -- Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'bounced', 'failed')),
    
    -- Provider info
    provider VARCHAR(50) DEFAULT 'sendgrid',
    provider_message_id VARCHAR(255),
    
    -- Tracking
    sent_at TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    opened_at TIMESTAMPTZ,
    clicked_at TIMESTAMPTZ,
    
    -- Error info
    error_message TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_email_notification_log_notification_id ON email_notification_log(notification_id);
CREATE INDEX idx_email_notification_log_user_id ON email_notification_log(user_id);
CREATE INDEX idx_email_notification_log_status ON email_notification_log(status);

-- SMS notification log
CREATE TABLE IF NOT EXISTS sms_notification_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- SMS details
    to_phone VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    
    -- Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed')),
    
    -- Provider info
    provider VARCHAR(50) DEFAULT 'twilio',
    provider_message_id VARCHAR(255),
    
    -- Tracking
    sent_at TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    
    -- Error info
    error_code VARCHAR(50),
    error_message TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_sms_notification_log_notification_id ON sms_notification_log(notification_id);
CREATE INDEX idx_sms_notification_log_user_id ON sms_notification_log(user_id);
CREATE INDEX idx_sms_notification_log_status ON sms_notification_log(status);

-- Functions and Triggers

-- Mark notifications as read
CREATE OR REPLACE FUNCTION mark_notifications_read(
    p_user_id UUID,
    p_notification_ids UUID[]
) RETURNS INTEGER AS $$
DECLARE
    v_updated_count INTEGER;
BEGIN
    UPDATE notifications
    SET is_read = TRUE,
        read_at = NOW()
    WHERE user_id = p_user_id
    AND id = ANY(p_notification_ids)
    AND is_read = FALSE;
    
    GET DIAGNOSTICS v_updated_count = ROW_COUNT;
    RETURN v_updated_count;
END;
$$ LANGUAGE plpgsql;

-- Get unread notification count
CREATE OR REPLACE FUNCTION get_unread_notification_count(p_user_id UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)
        FROM notifications
        WHERE user_id = p_user_id
        AND is_read = FALSE
        AND (expires_at IS NULL OR expires_at > NOW())
    );
END;
$$ LANGUAGE plpgsql;

-- Clean up expired notifications
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS INTEGER AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    DELETE FROM notifications
    WHERE expires_at < NOW()
    AND is_read = TRUE;
    
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Update timestamps
CREATE TRIGGER update_notifications_updated_at BEFORE UPDATE ON notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_preferences_updated_at BEFORE UPDATE ON notification_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_push_tokens_updated_at BEFORE UPDATE ON push_tokens
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_templates_updated_at BEFORE UPDATE ON notification_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_daily_kindness_updated_at BEFORE UPDATE ON daily_kindness
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments
COMMENT ON TABLE notifications IS 'User notifications across all channels';
COMMENT ON TABLE notification_preferences IS 'User notification preferences and settings';
COMMENT ON TABLE push_tokens IS 'Push notification device tokens';
COMMENT ON TABLE notification_templates IS 'Reusable notification templates';
COMMENT ON TABLE notification_queue IS 'Queue for batch notification processing';
COMMENT ON TABLE daily_kindness IS 'Daily kindness act suggestions';
COMMENT ON TABLE user_daily_kindness IS 'User participation in daily kindness';
COMMENT ON TABLE email_notification_log IS 'Email delivery tracking';
COMMENT ON TABLE sms_notification_log IS 'SMS delivery tracking';