-- Events System Tables
-- Community events, RSVPs, and event management

-- Main events table
CREATE TABLE IF NOT EXISTS events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    host_id UUID NOT NULL REFERENCES users(id),
    neighborhood_id UUID NOT NULL REFERENCES neighborhoods(id),
    
    -- Event details
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50) NOT NULL CHECK (category IN ('social', 'educational', 'volunteer', 'sports', 'cultural', 'civic', 'family', 'other')),
    
    -- Timing
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ NOT NULL,
    timezone VARCHAR(50) DEFAULT 'America/New_York',
    is_all_day BOOLEAN DEFAULT FALSE,
    
    -- Location
    location_type VARCHAR(20) DEFAULT 'physical' CHECK (location_type IN ('physical', 'virtual', 'hybrid')),
    location_name VARCHAR(255),
    location_address TEXT,
    location GEOGRAPHY(POINT, 4326),
    virtual_meeting_url VARCHAR(500),
    virtual_meeting_details JSONB DEFAULT '{}',
    
    -- Capacity and registration
    max_attendees INTEGER,
    registration_required BOOLEAN DEFAULT FALSE,
    registration_deadline TIMESTAMPTZ,
    registration_url VARCHAR(500),
    
    -- Cost
    is_free BOOLEAN DEFAULT TRUE,
    cost_amount NUMERIC(10,2),
    cost_description TEXT,
    
    -- Visibility and privacy
    visibility VARCHAR(20) DEFAULT 'neighborhood' CHECK (visibility IN ('public', 'neighborhood', 'invited_only', 'private')),
    is_approved BOOLEAN DEFAULT TRUE,
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMPTZ,
    
    -- Media
    cover_image_url VARCHAR(500),
    media_urls TEXT[] DEFAULT '{}',
    
    -- Engagement metrics
    rsvp_count INTEGER DEFAULT 0,
    interested_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    share_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    
    -- Tags
    tags TEXT[] DEFAULT '{}',
    
    -- Recurring event support
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_rule VARCHAR(500), -- RFC 5545 RRULE format
    parent_event_id UUID REFERENCES events(id),
    recurrence_exceptions DATE[] DEFAULT '{}',
    
    -- Status
    status VARCHAR(20) DEFAULT 'upcoming' CHECK (status IN ('draft', 'upcoming', 'ongoing', 'completed', 'cancelled')),
    is_cancelled BOOLEAN DEFAULT FALSE,
    cancelled_at TIMESTAMPTZ,
    cancellation_reason TEXT,
    
    -- Additional settings
    allow_comments BOOLEAN DEFAULT TRUE,
    allow_photos BOOLEAN DEFAULT TRUE,
    reminder_settings JSONB DEFAULT '{"24h": true, "1h": true}',
    
    -- Metadata
    source VARCHAR(50) DEFAULT 'app',
    external_id VARCHAR(255),
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_events_host_id ON events(host_id);
CREATE INDEX idx_events_neighborhood_id ON events(neighborhood_id);
CREATE INDEX idx_events_start_time ON events(start_time);
CREATE INDEX idx_events_status ON events(status);
CREATE INDEX idx_events_category ON events(category);
CREATE INDEX idx_events_location ON events USING GIST(location);
CREATE INDEX idx_events_parent_event_id ON events(parent_event_id) WHERE parent_event_id IS NOT NULL;
CREATE INDEX idx_events_tags ON events USING GIN(tags);

-- Event RSVPs
CREATE TABLE IF NOT EXISTS event_rsvps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- RSVP status
    status VARCHAR(20) NOT NULL DEFAULT 'going' CHECK (status IN ('going', 'maybe', 'not_going')),
    
    -- Guest count
    guest_count INTEGER DEFAULT 0,
    guest_names TEXT[] DEFAULT '{}',
    
    -- Additional info
    dietary_restrictions TEXT,
    accessibility_needs TEXT,
    notes TEXT,
    
    -- Notifications
    reminder_sent BOOLEAN DEFAULT FALSE,
    reminder_sent_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(event_id, user_id)
);

CREATE INDEX idx_event_rsvps_event_id ON event_rsvps(event_id);
CREATE INDEX idx_event_rsvps_user_id ON event_rsvps(user_id);
CREATE INDEX idx_event_rsvps_status ON event_rsvps(status);

-- Event comments
CREATE TABLE IF NOT EXISTS event_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    parent_comment_id UUID REFERENCES event_comments(id) ON DELETE CASCADE,
    
    -- Comment content
    content TEXT NOT NULL,
    
    -- Media
    media_urls TEXT[] DEFAULT '{}',
    
    -- Engagement
    like_count INTEGER DEFAULT 0,
    
    -- Status
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMPTZ,
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_event_comments_event_id ON event_comments(event_id);
CREATE INDEX idx_event_comments_user_id ON event_comments(user_id);
CREATE INDEX idx_event_comments_parent_id ON event_comments(parent_comment_id);
CREATE INDEX idx_event_comments_created_at ON event_comments(created_at);

-- Event comment likes
CREATE TABLE IF NOT EXISTS event_comment_likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    comment_id UUID NOT NULL REFERENCES event_comments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(comment_id, user_id)
);

CREATE INDEX idx_event_comment_likes_comment_id ON event_comment_likes(comment_id);
CREATE INDEX idx_event_comment_likes_user_id ON event_comment_likes(user_id);

-- Event invitations
CREATE TABLE IF NOT EXISTS event_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    inviter_id UUID NOT NULL REFERENCES users(id),
    invitee_id UUID NOT NULL REFERENCES users(id),
    
    -- Invitation details
    message TEXT,
    
    -- Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
    responded_at TIMESTAMPTZ,
    
    -- Delivery
    sent_via VARCHAR(20) DEFAULT 'app' CHECK (sent_via IN ('app', 'email', 'sms')),
    sent_at TIMESTAMPTZ DEFAULT NOW(),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(event_id, invitee_id)
);

CREATE INDEX idx_event_invitations_event_id ON event_invitations(event_id);
CREATE INDEX idx_event_invitations_invitee_id ON event_invitations(invitee_id);
CREATE INDEX idx_event_invitations_status ON event_invitations(status);

-- Event check-ins
CREATE TABLE IF NOT EXISTS event_checkins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Check-in details
    checked_in_at TIMESTAMPTZ DEFAULT NOW(),
    checked_out_at TIMESTAMPTZ,
    
    -- Location verification
    checkin_location GEOGRAPHY(POINT, 4326),
    location_verified BOOLEAN DEFAULT FALSE,
    
    -- Guest info
    guest_count INTEGER DEFAULT 0,
    
    -- Notes
    notes TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(event_id, user_id)
);

CREATE INDEX idx_event_checkins_event_id ON event_checkins(event_id);
CREATE INDEX idx_event_checkins_user_id ON event_checkins(user_id);
CREATE INDEX idx_event_checkins_checked_in_at ON event_checkins(checked_in_at);

-- Event photos
CREATE TABLE IF NOT EXISTS event_photos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    uploaded_by UUID NOT NULL REFERENCES users(id),
    
    -- Photo details
    photo_url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    caption TEXT,
    
    -- Metadata
    width INTEGER,
    height INTEGER,
    file_size INTEGER,
    
    -- Engagement
    like_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    
    -- Privacy
    is_public BOOLEAN DEFAULT TRUE,
    
    -- Status
    is_approved BOOLEAN DEFAULT TRUE,
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMPTZ,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_event_photos_event_id ON event_photos(event_id);
CREATE INDEX idx_event_photos_uploaded_by ON event_photos(uploaded_by);
CREATE INDEX idx_event_photos_created_at ON event_photos(created_at DESC);

-- Event photo likes
CREATE TABLE IF NOT EXISTS event_photo_likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    photo_id UUID NOT NULL REFERENCES event_photos(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(photo_id, user_id)
);

CREATE INDEX idx_event_photo_likes_photo_id ON event_photo_likes(photo_id);
CREATE INDEX idx_event_photo_likes_user_id ON event_photo_likes(user_id);

-- Event categories and interests
CREATE TABLE IF NOT EXISTS event_interests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    category VARCHAR(50) NOT NULL,
    interest_level INTEGER DEFAULT 5 CHECK (interest_level BETWEEN 1 AND 10),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, category)
);

CREATE INDEX idx_event_interests_user_id ON event_interests(user_id);
CREATE INDEX idx_event_interests_category ON event_interests(category);

-- Event reminders
CREATE TABLE IF NOT EXISTS event_reminders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Reminder timing
    remind_at TIMESTAMPTZ NOT NULL,
    reminder_type VARCHAR(20) DEFAULT 'custom' CHECK (reminder_type IN ('24h', '1h', '30m', 'custom')),
    
    -- Status
    sent BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(event_id, user_id, reminder_type)
);

CREATE INDEX idx_event_reminders_remind_at ON event_reminders(remind_at) WHERE sent = FALSE;
CREATE INDEX idx_event_reminders_event_id ON event_reminders(event_id);
CREATE INDEX idx_event_reminders_user_id ON event_reminders(user_id);

-- Event reports
CREATE TABLE IF NOT EXISTS event_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    reported_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Report details
    reason VARCHAR(50) NOT NULL,
    description TEXT,
    
    -- Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'reviewing', 'resolved', 'dismissed')),
    resolved_by UUID REFERENCES users(id),
    resolved_at TIMESTAMPTZ,
    resolution_notes TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_event_reports_event_id ON event_reports(event_id);
CREATE INDEX idx_event_reports_reported_by ON event_reports(reported_by);
CREATE INDEX idx_event_reports_status ON event_reports(status);

-- Functions and Triggers

-- Update RSVP counts
CREATE OR REPLACE FUNCTION update_event_rsvp_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE events
    SET rsvp_count = (
        SELECT COUNT(*) + COALESCE(SUM(guest_count), 0)
        FROM event_rsvps
        WHERE event_id = COALESCE(NEW.event_id, OLD.event_id)
        AND status = 'going'
    ),
    interested_count = (
        SELECT COUNT(*)
        FROM event_rsvps
        WHERE event_id = COALESCE(NEW.event_id, OLD.event_id)
        AND status = 'maybe'
    )
    WHERE id = COALESCE(NEW.event_id, OLD.event_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_rsvp_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON event_rsvps
    FOR EACH ROW EXECUTE FUNCTION update_event_rsvp_count();

-- Update comment counts
CREATE OR REPLACE FUNCTION update_event_comment_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE events
    SET comment_count = (
        SELECT COUNT(*)
        FROM event_comments
        WHERE event_id = COALESCE(NEW.event_id, OLD.event_id)
        AND is_deleted = FALSE
    )
    WHERE id = COALESCE(NEW.event_id, OLD.event_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_comment_count_trigger
    AFTER INSERT OR UPDATE OF is_deleted OR DELETE ON event_comments
    FOR EACH ROW EXECUTE FUNCTION update_event_comment_count();

-- Update comment like counts
CREATE OR REPLACE FUNCTION update_event_comment_like_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE event_comments
    SET like_count = (
        SELECT COUNT(*)
        FROM event_comment_likes
        WHERE comment_id = COALESCE(NEW.comment_id, OLD.comment_id)
    )
    WHERE id = COALESCE(NEW.comment_id, OLD.comment_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_comment_like_count_trigger
    AFTER INSERT OR DELETE ON event_comment_likes
    FOR EACH ROW EXECUTE FUNCTION update_event_comment_like_count();

-- Update photo like counts
CREATE OR REPLACE FUNCTION update_event_photo_like_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE event_photos
    SET like_count = (
        SELECT COUNT(*)
        FROM event_photo_likes
        WHERE photo_id = COALESCE(NEW.photo_id, OLD.photo_id)
    )
    WHERE id = COALESCE(NEW.photo_id, OLD.photo_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_photo_like_count_trigger
    AFTER INSERT OR DELETE ON event_photo_likes
    FOR EACH ROW EXECUTE FUNCTION update_event_photo_like_count();

-- Auto-create reminders for RSVPs
CREATE OR REPLACE FUNCTION create_event_reminders()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'going' THEN
        -- Create 24h reminder
        INSERT INTO event_reminders (event_id, user_id, remind_at, reminder_type)
        SELECT NEW.event_id, NEW.user_id, 
               (SELECT start_time - INTERVAL '24 hours' FROM events WHERE id = NEW.event_id),
               '24h'
        WHERE EXISTS (
            SELECT 1 FROM events 
            WHERE id = NEW.event_id 
            AND start_time > NOW() + INTERVAL '24 hours'
        )
        ON CONFLICT (event_id, user_id, reminder_type) DO NOTHING;
        
        -- Create 1h reminder
        INSERT INTO event_reminders (event_id, user_id, remind_at, reminder_type)
        SELECT NEW.event_id, NEW.user_id,
               (SELECT start_time - INTERVAL '1 hour' FROM events WHERE id = NEW.event_id),
               '1h'
        WHERE EXISTS (
            SELECT 1 FROM events 
            WHERE id = NEW.event_id 
            AND start_time > NOW() + INTERVAL '1 hour'
        )
        ON CONFLICT (event_id, user_id, reminder_type) DO NOTHING;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER create_reminders_trigger
    AFTER INSERT OR UPDATE OF status ON event_rsvps
    FOR EACH ROW EXECUTE FUNCTION create_event_reminders();

-- Update event status
CREATE OR REPLACE FUNCTION update_event_status()
RETURNS VOID AS $$
BEGIN
    -- Mark events as ongoing
    UPDATE events
    SET status = 'ongoing'
    WHERE status = 'upcoming'
    AND start_time <= NOW()
    AND end_time > NOW();
    
    -- Mark events as completed
    UPDATE events
    SET status = 'completed'
    WHERE status IN ('upcoming', 'ongoing')
    AND end_time <= NOW();
END;
$$ LANGUAGE plpgsql;

-- Update timestamps
CREATE TRIGGER update_events_updated_at BEFORE UPDATE ON events
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_event_rsvps_updated_at BEFORE UPDATE ON event_rsvps
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_event_comments_updated_at BEFORE UPDATE ON event_comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_event_interests_updated_at BEFORE UPDATE ON event_interests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments
COMMENT ON TABLE events IS 'Community events and gatherings';
COMMENT ON TABLE event_rsvps IS 'Event attendance responses';
COMMENT ON TABLE event_comments IS 'Comments on events';
COMMENT ON TABLE event_comment_likes IS 'Likes on event comments';
COMMENT ON TABLE event_invitations IS 'Event invitations';
COMMENT ON TABLE event_checkins IS 'Event attendance tracking';
COMMENT ON TABLE event_photos IS 'Photos from events';
COMMENT ON TABLE event_photo_likes IS 'Likes on event photos';
COMMENT ON TABLE event_interests IS 'User event category preferences';
COMMENT ON TABLE event_reminders IS 'Event reminder scheduling';
COMMENT ON TABLE event_reports IS 'Reported events for moderation';