# WebSocket Events Documentation

## Overview
The Civitas WebSocket implementation uses Socket.IO for real-time communication. All connections require JWT authentication.

## Connection
```javascript
const socket = io('http://localhost:3000', {
  auth: {
    token: 'YOUR_JWT_TOKEN'
  }
});
```

## Common Events

### Connection Events
- `connect` - Successfully connected to server
- `disconnect` - Disconnected from server
- `error` - Error occurred (includes error message)
- `ping` - Server health check
- `pong` - Response to ping

## Emergency Events

### Client → Server

#### `join_emergency`
Join an emergency room to receive real-time updates.
```javascript
socket.emit('join_emergency', {
  emergency_id: 'uuid'
});
```

#### `leave_emergency`
Leave an emergency room.
```javascript
socket.emit('leave_emergency', {
  emergency_id: 'uuid'
});
```

#### `update_location`
Send location update during emergency.
```javascript
socket.emit('update_location', {
  emergency_id: 'uuid',
  latitude: 37.7749,
  longitude: -122.4194,
  accuracy: 10, // meters
  heading: 45, // degrees
  speed: 5 // m/s
});
```

#### `send_emergency_message`
Send a message in emergency chat.
```javascript
socket.emit('send_emergency_message', {
  emergency_id: 'uuid',
  message: 'Help is on the way',
  type: 'text', // 'text', 'image', 'audio'
  metadata: {} // optional
});
```

#### `update_eta`
Update responder ETA (responders only).
```javascript
socket.emit('update_eta', {
  emergency_id: 'uuid',
  eta_minutes: 5
});
```

#### `send_quick_reply`
Send a pre-defined quick reply.
```javascript
socket.emit('send_quick_reply', {
  emergency_id: 'uuid',
  reply_id: 'uuid'
});
```

### Server → Client

#### `new_emergency`
New emergency alert for responders.
```javascript
socket.on('new_emergency', (data) => {
  // data: { emergency, timestamp }
});
```

#### `emergency_alert`
Targeted emergency alert with distance.
```javascript
socket.on('emergency_alert', (data) => {
  // data: { emergency, distance, timestamp }
});
```

#### `emergency_update`
Emergency status update.
```javascript
socket.on('emergency_update', (data) => {
  // data: { type, data, timestamp }
});
```

#### `location_update`
Real-time location update from emergency participant.
```javascript
socket.on('location_update', (data) => {
  // data: { user_id, role, location, timestamp }
});
```

#### `emergency_message`
New message in emergency chat.
```javascript
socket.on('emergency_message', (data) => {
  // data: { id, sender_id, content, sender_role, timestamp }
});
```

#### `eta_update`
Responder ETA update.
```javascript
socket.on('eta_update', (data) => {
  // data: { responder_id, eta_minutes, timestamp }
});
```

## Message Events

### Client → Server

#### `join_conversation`
Join a conversation room.
```javascript
socket.emit('join_conversation', {
  conversation_id: 'uuid'
});
```

#### `leave_conversation`
Leave a conversation room.
```javascript
socket.emit('leave_conversation', {
  conversation_id: 'uuid'
});
```

#### `typing`
Send typing indicator.
```javascript
socket.emit('typing', {
  conversation_id: 'uuid',
  is_typing: true
});
```

#### `mark_read`
Mark message as read.
```javascript
socket.emit('mark_read', {
  conversation_id: 'uuid',
  message_id: 'uuid'
});
```

#### `request_messages`
Request message history.
```javascript
socket.emit('request_messages', {
  conversation_id: 'uuid',
  before: '2023-12-01T00:00:00Z', // optional
  limit: 50 // optional, default 50
});
```

### Server → Client

#### `new_message`
New message received.
```javascript
socket.on('new_message', (data) => {
  // data: { id, sender_id, content, media, timestamp }
});
```

#### `message_edited`
Message was edited.
```javascript
socket.on('message_edited', (data) => {
  // data: { conversation_id, message_id, content, edited_at }
});
```

#### `message_deleted`
Message was deleted.
```javascript
socket.on('message_deleted', (data) => {
  // data: { conversation_id, message_id, timestamp }
});
```

#### `typing_indicator`
Someone is typing.
```javascript
socket.on('typing_indicator', (data) => {
  // data: { user_id, user_name, user_avatar, is_typing, conversation_id }
});
```

#### `message_read`
Message read receipt.
```javascript
socket.on('message_read', (data) => {
  // data: { user_id, conversation_id, message_id, timestamp }
});
```

## Location Events

### Client → Server

#### `start_location_sharing`
Start sharing location.
```javascript
socket.emit('start_location_sharing', {
  duration_minutes: 30,
  share_with: 'neighborhood' // or 'specific_users'
});
```

#### `stop_location_sharing`
Stop sharing location.
```javascript
socket.emit('stop_location_sharing');
```

#### `update_shared_location`
Update shared location.
```javascript
socket.emit('update_shared_location', {
  latitude: 37.7749,
  longitude: -122.4194,
  accuracy: 10,
  heading: 45,
  speed: 5
});
```

#### `share_location_with_users`
Share location with specific users.
```javascript
socket.emit('share_location_with_users', {
  user_ids: ['uuid1', 'uuid2'],
  duration_minutes: 30
});
```

#### `confirm_safe_arrival`
Confirm safe arrival at destination.
```javascript
socket.emit('confirm_safe_arrival', {
  destination: 'Home',
  shared_with_users: ['uuid1', 'uuid2']
});
```

### Server → Client

#### `neighbor_started_sharing`
Neighbor started location sharing.
```javascript
socket.on('neighbor_started_sharing', (data) => {
  // data: { user_id, user_name, expires_at }
});
```

#### `neighbor_stopped_sharing`
Neighbor stopped location sharing.
```javascript
socket.on('neighbor_stopped_sharing', (data) => {
  // data: { user_id, user_name, reason }
});
```

#### `neighbor_location_update`
Neighbor location update.
```javascript
socket.on('neighbor_location_update', (data) => {
  // data: { user_id, user_name, location, timestamp }
});
```

#### `location_shared_with_you`
Someone shared their location with you.
```javascript
socket.on('location_shared_with_you', (data) => {
  // data: { user_id, user_name, expires_at }
});
```

#### `user_arrived_safely`
User confirmed safe arrival.
```javascript
socket.on('user_arrived_safely', (data) => {
  // data: { user_id, user_name, destination, timestamp }
});
```

## Notification Events

### Client → Server

#### `subscribe_notifications`
Subscribe to notification updates.
```javascript
socket.emit('subscribe_notifications');
```

#### `mark_notification_read`
Mark notification as read.
```javascript
socket.emit('mark_notification_read', {
  notification_id: 'uuid'
});
```

#### `mark_all_notifications_read`
Mark all notifications as read.
```javascript
socket.emit('mark_all_notifications_read');
```

#### `delete_notification`
Delete a notification.
```javascript
socket.emit('delete_notification', {
  notification_id: 'uuid'
});
```

#### `update_notification_preferences`
Update notification preferences.
```javascript
socket.emit('update_notification_preferences', {
  preferences: {
    push_enabled: true,
    email_enabled: false,
    sms_enabled: false,
    quiet_hours_start: '22:00',
    quiet_hours_end: '08:00',
    notification_types: ['emergency', 'message', 'event']
  }
});
```

### Server → Client

#### `new_notification`
New notification received.
```javascript
socket.on('new_notification', (data) => {
  // data: { id, type, title, body, data, timestamp }
});
```

#### `notification_count_update`
Unread notification count changed.
```javascript
socket.on('notification_count_update', (data) => {
  // data: { unread_count }
});
```

#### `neighborhood_notification`
Neighborhood-wide notification.
```javascript
socket.on('neighborhood_notification', (data) => {
  // data: { title, body, neighborhood_id, timestamp }
});
```

#### `action_required`
Notification requiring user action.
```javascript
socket.on('action_required', (data) => {
  // data: { notification_id, actions, timestamp }
});
```

## Room Structure

Users automatically join these rooms upon connection:
- `user:{userId}` - Personal notifications
- `neighborhood:{neighborhoodId}` - Neighborhood updates
- `responders:{neighborhoodId}` - Emergency responders only

Additional rooms joined on demand:
- `emergency:{emergencyId}` - Active emergency participants
- `conversation:{conversationId}` - Active conversation participants
- `location:neighborhood:{neighborhoodId}` - Location sharing participants
- `notifications:{userId}` - Notification subscribers

## Error Handling

All errors follow this format:
```javascript
socket.on('error', (error) => {
  // error: { message: 'Error description' }
});
```

## Best Practices

1. **Always handle disconnections gracefully**
   ```javascript
   socket.on('disconnect', () => {
     // Clean up UI, show offline status
   });
   ```

2. **Implement reconnection logic**
   ```javascript
   socket.on('connect', () => {
     // Re-join rooms, refresh data
   });
   ```

3. **Use acknowledgments for critical events**
   ```javascript
   socket.emit('send_emergency_message', data, (ack) => {
     if (ack.success) {
       // Message sent successfully
     }
   });
   ```

4. **Handle network issues**
   ```javascript
   socket.on('connect_error', (error) => {
     // Show connection error to user
   });
   ```

5. **Clean up event listeners**
   ```javascript
   // When component unmounts
   socket.off('new_message');
   socket.off('typing_indicator');
   ```