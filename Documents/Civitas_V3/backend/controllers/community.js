const { query, withTransaction } = require('../config/database');
const { cache } = require('../config/redis');
const { AppError, asyncHandler } = require('../middleware/errorHandler');
const logger = require('../config/logger');
const { sendRealtimeNotification } = require('../websocket/notifications');

// Get community forums
const getForums = asyncHandler(async (req, res) => {
  const neighborhoodId = req.user.neighborhood_id;
  
  const forumsResult = await query(
    `SELECT f.*, 
            COUNT(DISTINCT t.id) as topic_count,
            COUNT(DISTINCT t.id) FILTER (WHERE t.created_at > NOW() - INTERVAL '7 days') as topics_this_week
     FROM community_forums f
     LEFT JOIN community_topics t ON f.id = t.forum_id AND t.is_deleted = false
     WHERE f.neighborhood_id = $1 AND f.is_active = true
     GROUP BY f.id
     ORDER BY f.position ASC, f.name ASC`,
    [neighborhoodId]
  );

  res.json({
    success: true,
    data: forumsResult.rows
  });
});

// Create discussion topic
const createTopic = asyncHandler(async (req, res) => {
  const { forumId } = req.params;
  const userId = req.user.id;
  const neighborhoodId = req.user.neighborhood_id;
  const { title, content, tags = [] } = req.body;

  // Verify forum exists and is active
  const forumCheck = await query(
    'SELECT id, is_moderated FROM community_forums WHERE id = $1 AND neighborhood_id = $2 AND is_active = true',
    [forumId, neighborhoodId]
  );

  if (forumCheck.rows.length === 0) {
    throw new AppError('Forum not found', 404);
  }

  const forum = forumCheck.rows[0];

  const topicResult = await query(
    `INSERT INTO community_topics 
     (forum_id, user_id, neighborhood_id, title, content, tags)
     VALUES ($1, $2, $3, $4, $5, $6)
     RETURNING *`,
    [forumId, userId, neighborhoodId, title, content, tags]
  );

  const topic = topicResult.rows[0];

  // Create activity
  await query(
    `INSERT INTO user_activities 
     (user_id, activity_type, activity_data, neighborhood_id)
     VALUES ($1, 'community_topic_created', $2, $3)`,
    [userId, { topic_id: topic.id, forum_id: forumId, title }, neighborhoodId]
  );

  res.status(201).json({
    success: true,
    data: topic
  });
});

// Get topic with replies
const getTopic = asyncHandler(async (req, res) => {
  const { topicId } = req.params;
  const userId = req.user.id;

  // Get topic details
  const topicResult = await query(
    `SELECT t.*, 
            u.first_name, u.last_name, u.username, u.profile_photo_url,
            f.name as forum_name, f.slug as forum_slug
     FROM community_topics t
     JOIN users u ON t.user_id = u.id
     JOIN community_forums f ON t.forum_id = f.id
     WHERE t.id = $1 AND t.is_deleted = false`,
    [topicId]
  );

  if (topicResult.rows.length === 0) {
    throw new AppError('Topic not found', 404);
  }

  const topic = topicResult.rows[0];

  // Increment view count
  await query(
    'UPDATE community_topics SET view_count = view_count + 1 WHERE id = $1',
    [topicId]
  );

  // Get replies with nested structure
  const repliesResult = await query(
    `WITH RECURSIVE reply_tree AS (
      SELECT r.*, 
             u.first_name, u.last_name, u.username, u.profile_photo_url,
             0 as depth,
             ARRAY[r.created_at] as path
      FROM community_replies r
      JOIN users u ON r.user_id = u.id
      WHERE r.topic_id = $1 AND r.parent_reply_id IS NULL AND r.is_deleted = false
      
      UNION ALL
      
      SELECT r.*, 
             u.first_name, u.last_name, u.username, u.profile_photo_url,
             rt.depth + 1,
             rt.path || r.created_at
      FROM community_replies r
      JOIN users u ON r.user_id = u.id
      JOIN reply_tree rt ON r.parent_reply_id = rt.id
      WHERE r.is_deleted = false AND rt.depth < 5
    )
    SELECT * FROM reply_tree
    ORDER BY path`,
    [topicId]
  );

  res.json({
    success: true,
    data: {
      ...topic,
      replies: repliesResult.rows
    }
  });
});

// Reply to topic
const replyToTopic = asyncHandler(async (req, res) => {
  const { topicId } = req.params;
  const userId = req.user.id;
  const { content, parentReplyId } = req.body;

  // Verify topic exists and is not locked
  const topicCheck = await query(
    'SELECT id, is_locked, user_id, title FROM community_topics WHERE id = $1 AND is_deleted = false',
    [topicId]
  );

  if (topicCheck.rows.length === 0) {
    throw new AppError('Topic not found', 404);
  }

  if (topicCheck.rows[0].is_locked) {
    throw new AppError('Topic is locked', 403);
  }

  const topic = topicCheck.rows[0];

  // Verify parent reply if provided
  if (parentReplyId) {
    const parentCheck = await query(
      'SELECT id FROM community_replies WHERE id = $1 AND topic_id = $2 AND is_deleted = false',
      [parentReplyId, topicId]
    );

    if (parentCheck.rows.length === 0) {
      throw new AppError('Parent reply not found', 404);
    }
  }

  const replyResult = await query(
    `INSERT INTO community_replies 
     (topic_id, user_id, parent_reply_id, content)
     VALUES ($1, $2, $3, $4)
     RETURNING *`,
    [topicId, userId, parentReplyId, content]
  );

  // Notify topic owner if not self
  if (topic.user_id !== userId) {
    await sendRealtimeNotification(topic.user_id, {
      type: 'community_reply',
      title: 'New Reply',
      body: `${req.user.first_name} replied to your topic: "${topic.title}"`,
      data: { topic_id: topicId, reply_id: replyResult.rows[0].id }
    });
  }

  res.status(201).json({
    success: true,
    data: replyResult.rows[0]
  });
});

// Create community poll
const createPoll = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const neighborhoodId = req.user.neighborhood_id;
  const {
    title,
    description,
    pollType = 'single_choice',
    options,
    settings = {},
    endDate
  } = req.body;

  if (!options || options.length < 2) {
    throw new AppError('Poll must have at least 2 options', 400);
  }

  if (options.length > 10) {
    throw new AppError('Poll cannot have more than 10 options', 400);
  }

  // Generate IDs for options
  const pollOptions = options.map((opt, index) => ({
    id: `opt_${Date.now()}_${index}`,
    text: opt.text || opt,
    color: opt.color || null
  }));

  const pollResult = await query(
    `INSERT INTO community_polls 
     (neighborhood_id, created_by, title, description, poll_type, options, settings, end_date)
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
     RETURNING *`,
    [neighborhoodId, userId, title, description, pollType, JSON.stringify(pollOptions), 
     JSON.stringify(settings), endDate]
  );

  // Notify neighborhood
  const { sendNeighborhoodNotification } = require('../websocket/notifications');
  await sendNeighborhoodNotification(neighborhoodId, {
    type: 'new_poll',
    title: 'New Community Poll',
    body: title,
    data: { poll_id: pollResult.rows[0].id }
  });

  res.status(201).json({
    success: true,
    data: pollResult.rows[0]
  });
});

// Get active polls
const getPolls = asyncHandler(async (req, res) => {
  const neighborhoodId = req.user.neighborhood_id;
  const userId = req.user.id;
  const { includeExpired = false, limit = 20, offset = 0 } = req.query;

  let queryStr = `
    SELECT p.*, 
           u.first_name, u.last_name, u.username,
           EXISTS(SELECT 1 FROM community_poll_votes WHERE poll_id = p.id AND user_id = $1) as has_voted,
           (SELECT option_ids FROM community_poll_votes WHERE poll_id = p.id AND user_id = $1) as user_vote
    FROM community_polls p
    JOIN users u ON p.created_by = u.id
    WHERE p.neighborhood_id = $2`;

  const params = [userId, neighborhoodId];

  if (!includeExpired) {
    queryStr += ` AND p.is_active = true AND (p.end_date IS NULL OR p.end_date > NOW())`;
  }

  queryStr += ` ORDER BY p.created_at DESC LIMIT $3 OFFSET $4`;
  params.push(limit, offset);

  const pollsResult = await query(queryStr, params);

  // Get vote counts for each poll
  const pollsWithVotes = await Promise.all(
    pollsResult.rows.map(async (poll) => {
      const voteStats = await query(
        `SELECT 
          jsonb_array_elements_text(option_ids::jsonb) as option_id,
          COUNT(*) as vote_count
         FROM community_poll_votes
         WHERE poll_id = $1
         GROUP BY option_id`,
        [poll.id]
      );

      const voteCounts = {};
      voteStats.rows.forEach(stat => {
        voteCounts[stat.option_id] = parseInt(stat.vote_count);
      });

      return {
        ...poll,
        vote_counts: voteCounts,
        options: JSON.parse(poll.options),
        settings: JSON.parse(poll.settings)
      };
    })
  );

  res.json({
    success: true,
    data: pollsWithVotes,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Vote on poll
const voteOnPoll = asyncHandler(async (req, res) => {
  const { pollId } = req.params;
  const userId = req.user.id;
  const { optionIds, comment } = req.body;

  // Get poll details
  const pollResult = await query(
    'SELECT * FROM community_polls WHERE id = $1',
    [pollId]
  );

  if (pollResult.rows.length === 0) {
    throw new AppError('Poll not found', 404);
  }

  const poll = pollResult.rows[0];

  // Check if poll is active
  if (!poll.is_active || (poll.end_date && new Date(poll.end_date) < new Date())) {
    throw new AppError('Poll has ended', 400);
  }

  // Validate options
  const pollOptions = JSON.parse(poll.options);
  const validOptionIds = pollOptions.map(opt => opt.id);
  
  if (!Array.isArray(optionIds) || optionIds.length === 0) {
    throw new AppError('No options selected', 400);
  }

  // Validate selected options exist
  const invalidOptions = optionIds.filter(id => !validOptionIds.includes(id));
  if (invalidOptions.length > 0) {
    throw new AppError('Invalid option selected', 400);
  }

  // Check poll type constraints
  if (poll.poll_type === 'single_choice' && optionIds.length > 1) {
    throw new AppError('Only one option can be selected', 400);
  }

  // Insert or update vote
  const voteResult = await query(
    `INSERT INTO community_poll_votes (poll_id, user_id, option_ids, comment)
     VALUES ($1, $2, $3, $4)
     ON CONFLICT (poll_id, user_id)
     DO UPDATE SET option_ids = $3, comment = $4, created_at = NOW()
     RETURNING *`,
    [pollId, userId, optionIds, comment]
  );

  // Update total votes count
  await query(
    'UPDATE community_polls SET total_votes = (SELECT COUNT(*) FROM community_poll_votes WHERE poll_id = $1) WHERE id = $1',
    [pollId]
  );

  res.json({
    success: true,
    data: voteResult.rows[0]
  });
});

// Create announcement
const createAnnouncement = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const neighborhoodId = req.user.neighborhood_id;
  const {
    title,
    content,
    announcementType = 'general',
    priority = 'normal',
    targetAudience = 'all',
    isPinned = false,
    expiresAt,
    mediaUrls = []
  } = req.body;

  // Check if user has permission (admin/moderator)
  if (!req.user.is_admin && !req.user.is_moderator) {
    throw new AppError('Admin or moderator access required', 403);
  }

  const announcementResult = await query(
    `INSERT INTO community_announcements 
     (neighborhood_id, created_by, title, content, announcement_type, 
      priority, target_audience, is_pinned, expires_at, media_urls)
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
     RETURNING *`,
    [neighborhoodId, userId, title, content, announcementType,
     priority, targetAudience, isPinned, expiresAt, mediaUrls]
  );

  // Send notification based on priority
  if (priority === 'urgent' || priority === 'high') {
    const { sendNeighborhoodNotification } = require('../websocket/notifications');
    await sendNeighborhoodNotification(neighborhoodId, {
      type: 'community_announcement',
      title: `${priority === 'urgent' ? '🚨 URGENT: ' : ''}${title}`,
      body: content.substring(0, 100) + '...',
      data: { announcement_id: announcementResult.rows[0].id }
    });
  }

  res.status(201).json({
    success: true,
    data: announcementResult.rows[0]
  });
});

// Get community resources
const getResources = asyncHandler(async (req, res) => {
  const neighborhoodId = req.user.neighborhood_id;
  const { category, search, isVerified, limit = 50, offset = 0 } = req.query;

  let queryStr = `
    SELECT r.*, 
           u.first_name as creator_first_name, 
           u.last_name as creator_last_name
    FROM community_resources r
    JOIN users u ON r.created_by = u.id
    WHERE r.neighborhood_id = $1 AND r.is_active = true`;

  const params = [neighborhoodId];
  let paramCount = 1;

  if (category) {
    queryStr += ` AND r.category = $${++paramCount}`;
    params.push(category);
  }

  if (search) {
    queryStr += ` AND (r.name ILIKE $${++paramCount} OR r.description ILIKE $${paramCount} OR r.tags @> ARRAY[$${++paramCount}])`;
    params.push(`%${search}%`, search.toLowerCase());
    paramCount++;
  }

  if (isVerified !== undefined) {
    queryStr += ` AND r.is_verified = $${++paramCount}`;
    params.push(isVerified === 'true');
  }

  queryStr += ` ORDER BY r.is_verified DESC, r.name ASC LIMIT $${++paramCount} OFFSET $${++paramCount}`;
  params.push(limit, offset);

  const resourcesResult = await query(queryStr, params);

  // Get categories with counts
  const categoriesResult = await query(
    `SELECT category, COUNT(*) as count
     FROM community_resources
     WHERE neighborhood_id = $1 AND is_active = true
     GROUP BY category
     ORDER BY count DESC`,
    [neighborhoodId]
  );

  res.json({
    success: true,
    data: {
      resources: resourcesResult.rows.map(r => ({
        ...r,
        contact_info: JSON.parse(r.contact_info || '{}'),
        hours_of_operation: JSON.parse(r.hours_of_operation || '{}')
      })),
      categories: categoriesResult.rows
    },
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Create community initiative
const createInitiative = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const neighborhoodId = req.user.neighborhood_id;
  const {
    title,
    description,
    category,
    goals = [],
    startDate,
    endDate,
    locationName,
    location,
    volunteersNeeded = 0,
    budget,
    mediaUrls = [],
    contactInfo
  } = req.body;

  const initiativeResult = await withTransaction(async (client) => {
    // Create initiative
    const result = await client.query(
      `INSERT INTO community_initiatives 
       (neighborhood_id, created_by, title, description, category, goals,
        start_date, end_date, location_name, location, volunteers_needed,
        budget, media_urls, contact_info)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, 
               ${location ? 'ST_MakePoint($10, $11)::geography' : 'NULL'}, 
               $12, $13, $14, $15)
       RETURNING *`,
      [neighborhoodId, userId, title, description, category, goals,
       startDate, endDate, locationName, location?.longitude, location?.latitude,
       volunteersNeeded, budget, mediaUrls, contactInfo]
    );

    const initiative = result.rows[0];

    // Auto-join creator as volunteer
    await client.query(
      `INSERT INTO community_initiative_volunteers (initiative_id, user_id, role)
       VALUES ($1, $2, 'organizer')`,
      [initiative.id, userId]
    );

    return initiative;
  });

  // Create activity
  await query(
    `INSERT INTO user_activities 
     (user_id, activity_type, activity_data, neighborhood_id)
     VALUES ($1, 'community_initiative_created', $2, $3)`,
    [userId, { initiative_id: initiativeResult.id, title }, neighborhoodId]
  );

  res.status(201).json({
    success: true,
    data: initiativeResult
  });
});

// Get local services directory
const getServices = asyncHandler(async (req, res) => {
  const neighborhoodId = req.user.neighborhood_id;
  const {
    category,
    search,
    location,
    radius = 10,
    minRating,
    isVerified,
    sort = 'rating',
    limit = 20,
    offset = 0
  } = req.query;

  let queryStr = `
    SELECT s.*, 
           u.first_name as submitter_first_name,
           u.last_name as submitter_last_name`;

  if (location) {
    queryStr += `,
           ST_Distance(s.location::geography, ST_MakePoint($1, $2)::geography) / 1609.34 as distance_miles`;
  }

  queryStr += `
    FROM community_services s
    JOIN users u ON s.submitted_by = u.id
    WHERE s.neighborhood_id = $${location ? 3 : 1} AND s.is_active = true`;

  const params = [];
  let paramCount = 0;

  if (location) {
    params.push(location.longitude, location.latitude);
    paramCount = 2;
  }

  params.push(neighborhoodId);
  paramCount++;

  if (category) {
    queryStr += ` AND s.category = $${++paramCount}`;
    params.push(category);
  }

  if (search) {
    queryStr += ` AND (s.business_name ILIKE $${++paramCount} OR s.description ILIKE $${paramCount} OR s.specialties @> ARRAY[$${++paramCount}])`;
    params.push(`%${search}%`, search.toLowerCase());
    paramCount++;
  }

  if (location && radius) {
    queryStr += ` AND ST_DWithin(s.location::geography, ST_MakePoint($1, $2)::geography, $${++paramCount} * 1609.34)`;
    params.push(radius);
  }

  if (minRating) {
    queryStr += ` AND s.average_rating >= $${++paramCount}`;
    params.push(minRating);
  }

  if (isVerified !== undefined) {
    queryStr += ` AND s.is_verified = $${++paramCount}`;
    params.push(isVerified === 'true');
  }

  // Apply sorting
  switch (sort) {
    case 'rating':
      queryStr += ` ORDER BY s.average_rating DESC, s.total_ratings DESC`;
      break;
    case 'newest':
      queryStr += ` ORDER BY s.created_at DESC`;
      break;
    case 'nearest':
      queryStr += location ? ` ORDER BY distance_miles ASC` : ` ORDER BY s.created_at DESC`;
      break;
    case 'name':
      queryStr += ` ORDER BY s.business_name ASC`;
      break;
    default:
      queryStr += ` ORDER BY s.average_rating DESC`;
  }

  queryStr += ` LIMIT $${++paramCount} OFFSET $${++paramCount}`;
  params.push(limit, offset);

  const servicesResult = await query(queryStr, params);

  // Get categories
  const categoriesResult = await query(
    `SELECT category, COUNT(*) as count
     FROM community_services
     WHERE neighborhood_id = $1 AND is_active = true
     GROUP BY category
     ORDER BY count DESC`,
    [neighborhoodId]
  );

  res.json({
    success: true,
    data: {
      services: servicesResult.rows.map(s => ({
        ...s,
        contact_info: JSON.parse(s.contact_info || '{}'),
        distance: s.distance_miles ? parseFloat(s.distance_miles).toFixed(1) : null
      })),
      categories: categoriesResult.rows
    },
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

module.exports = {
  getForums,
  createTopic,
  getTopic,
  replyToTopic,
  createPoll,
  getPolls,
  voteOnPoll,
  createAnnouncement,
  getResources,
  createInitiative,
  getServices
};