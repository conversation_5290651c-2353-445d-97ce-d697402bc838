const { query, pool } = require('../config/database');
const { cache } = require('../config/redis');
const asyncHandler = require('../middleware/asyncHandler');
const AppError = require('../utils/AppError');
const logger = require('../config/logger');
const { sendNotification } = require('../services/pushNotifications');

// Get user's conversations
const getConversations = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { page = 1, limit = 20, unread_only } = req.query;
  const offset = (page - 1) * limit;

  let conversationQuery = `
    WITH user_conversations AS (
      SELECT 
        c.*,
        cp.joined_at,
        cp.last_read_at,
        cp.is_muted,
        cp.muted_until,
        CASE 
          WHEN c.conversation_type = 'direct' THEN (
            SELECT CONCAT(u.first_name, ' ', u.last_name)
            FROM conversation_participants cp2
            JOIN users u ON u.id = cp2.user_id
            WHERE cp2.conversation_id = c.id 
              AND cp2.user_id != $1
            LIMIT 1
          )
          ELSE c.name
        END as display_name,
        CASE 
          WHEN c.conversation_type = 'direct' THEN (
            SELECT u.profile_photo_url
            FROM conversation_participants cp2
            JOIN users u ON u.id = cp2.user_id
            WHERE cp2.conversation_id = c.id 
              AND cp2.user_id != $1
            LIMIT 1
          )
          ELSE c.avatar_url
        END as display_avatar,
        (
          SELECT COUNT(*)
          FROM direct_messages dm
          WHERE dm.conversation_id = c.id
            AND dm.created_at > COALESCE(cp.last_read_at, '1970-01-01'::timestamp)
            AND dm.sender_id != $1
            AND NOT dm.is_deleted
        ) as unread_count,
        (
          SELECT json_build_object(
            'content', dm.content,
            'sender_name', CONCAT(u.first_name, ' ', u.last_name),
            'created_at', dm.created_at,
            'message_type', dm.message_type
          )
          FROM direct_messages dm
          JOIN users u ON u.id = dm.sender_id
          WHERE dm.conversation_id = c.id
            AND NOT dm.is_deleted
          ORDER BY dm.created_at DESC
          LIMIT 1
        ) as last_message
      FROM conversations c
      JOIN conversation_participants cp ON cp.conversation_id = c.id
      WHERE cp.user_id = $1
        AND cp.left_at IS NULL
    )
    SELECT * FROM user_conversations
  `;

  const params = [userId];

  if (unread_only === 'true') {
    conversationQuery += ` WHERE unread_count > 0`;
  }

  conversationQuery += ` ORDER BY last_message_at DESC NULLS LAST LIMIT $2 OFFSET $3`;
  params.push(limit, offset);

  const result = await query(conversationQuery, params);

  // Get participant info for group conversations
  const conversationIds = result.rows.filter(c => c.conversation_type === 'group').map(c => c.id);
  
  if (conversationIds.length > 0) {
    const participantsResult = await query(
      `SELECT 
        cp.conversation_id,
        json_agg(json_build_object(
          'id', u.id,
          'name', CONCAT(u.first_name, ' ', u.last_name),
          'avatar', u.profile_photo_url
        )) as participants
       FROM conversation_participants cp
       JOIN users u ON u.id = cp.user_id
       WHERE cp.conversation_id = ANY($1)
         AND cp.left_at IS NULL
       GROUP BY cp.conversation_id`,
      [conversationIds]
    );

    const participantMap = {};
    participantsResult.rows.forEach(row => {
      participantMap[row.conversation_id] = row.participants;
    });

    result.rows.forEach(conversation => {
      if (conversation.conversation_type === 'group') {
        conversation.participants = participantMap[conversation.id] || [];
      }
    });
  }

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      hasMore: result.rows.length === parseInt(limit)
    }
  });
});

// Create a new conversation
const createConversation = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { participant_ids, message, group_name } = req.body;

  // Validate participants
  if (participant_ids.length === 0) {
    throw new AppError('At least one participant is required', 400);
  }

  // Check if participants are neighbors
  const neighborCheck = await query(
    `SELECT id FROM users 
     WHERE id = ANY($1) 
       AND neighborhood_id = $2
       AND id != $3`,
    [participant_ids, req.user.neighborhood_id, userId]
  );

  if (neighborCheck.rows.length !== participant_ids.length) {
    throw new AppError('Can only message neighbors', 400);
  }

  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    let conversationId;
    const conversationType = participant_ids.length === 1 ? 'direct' : 'group';

    if (conversationType === 'direct') {
      // Check if direct conversation already exists
      const existingConv = await client.query(
        `SELECT c.id 
         FROM conversations c
         JOIN conversation_participants cp1 ON cp1.conversation_id = c.id
         JOIN conversation_participants cp2 ON cp2.conversation_id = c.id
         WHERE c.conversation_type = 'direct'
           AND cp1.user_id = $1
           AND cp2.user_id = $2
           AND cp1.left_at IS NULL
           AND cp2.left_at IS NULL
         LIMIT 1`,
        [userId, participant_ids[0]]
      );

      if (existingConv.rows.length > 0) {
        conversationId = existingConv.rows[0].id;
      }
    }

    if (!conversationId) {
      // Create new conversation
      const convResult = await client.query(
        `INSERT INTO conversations (
          conversation_type, name, created_by
         ) VALUES ($1, $2, $3)
         RETURNING *`,
        [conversationType, group_name, userId]
      );
      conversationId = convResult.rows[0].id;

      // Add creator as participant
      await client.query(
        `INSERT INTO conversation_participants (
          conversation_id, user_id, role
         ) VALUES ($1, $2, $3)`,
        [conversationId, userId, conversationType === 'group' ? 'admin' : 'member']
      );

      // Add other participants
      for (const participantId of participant_ids) {
        await client.query(
          `INSERT INTO conversation_participants (
            conversation_id, user_id
           ) VALUES ($1, $2)`,
          [conversationId, participantId]
        );
      }
    }

    // Send initial message if provided
    let messageData = null;
    if (message) {
      const messageResult = await client.query(
        `INSERT INTO direct_messages (
          conversation_id, sender_id, content
         ) VALUES ($1, $2, $3)
         RETURNING *`,
        [conversationId, userId, message]
      );
      messageData = messageResult.rows[0];

      // Update conversation last_message_at
      await client.query(
        `UPDATE conversations 
         SET last_message_at = $1 
         WHERE id = $2`,
        [messageData.created_at, conversationId]
      );
    }

    await client.query('COMMIT');

    // Get full conversation data
    const fullConversation = await query(
      `SELECT 
        c.*,
        CASE 
          WHEN c.conversation_type = 'direct' THEN (
            SELECT CONCAT(u.first_name, ' ', u.last_name)
            FROM conversation_participants cp2
            JOIN users u ON u.id = cp2.user_id
            WHERE cp2.conversation_id = c.id 
              AND cp2.user_id != $1
            LIMIT 1
          )
          ELSE c.name
        END as display_name
       FROM conversations c
       WHERE c.id = $2`,
      [userId, conversationId]
    );

    // Send push notifications to participants
    const notificationPromises = participant_ids.map(participantId => 
      sendNotification(participantId, {
        title: conversationType === 'direct' ? `New message from ${req.user.first_name}` : `New group: ${group_name}`,
        body: message || 'You have been added to a conversation',
        data: { conversation_id: conversationId }
      })
    );

    await Promise.allSettled(notificationPromises);

    res.status(201).json({
      success: true,
      data: {
        conversation: fullConversation.rows[0],
        message: messageData
      }
    });
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
});

// Get conversation details
const getConversation = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { conversationId } = req.params;

  // Verify user is participant
  const participantCheck = await query(
    `SELECT role, is_muted, muted_until 
     FROM conversation_participants 
     WHERE conversation_id = $1 AND user_id = $2 AND left_at IS NULL`,
    [conversationId, userId]
  );

  if (participantCheck.rows.length === 0) {
    throw new AppError('Not a participant in this conversation', 403);
  }

  const result = await query(
    `SELECT 
      c.*,
      cp.role as user_role,
      cp.is_muted,
      cp.muted_until,
      json_agg(
        json_build_object(
          'id', u.id,
          'name', CONCAT(u.first_name, ' ', u.last_name),
          'avatar', u.profile_photo_url,
          'role', cp2.role,
          'joined_at', cp2.joined_at
        )
      ) FILTER (WHERE u.id IS NOT NULL) as participants
     FROM conversations c
     JOIN conversation_participants cp ON cp.conversation_id = c.id
     LEFT JOIN conversation_participants cp2 ON cp2.conversation_id = c.id AND cp2.left_at IS NULL
     LEFT JOIN users u ON u.id = cp2.user_id
     WHERE c.id = $1 AND cp.user_id = $2
     GROUP BY c.id, cp.role, cp.is_muted, cp.muted_until`,
    [conversationId, userId]
  );

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Update conversation settings
const updateConversation = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { conversationId } = req.params;
  const { name, description, avatar_url } = req.body;

  // Check if user is admin (for groups)
  const roleCheck = await query(
    `SELECT c.conversation_type, cp.role
     FROM conversations c
     JOIN conversation_participants cp ON cp.conversation_id = c.id
     WHERE c.id = $1 AND cp.user_id = $2 AND cp.left_at IS NULL`,
    [conversationId, userId]
  );

  if (roleCheck.rows.length === 0) {
    throw new AppError('Not a participant in this conversation', 403);
  }

  if (roleCheck.rows[0].conversation_type === 'group' && roleCheck.rows[0].role !== 'admin') {
    throw new AppError('Only admins can update group settings', 403);
  }

  const updateFields = [];
  const values = [];
  let paramCount = 1;

  if (name !== undefined) {
    updateFields.push(`name = $${paramCount++}`);
    values.push(name);
  }
  if (description !== undefined) {
    updateFields.push(`description = $${paramCount++}`);
    values.push(description);
  }
  if (avatar_url !== undefined) {
    updateFields.push(`avatar_url = $${paramCount++}`);
    values.push(avatar_url);
  }

  if (updateFields.length === 0) {
    throw new AppError('No fields to update', 400);
  }

  updateFields.push(`updated_at = NOW()`);
  values.push(conversationId);

  const result = await query(
    `UPDATE conversations 
     SET ${updateFields.join(', ')}
     WHERE id = $${paramCount}
     RETURNING *`,
    values
  );

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Archive conversation
const archiveConversation = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { conversationId } = req.params;

  // Note: This is a soft archive - we just mark the user as having left
  const result = await query(
    `UPDATE conversation_participants
     SET left_at = NOW()
     WHERE conversation_id = $1 AND user_id = $2 AND left_at IS NULL
     RETURNING *`,
    [conversationId, userId]
  );

  if (result.rows.length === 0) {
    throw new AppError('Not a participant in this conversation', 404);
  }

  res.json({
    success: true,
    message: 'Conversation archived'
  });
});

// Leave conversation
const leaveConversation = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { conversationId } = req.params;

  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Mark user as left
    const leaveResult = await client.query(
      `UPDATE conversation_participants
       SET left_at = NOW()
       WHERE conversation_id = $1 AND user_id = $2 AND left_at IS NULL
       RETURNING *`,
      [conversationId, userId]
    );

    if (leaveResult.rows.length === 0) {
      throw new AppError('Not a participant in this conversation', 404);
    }

    // Add system message
    await client.query(
      `INSERT INTO direct_messages (
        conversation_id, sender_id, message_type, content
       ) VALUES ($1, $2, 'system', $3)`,
      [conversationId, userId, `${req.user.first_name} ${req.user.last_name} left the conversation`]
    );

    // Check if user was the only admin (for groups)
    const adminCheck = await client.query(
      `SELECT COUNT(*) as admin_count
       FROM conversation_participants
       WHERE conversation_id = $1 
         AND role = 'admin' 
         AND left_at IS NULL`,
      [conversationId]
    );

    if (adminCheck.rows[0].admin_count === '0') {
      // Promote oldest member to admin
      await client.query(
        `UPDATE conversation_participants
         SET role = 'admin'
         WHERE conversation_id = $1
           AND left_at IS NULL
         ORDER BY joined_at
         LIMIT 1`,
        [conversationId]
      );
    }

    await client.query('COMMIT');

    res.json({
      success: true,
      message: 'Left conversation successfully'
    });
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
});

// Get messages in conversation
const getMessages = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { conversationId } = req.params;
  const { page = 1, limit = 50, before } = req.query;
  const offset = (page - 1) * limit;

  // Verify participant
  const participantCheck = await query(
    `SELECT last_read_at FROM conversation_participants 
     WHERE conversation_id = $1 AND user_id = $2 AND left_at IS NULL`,
    [conversationId, userId]
  );

  if (participantCheck.rows.length === 0) {
    throw new AppError('Not a participant in this conversation', 403);
  }

  let messageQuery = `
    SELECT 
      dm.*,
      CONCAT(u.first_name, ' ', u.last_name) as sender_name,
      u.profile_photo_url as sender_avatar,
      rm.content as reply_to_content,
      rm.sender_id as reply_to_sender_id,
      CONCAT(ru.first_name, ' ', ru.last_name) as reply_to_sender_name,
      ARRAY(
        SELECT json_build_object(
          'id', dmm.id,
          'url', dmm.media_url,
          'type', dmm.media_type,
          'thumbnail_url', dmm.thumbnail_url
        )
        FROM direct_message_media dmm
        WHERE dmm.message_id = dm.id
      ) as media
    FROM direct_messages dm
    JOIN users u ON u.id = dm.sender_id
    LEFT JOIN direct_messages rm ON rm.id = dm.reply_to_id
    LEFT JOIN users ru ON ru.id = rm.sender_id
    WHERE dm.conversation_id = $1
      AND NOT dm.is_deleted
  `;

  const params = [conversationId];

  if (before) {
    messageQuery += ` AND dm.created_at < $2`;
    params.push(before);
  }

  messageQuery += ` ORDER BY dm.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
  params.push(limit, offset);

  const result = await query(messageQuery, params);

  // Mark user as having read
  if (result.rows.length > 0) {
    const latestTimestamp = Math.max(...result.rows.map(m => new Date(m.created_at).getTime()));
    const currentRead = participantCheck.rows[0].last_read_at;
    
    if (!currentRead || new Date(latestTimestamp) > new Date(currentRead)) {
      await query(
        `UPDATE conversation_participants 
         SET last_read_at = $1
         WHERE conversation_id = $2 AND user_id = $3`,
        [new Date(latestTimestamp), conversationId, userId]
      );
    }
  }

  res.json({
    success: true,
    data: result.rows.reverse(), // Return in chronological order
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      hasMore: result.rows.length === parseInt(limit)
    }
  });
});

// Send a message
const sendMessage = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { conversationId } = req.params;
  const { content, message_type = 'text', media_urls, reply_to_id } = req.body;

  // Verify participant
  const participantCheck = await query(
    `SELECT * FROM conversation_participants 
     WHERE conversation_id = $1 AND user_id = $2 AND left_at IS NULL`,
    [conversationId, userId]
  );

  if (participantCheck.rows.length === 0) {
    throw new AppError('Not a participant in this conversation', 403);
  }

  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Insert message
    const messageResult = await client.query(
      `INSERT INTO direct_messages (
        conversation_id, sender_id, message_type, content, reply_to_id
       ) VALUES ($1, $2, $3, $4, $5)
       RETURNING *`,
      [conversationId, userId, message_type, content, reply_to_id]
    );

    const message = messageResult.rows[0];

    // Add media if provided
    if (media_urls && media_urls.length > 0) {
      for (const url of media_urls) {
        await client.query(
          `INSERT INTO direct_message_media (
            message_id, media_url, media_type
           ) VALUES ($1, $2, $3)`,
          [message.id, url, message_type === 'image' ? 'image' : 'file']
        );
      }
    }

    // Update conversation last_message_at
    await client.query(
      `UPDATE conversations 
       SET last_message_at = $1 
       WHERE id = $2`,
      [message.created_at, conversationId]
    );

    // Update sender's last_read_at
    await client.query(
      `UPDATE conversation_participants 
       SET last_read_at = $1
       WHERE conversation_id = $2 AND user_id = $3`,
      [message.created_at, conversationId, userId]
    );

    await client.query('COMMIT');

    // Get full message data
    const fullMessage = await query(
      `SELECT 
        dm.*,
        CONCAT(u.first_name, ' ', u.last_name) as sender_name,
        u.profile_photo_url as sender_avatar,
        ARRAY(
          SELECT json_build_object(
            'id', dmm.id,
            'url', dmm.media_url,
            'type', dmm.media_type
          )
          FROM direct_message_media dmm
          WHERE dmm.message_id = dm.id
        ) as media
       FROM direct_messages dm
       JOIN users u ON u.id = dm.sender_id
       WHERE dm.id = $1`,
      [message.id]
    );

    // Send push notifications
    const participants = await query(
      `SELECT u.id, u.fcm_token, cp.is_muted, cp.muted_until
       FROM conversation_participants cp
       JOIN users u ON u.id = cp.user_id
       WHERE cp.conversation_id = $1 
         AND cp.user_id != $2 
         AND cp.left_at IS NULL
         AND u.fcm_token IS NOT NULL`,
      [conversationId, userId]
    );

    const notificationPromises = participants.rows
      .filter(p => !p.is_muted || (p.muted_until && new Date(p.muted_until) < new Date()))
      .map(participant => 
        sendNotification(participant.id, {
          title: `Message from ${req.user.first_name}`,
          body: content.substring(0, 100),
          data: { 
            conversation_id: conversationId,
            message_id: message.id
          }
        })
      );

    await Promise.allSettled(notificationPromises);

    // Emit to WebSocket if available
    if (req.app.locals.io) {
      req.app.locals.io.to(`conversation:${conversationId}`).emit('new_message', fullMessage.rows[0]);
    }

    res.status(201).json({
      success: true,
      data: fullMessage.rows[0]
    });
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
});

// Edit a message
const editMessage = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { messageId } = req.params;
  const { content } = req.body;

  const result = await query(
    `UPDATE direct_messages
     SET content = $1, is_edited = true, edited_at = NOW()
     WHERE id = $2 AND sender_id = $3 AND NOT is_deleted
     RETURNING *`,
    [content, messageId, userId]
  );

  if (result.rows.length === 0) {
    throw new AppError('Message not found or cannot be edited', 404);
  }

  // Emit to WebSocket
  if (req.app.locals.io) {
    req.app.locals.io.to(`conversation:${result.rows[0].conversation_id}`).emit('message_edited', {
      message_id: messageId,
      content,
      edited_at: result.rows[0].edited_at
    });
  }

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Delete a message
const deleteMessage = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { messageId } = req.params;

  const result = await query(
    `UPDATE direct_messages
     SET is_deleted = true, deleted_at = NOW(), content = '[Message deleted]'
     WHERE id = $1 AND sender_id = $2 AND NOT is_deleted
     RETURNING conversation_id`,
    [messageId, userId]
  );

  if (result.rows.length === 0) {
    throw new AppError('Message not found or already deleted', 404);
  }

  // Emit to WebSocket
  if (req.app.locals.io) {
    req.app.locals.io.to(`conversation:${result.rows[0].conversation_id}`).emit('message_deleted', {
      message_id: messageId
    });
  }

  res.json({
    success: true,
    message: 'Message deleted'
  });
});

// Mark messages as read
const markAsRead = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { conversationId } = req.params;

  await query(
    `UPDATE conversation_participants
     SET last_read_at = NOW()
     WHERE conversation_id = $1 AND user_id = $2`,
    [conversationId, userId]
  );

  // Clear notification count
  await query(
    `UPDATE notifications 
     SET is_read = true
     WHERE user_id = $1 
       AND data->>'conversation_id' = $2
       AND is_read = false`,
    [userId, conversationId]
  );

  res.json({
    success: true,
    message: 'Messages marked as read'
  });
});

// Send typing indicator
const sendTypingIndicator = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { conversationId } = req.params;
  const { is_typing } = req.body;

  // Verify participant
  const participantCheck = await query(
    `SELECT 1 FROM conversation_participants 
     WHERE conversation_id = $1 AND user_id = $2 AND left_at IS NULL`,
    [conversationId, userId]
  );

  if (participantCheck.rows.length === 0) {
    throw new AppError('Not a participant in this conversation', 403);
  }

  // Emit to WebSocket
  if (req.app.locals.io) {
    req.app.locals.io.to(`conversation:${conversationId}`).emit('typing_indicator', {
      user_id: userId,
      user_name: `${req.user.first_name} ${req.user.last_name}`,
      is_typing,
      conversation_id: conversationId
    });
  }

  res.json({
    success: true,
    message: 'Typing indicator sent'
  });
});

// Add participants to group
const addParticipants = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { conversationId } = req.params;
  const { user_ids } = req.body;

  // Check if user is admin
  const adminCheck = await query(
    `SELECT c.conversation_type, cp.role
     FROM conversations c
     JOIN conversation_participants cp ON cp.conversation_id = c.id
     WHERE c.id = $1 AND cp.user_id = $2 AND cp.left_at IS NULL`,
    [conversationId, userId]
  );

  if (adminCheck.rows.length === 0 || adminCheck.rows[0].role !== 'admin') {
    throw new AppError('Only admins can add participants', 403);
  }

  if (adminCheck.rows[0].conversation_type !== 'group') {
    throw new AppError('Cannot add participants to direct conversations', 400);
  }

  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    const addedUsers = [];
    for (const newUserId of user_ids) {
      // Check if already participant
      const existing = await client.query(
        `SELECT 1 FROM conversation_participants 
         WHERE conversation_id = $1 AND user_id = $2`,
        [conversationId, newUserId]
      );

      if (existing.rows.length === 0) {
        await client.query(
          `INSERT INTO conversation_participants (conversation_id, user_id)
           VALUES ($1, $2)`,
          [conversationId, newUserId]
        );

        const userInfo = await client.query(
          `SELECT first_name, last_name FROM users WHERE id = $1`,
          [newUserId]
        );
        addedUsers.push(userInfo.rows[0]);
      }
    }

    // Add system message
    if (addedUsers.length > 0) {
      const names = addedUsers.map(u => `${u.first_name} ${u.last_name}`).join(', ');
      await client.query(
        `INSERT INTO direct_messages (
          conversation_id, sender_id, message_type, content
         ) VALUES ($1, $2, 'system', $3)`,
        [conversationId, userId, `${req.user.first_name} added ${names} to the conversation`]
      );
    }

    await client.query('COMMIT');

    res.json({
      success: true,
      message: `Added ${addedUsers.length} participants`
    });
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
});

// Remove participant from group
const removeParticipant = asyncHandler(async (req, res) => {
  const adminId = req.user.id;
  const { conversationId, userId } = req.params;

  // Check if admin
  const adminCheck = await query(
    `SELECT c.conversation_type, cp.role
     FROM conversations c
     JOIN conversation_participants cp ON cp.conversation_id = c.id
     WHERE c.id = $1 AND cp.user_id = $2 AND cp.left_at IS NULL`,
    [conversationId, adminId]
  );

  if (adminCheck.rows.length === 0 || adminCheck.rows[0].role !== 'admin') {
    throw new AppError('Only admins can remove participants', 403);
  }

  if (adminCheck.rows[0].conversation_type !== 'group') {
    throw new AppError('Cannot remove participants from direct conversations', 400);
  }

  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Remove participant
    const removeResult = await client.query(
      `UPDATE conversation_participants
       SET left_at = NOW()
       WHERE conversation_id = $1 AND user_id = $2 AND left_at IS NULL
       RETURNING (SELECT CONCAT(first_name, ' ', last_name) FROM users WHERE id = $2)`,
      [conversationId, userId]
    );

    if (removeResult.rows.length === 0) {
      throw new AppError('User is not a participant', 404);
    }

    // Add system message
    await client.query(
      `INSERT INTO direct_messages (
        conversation_id, sender_id, message_type, content
       ) VALUES ($1, $2, 'system', $3)`,
      [conversationId, adminId, `${req.user.first_name} removed ${removeResult.rows[0].concat} from the conversation`]
    );

    await client.query('COMMIT');

    res.json({
      success: true,
      message: 'Participant removed'
    });
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
});

// Mute/unmute conversation
const muteConversation = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { conversationId } = req.params;
  const { is_muted, muted_until } = req.body;

  const result = await query(
    `UPDATE conversation_participants
     SET is_muted = $1, muted_until = $2
     WHERE conversation_id = $3 AND user_id = $4 AND left_at IS NULL
     RETURNING *`,
    [is_muted, muted_until, conversationId, userId]
  );

  if (result.rows.length === 0) {
    throw new AppError('Not a participant in this conversation', 404);
  }

  res.json({
    success: true,
    message: is_muted ? 'Conversation muted' : 'Conversation unmuted',
    data: {
      is_muted: result.rows[0].is_muted,
      muted_until: result.rows[0].muted_until
    }
  });
});

// Search messages
const searchMessages = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { q, conversation_id } = req.query;

  if (!q || q.trim().length < 2) {
    throw new AppError('Search query must be at least 2 characters', 400);
  }

  let searchQuery = `
    SELECT 
      dm.*,
      c.name as conversation_name,
      c.conversation_type,
      CONCAT(u.first_name, ' ', u.last_name) as sender_name,
      u.profile_photo_url as sender_avatar,
      ts_rank(to_tsvector('english', dm.content), plainto_tsquery('english', $2)) as rank
    FROM direct_messages dm
    JOIN conversations c ON c.id = dm.conversation_id
    JOIN conversation_participants cp ON cp.conversation_id = c.id
    JOIN users u ON u.id = dm.sender_id
    WHERE cp.user_id = $1
      AND cp.left_at IS NULL
      AND NOT dm.is_deleted
      AND dm.content ILIKE '%' || $2 || '%'
  `;

  const params = [userId, q];

  if (conversation_id) {
    searchQuery += ` AND dm.conversation_id = $3`;
    params.push(conversation_id);
  }

  searchQuery += ` ORDER BY rank DESC, dm.created_at DESC LIMIT 50`;

  const result = await query(searchQuery, params);

  res.json({
    success: true,
    data: result.rows,
    query: q
  });
});

module.exports = {
  getConversations,
  createConversation,
  getConversation,
  updateConversation,
  archiveConversation,
  leaveConversation,
  getMessages,
  sendMessage,
  editMessage,
  deleteMessage,
  markAsRead,
  sendTypingIndicator,
  addParticipants,
  removeParticipant,
  muteConversation,
  searchMessages
};