const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { query, withTransaction } = require('../config/database');
const { sessions, cache } = require('../config/redis');
const { AppError, asyncHandler } = require('../middleware/errorHandler');
const logger = require('../config/logger');

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign(
    { id: userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRE || '24h' }
  );
};

// Generate refresh token
const generateRefreshToken = (userId) => {
  return jwt.sign(
    { id: userId, type: 'refresh' },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRE || '7d' }
  );
};

// Register new user
const register = asyncHandler(async (req, res) => {
  const { email, password, first_name, last_name, bio } = req.body;

  // Check if email already exists
  const existingUser = await query(
    'SELECT id FROM users WHERE email = $1',
    [email]
  );

  if (existingUser.rows.length > 0) {
    throw new AppError('Email already registered', 409, 'EMAIL_EXISTS');
  }

  // Hash password
  const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS) || 10);
  const passwordHash = await bcrypt.hash(password, salt);

  // Create user
  const result = await query(
    `INSERT INTO users (email, password_hash, first_name, last_name, bio)
     VALUES ($1, $2, $3, $4, $5)
     RETURNING id, email, first_name, last_name, bio, is_verified, created_at`,
    [email, passwordHash, first_name, last_name, bio]
  );

  const user = result.rows[0];

  // Generate tokens
  const token = generateToken(user.id);
  const refreshToken = generateRefreshToken(user.id);

  // Store session in Redis
  await sessions.set(user.id, {
    token,
    refreshToken,
    createdAt: Date.now()
  });

  // TODO: Send verification email

  res.status(201).json({
    success: true,
    data: {
      token,
      refresh_token: refreshToken,
      expires_in: 86400, // 24 hours
      user: {
        ...user,
        trust_score: 0,
        is_emergency_responder: false
      }
    }
  });
});

// Login user
const login = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // Get user by email
  const result = await query(
    `SELECT id, email, password_hash, first_name, last_name, profile_photo_url,
            bio, is_verified, is_emergency_responder, trust_score, status, created_at
     FROM users
     WHERE email = $1 AND status = 'active'`,
    [email]
  );

  if (result.rows.length === 0) {
    throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS');
  }

  const user = result.rows[0];

  // Verify password
  const isPasswordValid = await bcrypt.compare(password, user.password_hash);
  if (!isPasswordValid) {
    throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS');
  }

  // Generate tokens
  const token = generateToken(user.id);
  const refreshToken = generateRefreshToken(user.id);

  // Store session
  await sessions.set(user.id, {
    token,
    refreshToken,
    createdAt: Date.now()
  });

  // Log login history
  await query(
    `INSERT INTO login_history (user_id, ip_address, user_agent)
     VALUES ($1, $2, $3)`,
    [user.id, req.ip, req.get('user-agent')]
  );

  // Remove password from response
  delete user.password_hash;

  res.json({
    success: true,
    data: {
      token,
      refresh_token: refreshToken,
      expires_in: 86400,
      user
    }
  });
});

// Logout
const logout = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // Remove session from Redis
  await sessions.del(userId);

  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Logout from all devices
const logoutAll = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // Remove all sessions
  await sessions.delAll(userId);

  res.json({
    success: true,
    message: 'Logged out from all devices'
  });
});

// Refresh token
const refreshToken = asyncHandler(async (req, res) => {
  const { refresh_token } = req.body;

  try {
    // Verify refresh token
    const decoded = jwt.verify(refresh_token, process.env.JWT_REFRESH_SECRET);
    
    if (decoded.type !== 'refresh') {
      throw new AppError('Invalid token type', 401, 'INVALID_TOKEN');
    }

    // Check if session exists
    const session = await sessions.get(decoded.id);
    if (!session || session.refreshToken !== refresh_token) {
      throw new AppError('Invalid refresh token', 401, 'INVALID_TOKEN');
    }

    // Generate new tokens
    const newToken = generateToken(decoded.id);
    const newRefreshToken = generateRefreshToken(decoded.id);

    // Update session
    await sessions.set(decoded.id, {
      token: newToken,
      refreshToken: newRefreshToken,
      createdAt: Date.now()
    });

    // Get user data
    const result = await query(
      `SELECT id, email, first_name, last_name, profile_photo_url,
              bio, is_verified, is_emergency_responder, trust_score, created_at
       FROM users
       WHERE id = $1 AND status = 'active'`,
      [decoded.id]
    );

    res.json({
      success: true,
      data: {
        token: newToken,
        refresh_token: newRefreshToken,
        expires_in: 86400,
        user: result.rows[0]
      }
    });
  } catch (error) {
    throw new AppError('Invalid refresh token', 401, 'INVALID_TOKEN');
  }
});

// Verify email
const verifyEmail = asyncHandler(async (req, res) => {
  const { email, code } = req.body;

  // TODO: Implement actual verification logic
  // For now, just mark user as verified
  const result = await query(
    `UPDATE users 
     SET is_verified = true, email_verified_at = NOW()
     WHERE email = $1
     RETURNING id`,
    [email]
  );

  if (result.rows.length === 0) {
    throw new AppError('User not found', 404);
  }

  res.json({
    success: true,
    message: 'Email verified successfully'
  });
});

// Resend verification
const resendVerification = asyncHandler(async (req, res) => {
  const { email } = req.body;

  // TODO: Implement email sending
  
  res.json({
    success: true,
    message: 'Verification code sent to email'
  });
});

// Forgot password
const forgotPassword = asyncHandler(async (req, res) => {
  const { email } = req.body;

  // Check if user exists
  const result = await query(
    'SELECT id, first_name FROM users WHERE email = $1',
    [email]
  );

  if (result.rows.length === 0) {
    // Don't reveal if email exists
    res.json({
      success: true,
      message: 'If the email exists, a reset code has been sent'
    });
    return;
  }

  // TODO: Generate and send reset code

  res.json({
    success: true,
    message: 'Reset code sent to email'
  });
});

// Reset password
const resetPassword = asyncHandler(async (req, res) => {
  const { email, code, new_password } = req.body;

  // TODO: Verify reset code

  // Hash new password
  const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS) || 10);
  const passwordHash = await bcrypt.hash(new_password, salt);

  // Update password
  const result = await query(
    `UPDATE users 
     SET password_hash = $2, updated_at = NOW()
     WHERE email = $1
     RETURNING id`,
    [email, passwordHash]
  );

  if (result.rows.length === 0) {
    throw new AppError('Invalid reset code', 400);
  }

  // Clear all sessions
  await sessions.delAll(result.rows[0].id);

  res.json({
    success: true,
    message: 'Password reset successfully'
  });
});

// Change password
const changePassword = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { current_password, new_password } = req.body;

  // Get current password hash
  const result = await query(
    'SELECT password_hash FROM users WHERE id = $1',
    [userId]
  );

  // Verify current password
  const isValid = await bcrypt.compare(current_password, result.rows[0].password_hash);
  if (!isValid) {
    throw new AppError('Current password is incorrect', 401);
  }

  // Hash new password
  const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS) || 10);
  const passwordHash = await bcrypt.hash(new_password, salt);

  // Update password
  await query(
    'UPDATE users SET password_hash = $2, updated_at = NOW() WHERE id = $1',
    [userId, passwordHash]
  );

  res.json({
    success: true,
    message: 'Password changed successfully'
  });
});

// Verify address
const verifyAddress = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { address, latitude, longitude } = req.body;

  // Find or create neighborhood
  let neighborhoodResult = await query(
    `SELECT id, name FROM neighborhoods
     WHERE ST_DWithin(
       center_location::geography,
       ST_MakePoint($1, $2)::geography,
       radius_miles * 1609.34
     )
     LIMIT 1`,
    [longitude, latitude]
  );

  let neighborhoodId;
  let neighborhoodName;

  if (neighborhoodResult.rows.length === 0) {
    // Create new neighborhood
    const insertResult = await query(
      `INSERT INTO neighborhoods (name, center_location, city, state, country, postal_code)
       VALUES ($1, ST_MakePoint($2, $3)::geography, $4, $5, $6, $7)
       RETURNING id, name`,
      [
        `${address.city} - ${address.postal_code}`,
        longitude,
        latitude,
        address.city,
        address.state,
        address.country || 'US',
        address.postal_code
      ]
    );
    neighborhoodId = insertResult.rows[0].id;
    neighborhoodName = insertResult.rows[0].name;
  } else {
    neighborhoodId = neighborhoodResult.rows[0].id;
    neighborhoodName = neighborhoodResult.rows[0].name;
  }

  // Update user
  await query(
    `UPDATE users 
     SET neighborhood_id = $2, 
         address = $3,
         location = ST_MakePoint($4, $5)::geography,
         address_verified_at = NOW(),
         updated_at = NOW()
     WHERE id = $1`,
    [userId, neighborhoodId, address, longitude, latitude]
  );

  res.json({
    success: true,
    data: {
      neighborhood_id: neighborhoodId,
      neighborhood_name: neighborhoodName
    }
  });
});

// Check email availability
const checkEmail = asyncHandler(async (req, res) => {
  const { email } = req.query;

  const result = await query(
    'SELECT 1 FROM users WHERE email = $1',
    [email]
  );

  res.json({
    success: true,
    data: {
      available: result.rows.length === 0
    }
  });
});

// Check password strength
const checkPasswordStrength = asyncHandler(async (req, res) => {
  const { password } = req.body;

  let score = 0;
  let strength = 'weak';
  const suggestions = [];

  // Length check
  if (password.length >= 8) score += 20;
  if (password.length >= 12) score += 10;
  else suggestions.push('Use at least 12 characters');

  // Character variety
  if (/[a-z]/.test(password)) score += 15;
  else suggestions.push('Add lowercase letters');
  
  if (/[A-Z]/.test(password)) score += 15;
  else suggestions.push('Add uppercase letters');
  
  if (/[0-9]/.test(password)) score += 15;
  else suggestions.push('Add numbers');
  
  if (/[^a-zA-Z0-9]/.test(password)) score += 25;
  else suggestions.push('Add special characters');

  // Determine strength
  if (score >= 80) strength = 'strong';
  else if (score >= 60) strength = 'good';
  else if (score >= 40) strength = 'fair';

  res.json({
    success: true,
    data: {
      strength,
      score,
      suggestions
    }
  });
});

// Get login history
const getLoginHistory = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { limit = 20, offset = 0 } = req.query;

  const result = await query(
    `SELECT id, ip_address, user_agent, device_type, location, created_at
     FROM login_history
     WHERE user_id = $1
     ORDER BY created_at DESC
     LIMIT $2 OFFSET $3`,
    [userId, limit, offset]
  );

  res.json({
    success: true,
    data: result.rows
  });
});

// Enable biometric
const enableBiometric = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { device_id, biometric_key } = req.body;

  // Store biometric auth data
  await query(
    `INSERT INTO biometric_auth (user_id, device_id, biometric_key, is_active)
     VALUES ($1, $2, $3, true)
     ON CONFLICT (user_id, device_id) 
     DO UPDATE SET biometric_key = $3, is_active = true, updated_at = NOW()`,
    [userId, device_id, biometric_key]
  );

  res.json({
    success: true,
    message: 'Biometric authentication enabled'
  });
});

// Biometric login
const biometricLogin = asyncHandler(async (req, res) => {
  const { device_id, biometric_signature } = req.body;

  // TODO: Implement actual biometric verification
  // For testing, just return a valid user
  const result = await query(
    `SELECT u.* FROM users u
     JOIN biometric_auth ba ON u.id = ba.user_id
     WHERE ba.device_id = $1 AND ba.is_active = true
     LIMIT 1`,
    [device_id]
  );

  if (result.rows.length === 0) {
    throw new AppError('Biometric authentication failed', 401);
  }

  const user = result.rows[0];

  // Generate tokens
  const token = generateToken(user.id);
  const refreshToken = generateRefreshToken(user.id);

  // Store session
  await sessions.set(user.id, {
    token,
    refreshToken,
    createdAt: Date.now()
  });

  delete user.password_hash;

  res.json({
    success: true,
    data: {
      token,
      refresh_token: refreshToken,
      expires_in: 86400,
      user
    }
  });
});

module.exports = {
  register,
  login,
  logout,
  logoutAll,
  refreshToken,
  verifyEmail,
  resendVerification,
  forgotPassword,
  resetPassword,
  changePassword,
  verifyAddress,
  checkEmail,
  checkPasswordStrength,
  getLoginHistory,
  enableBiometric,
  biometricLogin
};