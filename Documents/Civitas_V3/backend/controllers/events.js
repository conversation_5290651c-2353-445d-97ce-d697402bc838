const { query, withTransaction } = require('../config/database');
const { cache } = require('../config/redis');
const { AppError, asyncHandler } = require('../middleware/errorHandler');
const logger = require('../config/logger');
const { sendRealtimeNotification } = require('../websocket/notifications');
const { calculateDistance } = require('../services/locationService');

// Create a new event
const createEvent = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const {
    title,
    description,
    event_type,
    start_date,
    end_date,
    location,
    address,
    max_attendees,
    is_public,
    is_recurring,
    recurrence_pattern,
    allow_guests,
    require_rsvp,
    tags,
    cover_photo_url
  } = req.body;

  // Validate dates
  if (new Date(start_date) < new Date()) {
    throw new AppError('Event start date must be in the future', 400);
  }

  if (end_date && new Date(end_date) < new Date(start_date)) {
    throw new AppError('End date must be after start date', 400);
  }

  const eventData = await withTransaction(async (client) => {
    // Create event
    const eventResult = await client.query(
      `INSERT INTO events 
       (organizer_id, neighborhood_id, title, description, event_type,
        start_date, end_date, location, address, max_attendees,
        is_public, is_recurring, recurrence_pattern, allow_guests,
        require_rsvp, tags, cover_photo_url, status)
       VALUES ($1, $2, $3, $4, $5, $6, $7, 
               ST_MakePoint($8, $9)::geography, $10, $11, $12, $13, $14,
               $15, $16, $17, $18, 'active')
       RETURNING *`,
      [userId, req.user.neighborhood_id, title, description, event_type,
       start_date, end_date, location?.longitude, location?.latitude,
       address, max_attendees, is_public || false, is_recurring || false,
       recurrence_pattern, allow_guests || true, require_rsvp || false,
       tags || [], cover_photo_url]
    );

    const event = eventResult.rows[0];

    // Add organizer as attendee
    await client.query(
      `INSERT INTO event_attendees (event_id, user_id, status, is_organizer)
       VALUES ($1, $2, 'attending', true)`,
      [event.id, userId]
    );

    // Create activity feed entry
    await client.query(
      `INSERT INTO user_activities 
       (user_id, activity_type, activity_data, neighborhood_id)
       VALUES ($1, 'event_created', $2, $3)`,
      [userId, { event_id: event.id, event_title: title }, req.user.neighborhood_id]
    );

    return event;
  });

  // Cache event for quick access
  await cache.setex(`event:${eventData.id}`, 3600, JSON.stringify(eventData));

  // Notify neighborhood if public event
  if (is_public) {
    setImmediate(async () => {
      const { sendNeighborhoodNotification } = require('../websocket/notifications');
      await sendNeighborhoodNotification(req.user.neighborhood_id, {
        type: 'new_event',
        title: 'New Community Event',
        body: `${req.user.first_name} created: ${title}`,
        data: { event_id: eventData.id }
      });
    });
  }

  res.status(201).json({
    success: true,
    data: eventData
  });
});

// Get all events
const getEvents = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { 
    filter = 'upcoming',
    event_type,
    date_from,
    date_to,
    search,
    limit = 20,
    offset = 0
  } = req.query;

  let queryStr = `
    SELECT e.*, 
           u.first_name as organizer_first_name,
           u.last_name as organizer_last_name,
           u.profile_photo_url as organizer_photo,
           COUNT(DISTINCT ea.id) FILTER (WHERE ea.status = 'attending') as attendee_count,
           EXISTS(
             SELECT 1 FROM event_attendees 
             WHERE event_id = e.id AND user_id = $1 AND status = 'attending'
           ) as is_attending,
           EXISTS(
             SELECT 1 FROM event_attendees 
             WHERE event_id = e.id AND user_id = $1 AND status = 'interested'
           ) as is_interested
    FROM events e
    JOIN users u ON e.organizer_id = u.id
    LEFT JOIN event_attendees ea ON e.id = ea.event_id
    WHERE e.neighborhood_id = $2
      AND e.status = 'active'`;

  const params = [userId, req.user.neighborhood_id];
  let paramCount = 2;

  // Apply filters
  if (filter === 'upcoming') {
    queryStr += ` AND e.start_date > NOW()`;
  } else if (filter === 'past') {
    queryStr += ` AND e.end_date < NOW()`;
  } else if (filter === 'attending') {
    queryStr += ` AND EXISTS(
      SELECT 1 FROM event_attendees 
      WHERE event_id = e.id AND user_id = $1 AND status = 'attending'
    )`;
  } else if (filter === 'organizing') {
    queryStr += ` AND e.organizer_id = $1`;
  }

  if (event_type) {
    queryStr += ` AND e.event_type = $${++paramCount}`;
    params.push(event_type);
  }

  if (date_from) {
    queryStr += ` AND e.start_date >= $${++paramCount}`;
    params.push(date_from);
  }

  if (date_to) {
    queryStr += ` AND e.start_date <= $${++paramCount}`;
    params.push(date_to);
  }

  if (search) {
    queryStr += ` AND (e.title ILIKE $${++paramCount} OR e.description ILIKE $${paramCount})`;
    params.push(`%${search}%`);
  }

  queryStr += `
    GROUP BY e.id, u.first_name, u.last_name, u.profile_photo_url
    ORDER BY e.start_date ASC
    LIMIT $${++paramCount} OFFSET $${++paramCount}`;
  
  params.push(limit, offset);

  const result = await query(queryStr, params);

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: result.rows.length
    }
  });
});

// Get single event
const getEvent = asyncHandler(async (req, res) => {
  const { eventId } = req.params;
  const userId = req.user.id;

  // Try cache first
  const cached = await cache.get(`event:${eventId}`);
  if (cached) {
    res.json({
      success: true,
      data: JSON.parse(cached)
    });
    return;
  }

  const eventResult = await query(
    `SELECT e.*, 
            u.first_name as organizer_first_name,
            u.last_name as organizer_last_name,
            u.profile_photo_url as organizer_photo,
            COUNT(DISTINCT ea.id) FILTER (WHERE ea.status = 'attending') as attendee_count,
            COUNT(DISTINCT ea.id) FILTER (WHERE ea.status = 'interested') as interested_count,
            EXISTS(
              SELECT 1 FROM event_attendees 
              WHERE event_id = e.id AND user_id = $2 AND status = 'attending'
            ) as is_attending,
            EXISTS(
              SELECT 1 FROM event_attendees 
              WHERE event_id = e.id AND user_id = $2 AND status = 'interested'
            ) as is_interested,
            ST_X(e.location::geometry) as longitude,
            ST_Y(e.location::geometry) as latitude
     FROM events e
     JOIN users u ON e.organizer_id = u.id
     LEFT JOIN event_attendees ea ON e.id = ea.event_id
     WHERE e.id = $1
     GROUP BY e.id, u.first_name, u.last_name, u.profile_photo_url`,
    [eventId, userId]
  );

  if (eventResult.rows.length === 0) {
    throw new AppError('Event not found', 404);
  }

  const event = eventResult.rows[0];

  // Check if user can view private event
  if (!event.is_public && event.organizer_id !== userId) {
    const inviteCheck = await query(
      'SELECT 1 FROM event_attendees WHERE event_id = $1 AND user_id = $2',
      [eventId, userId]
    );
    
    if (inviteCheck.rows.length === 0) {
      throw new AppError('This is a private event', 403);
    }
  }

  // Cache for next time
  await cache.setex(`event:${eventId}`, 3600, JSON.stringify(event));

  res.json({
    success: true,
    data: event
  });
});

// Update event
const updateEvent = asyncHandler(async (req, res) => {
  const { eventId } = req.params;
  const userId = req.user.id;
  const updates = req.body;

  // Check ownership
  const ownerCheck = await query(
    'SELECT organizer_id FROM events WHERE id = $1',
    [eventId]
  );

  if (ownerCheck.rows.length === 0) {
    throw new AppError('Event not found', 404);
  }

  if (ownerCheck.rows[0].organizer_id !== userId) {
    throw new AppError('Only the organizer can update this event', 403);
  }

  // Build update query
  const allowedUpdates = [
    'title', 'description', 'event_type', 'start_date', 'end_date',
    'address', 'max_attendees', 'is_public', 'allow_guests',
    'require_rsvp', 'tags', 'cover_photo_url'
  ];

  const updateFields = [];
  const values = [];
  let paramCount = 1;

  for (const field of allowedUpdates) {
    if (updates[field] !== undefined) {
      updateFields.push(`${field} = $${paramCount++}`);
      values.push(updates[field]);
    }
  }

  if (updates.location) {
    updateFields.push(`location = ST_MakePoint($${paramCount++}, $${paramCount++})::geography`);
    values.push(updates.location.longitude, updates.location.latitude);
  }

  if (updateFields.length === 0) {
    throw new AppError('No valid updates provided', 400);
  }

  values.push(eventId);

  const result = await query(
    `UPDATE events 
     SET ${updateFields.join(', ')}, updated_at = NOW()
     WHERE id = $${paramCount}
     RETURNING *`,
    values
  );

  // Clear cache
  await cache.del(`event:${eventId}`);

  // Notify attendees of changes
  if (updates.start_date || updates.location || updates.address) {
    setImmediate(async () => {
      const attendees = await query(
        'SELECT user_id FROM event_attendees WHERE event_id = $1 AND status = "attending"',
        [eventId]
      );

      for (const attendee of attendees.rows) {
        await sendRealtimeNotification(attendee.user_id, {
          type: 'event_updated',
          title: 'Event Updated',
          body: `"${result.rows[0].title}" has been updated`,
          data: { event_id: eventId }
        });
      }
    });
  }

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Delete/cancel event
const deleteEvent = asyncHandler(async (req, res) => {
  const { eventId } = req.params;
  const userId = req.user.id;
  const { cancellation_reason } = req.body;

  // Check ownership
  const eventCheck = await query(
    'SELECT organizer_id, title FROM events WHERE id = $1',
    [eventId]
  );

  if (eventCheck.rows.length === 0) {
    throw new AppError('Event not found', 404);
  }

  if (eventCheck.rows[0].organizer_id !== userId) {
    throw new AppError('Only the organizer can cancel this event', 403);
  }

  const eventTitle = eventCheck.rows[0].title;

  await withTransaction(async (client) => {
    // Update event status
    await client.query(
      `UPDATE events 
       SET status = 'cancelled', 
           cancellation_reason = $2,
           cancelled_at = NOW()
       WHERE id = $1`,
      [eventId, cancellation_reason]
    );

    // Notify all attendees
    const attendees = await client.query(
      'SELECT user_id FROM event_attendees WHERE event_id = $1 AND status IN ("attending", "interested")',
      [eventId]
    );

    for (const attendee of attendees.rows) {
      await sendRealtimeNotification(attendee.user_id, {
        type: 'event_cancelled',
        title: 'Event Cancelled',
        body: `"${eventTitle}" has been cancelled`,
        data: { event_id: eventId, reason: cancellation_reason }
      });
    }
  });

  // Clear cache
  await cache.del(`event:${eventId}`);

  res.json({
    success: true,
    message: 'Event cancelled successfully'
  });
});

// RSVP to event
const rsvpEvent = asyncHandler(async (req, res) => {
  const { eventId } = req.params;
  const userId = req.user.id;
  const { status, guests_count = 0, note } = req.body;

  if (!['attending', 'interested', 'not_attending'].includes(status)) {
    throw new AppError('Invalid RSVP status', 400);
  }

  // Get event details
  const eventResult = await query(
    `SELECT e.*, COUNT(ea.id) as current_attendees
     FROM events e
     LEFT JOIN event_attendees ea ON e.id = ea.event_id AND ea.status = 'attending'
     WHERE e.id = $1
     GROUP BY e.id`,
    [eventId]
  );

  if (eventResult.rows.length === 0) {
    throw new AppError('Event not found', 404);
  }

  const event = eventResult.rows[0];

  // Check capacity if attending
  if (status === 'attending' && event.max_attendees) {
    const totalRequested = 1 + (event.allow_guests ? guests_count : 0);
    if (event.current_attendees + totalRequested > event.max_attendees) {
      throw new AppError('Event is at capacity', 400, 'EVENT_FULL');
    }
  }

  // Check if guests allowed
  if (guests_count > 0 && !event.allow_guests) {
    throw new AppError('This event does not allow guests', 400);
  }

  // Update or insert RSVP
  const rsvpResult = await query(
    `INSERT INTO event_attendees (event_id, user_id, status, guests_count, note, responded_at)
     VALUES ($1, $2, $3, $4, $5, NOW())
     ON CONFLICT (event_id, user_id)
     DO UPDATE SET 
       status = $3,
       guests_count = $4,
       note = $5,
       responded_at = NOW()
     RETURNING *`,
    [eventId, userId, status, guests_count, note]
  );

  // Clear event cache
  await cache.del(`event:${eventId}`);

  // Notify organizer
  if (event.organizer_id !== userId && status === 'attending') {
    await sendRealtimeNotification(event.organizer_id, {
      type: 'event_rsvp',
      title: 'New RSVP',
      body: `${req.user.first_name} is attending your event`,
      data: { event_id: eventId, user_id: userId }
    });
  }

  res.json({
    success: true,
    data: rsvpResult.rows[0]
  });
});

// Get event attendees
const getEventAttendees = asyncHandler(async (req, res) => {
  const { eventId } = req.params;
  const { status = 'attending', limit = 50, offset = 0 } = req.query;

  const attendeesResult = await query(
    `SELECT ea.*, u.first_name, u.last_name, u.profile_photo_url, u.bio
     FROM event_attendees ea
     JOIN users u ON ea.user_id = u.id
     WHERE ea.event_id = $1 AND ea.status = $2
     ORDER BY ea.is_organizer DESC, ea.responded_at DESC
     LIMIT $3 OFFSET $4`,
    [eventId, status, limit, offset]
  );

  res.json({
    success: true,
    data: attendeesResult.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Add comment to event
const addEventComment = asyncHandler(async (req, res) => {
  const { eventId } = req.params;
  const userId = req.user.id;
  const { content } = req.body;

  // Verify user can comment (attending or interested)
  const participantCheck = await query(
    'SELECT 1 FROM event_attendees WHERE event_id = $1 AND user_id = $2',
    [eventId, userId]
  );

  if (participantCheck.rows.length === 0) {
    throw new AppError('Must RSVP to comment on event', 403);
  }

  const commentResult = await query(
    `INSERT INTO event_comments (event_id, user_id, content)
     VALUES ($1, $2, $3)
     RETURNING *,
       (SELECT first_name FROM users WHERE id = $2) as user_first_name,
       (SELECT last_name FROM users WHERE id = $2) as user_last_name`,
    [eventId, userId, content]
  );

  // Notify event organizer
  const eventResult = await query(
    'SELECT organizer_id, title FROM events WHERE id = $1',
    [eventId]
  );

  if (eventResult.rows[0].organizer_id !== userId) {
    await sendRealtimeNotification(eventResult.rows[0].organizer_id, {
      type: 'event_comment',
      title: 'New Event Comment',
      body: `${req.user.first_name} commented on "${eventResult.rows[0].title}"`,
      data: { event_id: eventId, comment_id: commentResult.rows[0].id }
    });
  }

  res.status(201).json({
    success: true,
    data: commentResult.rows[0]
  });
});

// Get event comments
const getEventComments = asyncHandler(async (req, res) => {
  const { eventId } = req.params;
  const { limit = 20, offset = 0 } = req.query;

  const commentsResult = await query(
    `SELECT ec.*, u.first_name, u.last_name, u.profile_photo_url
     FROM event_comments ec
     JOIN users u ON ec.user_id = u.id
     WHERE ec.event_id = $1
     ORDER BY ec.created_at DESC
     LIMIT $2 OFFSET $3`,
    [eventId, limit, offset]
  );

  res.json({
    success: true,
    data: commentsResult.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Get nearby events
const getNearbyEvents = asyncHandler(async (req, res) => {
  const { latitude, longitude, radius = 5 } = req.query;
  const userId = req.user.id;

  if (!latitude || !longitude) {
    throw new AppError('Location coordinates required', 400);
  }

  const eventsResult = await query(
    `SELECT e.*, 
            u.first_name as organizer_first_name,
            u.last_name as organizer_last_name,
            ST_Distance(e.location::geography, ST_MakePoint($1, $2)::geography) / 1609.34 as distance_miles,
            ST_X(e.location::geometry) as event_longitude,
            ST_Y(e.location::geometry) as event_latitude
     FROM events e
     JOIN users u ON e.organizer_id = u.id
     WHERE e.status = 'active'
       AND e.is_public = true
       AND e.start_date > NOW()
       AND ST_DWithin(e.location::geography, ST_MakePoint($1, $2)::geography, $3 * 1609.34)
     ORDER BY distance_miles ASC, e.start_date ASC
     LIMIT 20`,
    [longitude, latitude, radius]
  );

  res.json({
    success: true,
    data: eventsResult.rows.map(event => ({
      ...event,
      distance: event.distance_miles.toFixed(1)
    }))
  });
});

// Invite users to event
const inviteToEvent = asyncHandler(async (req, res) => {
  const { eventId } = req.params;
  const userId = req.user.id;
  const { user_ids, message } = req.body;

  // Verify organizer
  const eventResult = await query(
    'SELECT organizer_id, title, is_public FROM events WHERE id = $1',
    [eventId]
  );

  if (eventResult.rows.length === 0) {
    throw new AppError('Event not found', 404);
  }

  if (eventResult.rows[0].organizer_id !== userId) {
    throw new AppError('Only organizer can send invites', 403);
  }

  const event = eventResult.rows[0];
  const invitedUsers = [];

  await withTransaction(async (client) => {
    for (const inviteeId of user_ids) {
      // Check if already invited
      const existingCheck = await client.query(
        'SELECT 1 FROM event_invites WHERE event_id = $1 AND invitee_id = $2',
        [eventId, inviteeId]
      );

      if (existingCheck.rows.length === 0) {
        // Create invite
        await client.query(
          `INSERT INTO event_invites (event_id, inviter_id, invitee_id, message)
           VALUES ($1, $2, $3, $4)`,
          [eventId, userId, inviteeId, message]
        );

        // Send notification
        await sendRealtimeNotification(inviteeId, {
          type: 'event_invite',
          title: 'Event Invitation',
          body: `${req.user.first_name} invited you to "${event.title}"`,
          data: { event_id: eventId, inviter_id: userId }
        });

        invitedUsers.push(inviteeId);
      }
    }
  });

  res.json({
    success: true,
    data: {
      invited_count: invitedUsers.length,
      invited_users: invitedUsers
    }
  });
});

// Get event recommendations
const getEventRecommendations = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { limit = 10 } = req.query;

  // Get user's interests and past events
  const userInterests = await query(
    `SELECT DISTINCT e.event_type, COUNT(*) as interest_score
     FROM event_attendees ea
     JOIN events e ON ea.event_id = e.id
     WHERE ea.user_id = $1 AND ea.status = 'attending'
     GROUP BY e.event_type
     ORDER BY interest_score DESC
     LIMIT 5`,
    [userId]
  );

  const preferredTypes = userInterests.rows.map(r => r.event_type);

  // Get recommendations
  let recommendationsQuery = `
    SELECT e.*, 
           u.first_name as organizer_first_name,
           u.last_name as organizer_last_name,
           u.profile_photo_url as organizer_photo,
           COUNT(DISTINCT ea.id) FILTER (WHERE ea.status = 'attending') as attendee_count,
           CASE 
             WHEN e.event_type = ANY($2) THEN 2
             ELSE 1
           END as relevance_score
    FROM events e
    JOIN users u ON e.organizer_id = u.id
    LEFT JOIN event_attendees ea ON e.id = ea.event_id
    WHERE e.neighborhood_id = $3
      AND e.status = 'active'
      AND e.start_date > NOW()
      AND e.is_public = true
      AND NOT EXISTS (
        SELECT 1 FROM event_attendees 
        WHERE event_id = e.id AND user_id = $1
      )
    GROUP BY e.id, u.first_name, u.last_name, u.profile_photo_url
    ORDER BY relevance_score DESC, e.start_date ASC
    LIMIT $4`;

  const recommendations = await query(
    recommendationsQuery,
    [userId, preferredTypes, req.user.neighborhood_id, limit]
  );

  res.json({
    success: true,
    data: recommendations.rows
  });
});

// Create recurring events
const createRecurringEvents = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { event_data, recurrence_end_date } = req.body;

  if (!event_data.is_recurring || !event_data.recurrence_pattern) {
    throw new AppError('Recurrence pattern required for recurring events', 400);
  }

  const events = [];
  const currentDate = new Date(event_data.start_date);
  const endDate = new Date(recurrence_end_date || 
    new Date(currentDate.getTime() + 365 * 24 * 60 * 60 * 1000)); // Default 1 year

  await withTransaction(async (client) => {
    let recurrenceCount = 0;
    const maxRecurrences = 52; // Max 52 occurrences

    while (currentDate <= endDate && recurrenceCount < maxRecurrences) {
      // Create event for this date
      const eventResult = await client.query(
        `INSERT INTO events 
         (organizer_id, neighborhood_id, title, description, event_type,
          start_date, end_date, location, address, max_attendees,
          is_public, is_recurring, recurrence_pattern, allow_guests,
          require_rsvp, tags, cover_photo_url, parent_event_id)
         VALUES ($1, $2, $3, $4, $5, $6, $7, 
                 ST_MakePoint($8, $9)::geography, $10, $11, $12, $13, $14,
                 $15, $16, $17, $18, $19)
         RETURNING id, start_date`,
        [userId, req.user.neighborhood_id, event_data.title, event_data.description,
         event_data.event_type, currentDate, 
         event_data.end_date ? new Date(currentDate.getTime() + 
          (new Date(event_data.end_date) - new Date(event_data.start_date))) : null,
         event_data.location?.longitude, event_data.location?.latitude,
         event_data.address, event_data.max_attendees, event_data.is_public,
         true, event_data.recurrence_pattern, event_data.allow_guests,
         event_data.require_rsvp, event_data.tags, event_data.cover_photo_url,
         recurrenceCount === 0 ? null : events[0]?.id]
      );

      events.push(eventResult.rows[0]);

      // Add organizer as attendee
      await client.query(
        `INSERT INTO event_attendees (event_id, user_id, status, is_organizer)
         VALUES ($1, $2, 'attending', true)`,
        [eventResult.rows[0].id, userId]
      );

      // Calculate next occurrence
      switch (event_data.recurrence_pattern) {
        case 'daily':
          currentDate.setDate(currentDate.getDate() + 1);
          break;
        case 'weekly':
          currentDate.setDate(currentDate.getDate() + 7);
          break;
        case 'biweekly':
          currentDate.setDate(currentDate.getDate() + 14);
          break;
        case 'monthly':
          currentDate.setMonth(currentDate.getMonth() + 1);
          break;
        default:
          throw new AppError('Invalid recurrence pattern', 400);
      }

      recurrenceCount++;
    }
  });

  res.status(201).json({
    success: true,
    data: {
      events_created: events.length,
      first_event_id: events[0]?.id,
      events: events
    }
  });
});

module.exports = {
  createEvent,
  getEvents,
  getEvent,
  updateEvent,
  deleteEvent,
  rsvpEvent,
  getEventAttendees,
  addEventComment,
  getEventComments,
  getNearbyEvents,
  inviteToEvent,
  getEventRecommendations,
  createRecurringEvents
};