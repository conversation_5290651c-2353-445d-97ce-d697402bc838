const { query, withTransaction } = require('../config/database');
const { cache } = require('../config/redis');
const { AppError, asyncHandler } = require('../middleware/errorHandler');
const logger = require('../config/logger');

// Global search across all content types
const globalSearch = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const neighborhoodId = req.user.neighborhood_id;
  const { 
    q, 
    types = ['posts', 'users', 'events', 'exchange'],
    limit = 10
  } = req.query;

  if (!q || q.trim().length < 2) {
    throw new AppError('Search query must be at least 2 characters', 400);
  }

  const searchTerm = q.trim();
  const results = {
    posts: [],
    users: [],
    events: [],
    exchange: []
  };

  // Save search to history
  setImmediate(async () => {
    await query(
      `INSERT INTO search_history (user_id, search_query, search_type)
       VALUES ($1, $2, 'global')
       ON CONFLICT (user_id, search_query) 
       DO UPDATE SET search_count = search_history.search_count + 1,
                     last_searched_at = NOW()`,
      [userId, searchTerm]
    );
  });

  // Search posts
  if (types.includes('posts')) {
    const postsResult = await query(
      `SELECT p.*, 
              u.first_name, u.last_name, u.profile_photo_url,
              COUNT(DISTINCT pl.id) as likes_count,
              COUNT(DISTINCT pc.id) as comments_count,
              EXISTS(SELECT 1 FROM post_likes WHERE post_id = p.id AND user_id = $1) as is_liked
       FROM posts p
       JOIN users u ON p.user_id = u.id
       LEFT JOIN post_likes pl ON p.id = pl.post_id
       LEFT JOIN post_comments pc ON p.id = pc.post_id
       WHERE p.neighborhood_id = $2
         AND p.is_deleted = false
         AND (p.content ILIKE $3 OR p.hashtags @> ARRAY[$4])
       GROUP BY p.id, u.first_name, u.last_name, u.profile_photo_url
       ORDER BY p.created_at DESC
       LIMIT $5`,
      [userId, neighborhoodId, `%${searchTerm}%`, searchTerm.toLowerCase(), limit]
    );
    results.posts = postsResult.rows;
  }

  // Search users
  if (types.includes('users')) {
    const usersResult = await query(
      `SELECT u.id, u.first_name, u.last_name, u.username, 
              u.profile_photo_url, u.bio, u.neighborhood_id,
              EXISTS(SELECT 1 FROM user_follows WHERE follower_id = $1 AND following_id = u.id) as is_following
       FROM users u
       WHERE u.neighborhood_id = $2
         AND u.is_active = true
         AND u.id != $1
         AND (u.first_name ILIKE $3 OR u.last_name ILIKE $3 OR 
              u.username ILIKE $3 OR u.bio ILIKE $3)
       ORDER BY 
         CASE WHEN u.first_name ILIKE $4 OR u.last_name ILIKE $4 THEN 0 ELSE 1 END,
         u.created_at DESC
       LIMIT $5`,
      [userId, neighborhoodId, `%${searchTerm}%`, `${searchTerm}%`, limit]
    );
    results.users = usersResult.rows;
  }

  // Search events
  if (types.includes('events')) {
    const eventsResult = await query(
      `SELECT e.*, 
              u.first_name as organizer_first_name,
              u.last_name as organizer_last_name,
              COUNT(DISTINCT ea.id) FILTER (WHERE ea.status = 'attending') as attendee_count,
              EXISTS(SELECT 1 FROM event_attendees WHERE event_id = e.id AND user_id = $1) as is_attending
       FROM events e
       JOIN users u ON e.organizer_id = u.id
       LEFT JOIN event_attendees ea ON e.id = ea.event_id
       WHERE e.neighborhood_id = $2
         AND e.status = 'active'
         AND e.start_date > NOW()
         AND (e.title ILIKE $3 OR e.description ILIKE $3)
       GROUP BY e.id, u.first_name, u.last_name
       ORDER BY e.start_date ASC
       LIMIT $4`,
      [userId, neighborhoodId, `%${searchTerm}%`, limit]
    );
    results.events = eventsResult.rows;
  }

  // Search exchange listings
  if (types.includes('exchange')) {
    const exchangeResult = await query(
      `SELECT el.*, 
              u.first_name, u.last_name, u.profile_photo_url,
              EXISTS(SELECT 1 FROM exchange_saved_listings WHERE listing_id = el.id AND user_id = $1) as is_saved
       FROM exchange_listings el
       JOIN users u ON el.user_id = u.id
       WHERE el.neighborhood_id = $2
         AND el.status = 'available'
         AND el.is_active = true
         AND (el.title ILIKE $3 OR el.description ILIKE $3 OR el.tags @> ARRAY[$4])
       ORDER BY el.created_at DESC
       LIMIT $5`,
      [userId, neighborhoodId, `%${searchTerm}%`, searchTerm.toLowerCase(), limit]
    );
    results.exchange = exchangeResult.rows;
  }

  res.json({
    success: true,
    data: results,
    query: searchTerm
  });
});

// Search posts with advanced filters
const searchPosts = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const {
    q,
    hashtags,
    author_id,
    date_from,
    date_to,
    has_media,
    sort = 'relevance',
    limit = 20,
    offset = 0
  } = req.query;

  let queryStr = `
    SELECT p.*, 
           u.first_name, u.last_name, u.username, u.profile_photo_url,
           COUNT(DISTINCT pl.id) as likes_count,
           COUNT(DISTINCT pc.id) as comments_count,
           COUNT(DISTINCT ps.id) as shares_count,
           EXISTS(SELECT 1 FROM post_likes WHERE post_id = p.id AND user_id = $1) as is_liked,
           EXISTS(SELECT 1 FROM post_bookmarks WHERE post_id = p.id AND user_id = $1) as is_bookmarked`;

  // Add relevance score for sorting
  if (q && sort === 'relevance') {
    queryStr += `,
           ts_rank(to_tsvector('english', p.content), plainto_tsquery('english', $2)) as relevance`;
  }

  queryStr += `
    FROM posts p
    JOIN users u ON p.user_id = u.id
    LEFT JOIN post_likes pl ON p.id = pl.post_id
    LEFT JOIN post_comments pc ON p.id = pc.post_id
    LEFT JOIN post_shares ps ON p.id = ps.post_id
    WHERE p.neighborhood_id = $3
      AND p.is_deleted = false`;

  const params = [userId];
  let paramCount = 1;

  if (q) {
    params.push(q);
    paramCount++;
  }

  params.push(req.user.neighborhood_id);
  paramCount++;

  // Apply filters
  if (q) {
    queryStr += ` AND (p.content ILIKE $${++paramCount} OR to_tsvector('english', p.content) @@ plainto_tsquery('english', $${paramCount - 1}))`;
    params.push(`%${q}%`);
  }

  if (hashtags) {
    const hashtagArray = hashtags.split(',').map(tag => tag.trim().toLowerCase());
    queryStr += ` AND p.hashtags && $${++paramCount}`;
    params.push(hashtagArray);
  }

  if (author_id) {
    queryStr += ` AND p.user_id = $${++paramCount}`;
    params.push(author_id);
  }

  if (date_from) {
    queryStr += ` AND p.created_at >= $${++paramCount}`;
    params.push(date_from);
  }

  if (date_to) {
    queryStr += ` AND p.created_at <= $${++paramCount}`;
    params.push(date_to);
  }

  if (has_media === 'true') {
    queryStr += ` AND (p.media_urls IS NOT NULL AND array_length(p.media_urls, 1) > 0)`;
  }

  queryStr += `
    GROUP BY p.id, u.first_name, u.last_name, u.username, u.profile_photo_url`;

  if (q && sort === 'relevance') {
    queryStr += `, relevance`;
  }

  // Apply sorting
  switch (sort) {
    case 'relevance':
      queryStr += q ? ` ORDER BY relevance DESC, p.created_at DESC` : ` ORDER BY p.created_at DESC`;
      break;
    case 'newest':
      queryStr += ` ORDER BY p.created_at DESC`;
      break;
    case 'oldest':
      queryStr += ` ORDER BY p.created_at ASC`;
      break;
    case 'most_liked':
      queryStr += ` ORDER BY likes_count DESC`;
      break;
    case 'most_commented':
      queryStr += ` ORDER BY comments_count DESC`;
      break;
    default:
      queryStr += ` ORDER BY p.created_at DESC`;
  }

  queryStr += ` LIMIT $${++paramCount} OFFSET $${++paramCount}`;
  params.push(limit, offset);

  const result = await query(queryStr, params);

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Search users
const searchUsers = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const {
    q,
    skills,
    interests,
    location,
    radius,
    sort = 'relevance',
    limit = 20,
    offset = 0
  } = req.query;

  let queryStr = `
    SELECT u.id, u.first_name, u.last_name, u.username, 
           u.profile_photo_url, u.bio, u.skills, u.interests,
           u.neighborhood_id, u.created_at,
           EXISTS(SELECT 1 FROM user_follows WHERE follower_id = $1 AND following_id = u.id) as is_following,
           EXISTS(SELECT 1 FROM user_blocks WHERE blocker_id = $1 AND blocked_id = u.id) as is_blocked,
           COUNT(DISTINCT uf.id) as followers_count`;

  if (location && radius) {
    queryStr += `,
           ST_Distance(u.location::geography, ST_MakePoint($2, $3)::geography) / 1609.34 as distance_miles`;
  }

  queryStr += `
    FROM users u
    LEFT JOIN user_follows uf ON u.id = uf.following_id
    WHERE u.is_active = true
      AND u.id != $1
      AND NOT EXISTS(SELECT 1 FROM user_blocks WHERE blocker_id = u.id AND blocked_id = $1)`;

  const params = [userId];
  let paramCount = 1;

  if (location && radius) {
    params.push(location.longitude, location.latitude);
    paramCount += 2;
  }

  // Apply filters
  if (q) {
    queryStr += ` AND (u.first_name ILIKE $${++paramCount} OR u.last_name ILIKE $${paramCount} OR 
                       u.username ILIKE $${paramCount} OR u.bio ILIKE $${paramCount})`;
    params.push(`%${q}%`);
  }

  if (skills) {
    const skillsArray = skills.split(',').map(s => s.trim());
    queryStr += ` AND u.skills && $${++paramCount}`;
    params.push(skillsArray);
  }

  if (interests) {
    const interestsArray = interests.split(',').map(i => i.trim());
    queryStr += ` AND u.interests && $${++paramCount}`;
    params.push(interestsArray);
  }

  if (location && radius) {
    queryStr += ` AND ST_DWithin(u.location::geography, ST_MakePoint($2, $3)::geography, $${++paramCount} * 1609.34)`;
    params.push(radius);
  }

  queryStr += ` GROUP BY u.id`;

  if (location && radius) {
    queryStr += `, distance_miles`;
  }

  // Apply sorting
  switch (sort) {
    case 'relevance':
      queryStr += q ? ` ORDER BY CASE WHEN u.username ILIKE $${paramCount} THEN 0 ELSE 1 END` : '';
      queryStr += ` ORDER BY followers_count DESC`;
      break;
    case 'newest':
      queryStr += ` ORDER BY u.created_at DESC`;
      break;
    case 'nearest':
      queryStr += location ? ` ORDER BY distance_miles ASC` : ` ORDER BY u.created_at DESC`;
      break;
    case 'most_followed':
      queryStr += ` ORDER BY followers_count DESC`;
      break;
    default:
      queryStr += ` ORDER BY u.created_at DESC`;
  }

  queryStr += ` LIMIT $${++paramCount} OFFSET $${++paramCount}`;
  params.push(limit, offset);

  const result = await query(queryStr, params);

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Search events
const searchEvents = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const {
    q,
    event_type,
    date_from,
    date_to,
    location,
    radius,
    only_available,
    sort = 'date',
    limit = 20,
    offset = 0
  } = req.query;

  let queryStr = `
    SELECT e.*, 
           u.first_name as organizer_first_name,
           u.last_name as organizer_last_name,
           u.profile_photo_url as organizer_photo,
           COUNT(DISTINCT ea.id) FILTER (WHERE ea.status = 'attending') as attendee_count,
           e.max_attendees,
           EXISTS(SELECT 1 FROM event_attendees WHERE event_id = e.id AND user_id = $1) as is_attending`;

  if (location && radius) {
    queryStr += `,
           ST_Distance(e.location::geography, ST_MakePoint($2, $3)::geography) / 1609.34 as distance_miles`;
  }

  queryStr += `
    FROM events e
    JOIN users u ON e.organizer_id = u.id
    LEFT JOIN event_attendees ea ON e.id = ea.event_id
    WHERE e.status = 'active'
      AND (e.is_public = true OR EXISTS(
        SELECT 1 FROM event_attendees WHERE event_id = e.id AND user_id = $1
      ))`;

  const params = [userId];
  let paramCount = 1;

  if (location && radius) {
    params.push(location.longitude, location.latitude);
    paramCount += 2;
  }

  // Apply filters
  if (q) {
    queryStr += ` AND (e.title ILIKE $${++paramCount} OR e.description ILIKE $${paramCount})`;
    params.push(`%${q}%`);
  }

  if (event_type) {
    queryStr += ` AND e.event_type = $${++paramCount}`;
    params.push(event_type);
  }

  if (date_from) {
    queryStr += ` AND e.start_date >= $${++paramCount}`;
    params.push(date_from);
  }

  if (date_to) {
    queryStr += ` AND e.start_date <= $${++paramCount}`;
    params.push(date_to);
  }

  if (location && radius) {
    queryStr += ` AND ST_DWithin(e.location::geography, ST_MakePoint($2, $3)::geography, $${++paramCount} * 1609.34)`;
    params.push(radius);
  }

  if (only_available === 'true') {
    queryStr += ` AND (e.max_attendees IS NULL OR 
                       (SELECT COUNT(*) FROM event_attendees WHERE event_id = e.id AND status = 'attending') < e.max_attendees)`;
  }

  queryStr += ` GROUP BY e.id, u.first_name, u.last_name, u.profile_photo_url`;

  if (location && radius) {
    queryStr += `, distance_miles`;
  }

  // Apply sorting
  switch (sort) {
    case 'date':
      queryStr += ` ORDER BY e.start_date ASC`;
      break;
    case 'newest':
      queryStr += ` ORDER BY e.created_at DESC`;
      break;
    case 'popular':
      queryStr += ` ORDER BY attendee_count DESC`;
      break;
    case 'nearest':
      queryStr += location ? ` ORDER BY distance_miles ASC` : ` ORDER BY e.start_date ASC`;
      break;
    default:
      queryStr += ` ORDER BY e.start_date ASC`;
  }

  queryStr += ` LIMIT $${++paramCount} OFFSET $${++paramCount}`;
  params.push(limit, offset);

  const result = await query(queryStr, params);

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Search exchange listings
const searchExchange = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const {
    q,
    category,
    exchange_type,
    condition,
    location,
    radius,
    sort = 'newest',
    limit = 20,
    offset = 0
  } = req.query;

  let queryStr = `
    SELECT el.*, 
           u.first_name, u.last_name, u.profile_photo_url,
           EXISTS(SELECT 1 FROM exchange_saved_listings WHERE listing_id = el.id AND user_id = $1) as is_saved,
           EXISTS(SELECT 1 FROM exchange_requests WHERE listing_id = el.id AND requester_id = $1) as has_requested`;

  if (location && radius) {
    queryStr += `,
           ST_Distance(el.location::geography, ST_MakePoint($2, $3)::geography) / 1609.34 as distance_miles`;
  }

  queryStr += `
    FROM exchange_listings el
    JOIN users u ON el.user_id = u.id
    WHERE el.status = 'available'
      AND el.is_active = true
      AND el.expires_at > NOW()`;

  const params = [userId];
  let paramCount = 1;

  if (location && radius) {
    params.push(location.longitude, location.latitude);
    paramCount += 2;
  }

  // Apply filters
  if (q) {
    queryStr += ` AND (el.title ILIKE $${++paramCount} OR el.description ILIKE $${paramCount} OR el.tags @> ARRAY[$${++paramCount}])`;
    params.push(`%${q}%`, q.toLowerCase());
    paramCount++;
  }

  if (category) {
    queryStr += ` AND el.category = $${++paramCount}`;
    params.push(category);
  }

  if (exchange_type) {
    queryStr += ` AND el.exchange_type = $${++paramCount}`;
    params.push(exchange_type);
  }

  if (condition) {
    queryStr += ` AND el.condition = $${++paramCount}`;
    params.push(condition);
  }

  if (location && radius) {
    queryStr += ` AND ST_DWithin(el.location::geography, ST_MakePoint($2, $3)::geography, $${++paramCount} * 1609.34)`;
    params.push(radius);
  }

  // Apply sorting
  switch (sort) {
    case 'newest':
      queryStr += ` ORDER BY el.created_at DESC`;
      break;
    case 'oldest':
      queryStr += ` ORDER BY el.created_at ASC`;
      break;
    case 'nearest':
      queryStr += location ? ` ORDER BY distance_miles ASC` : ` ORDER BY el.created_at DESC`;
      break;
    case 'most_viewed':
      queryStr += ` ORDER BY el.views_count DESC`;
      break;
    default:
      queryStr += ` ORDER BY el.created_at DESC`;
  }

  queryStr += ` LIMIT $${++paramCount} OFFSET $${++paramCount}`;
  params.push(limit, offset);

  const result = await query(queryStr, params);

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Get search suggestions/autocomplete
const getSearchSuggestions = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { q, type = 'all' } = req.query;

  if (!q || q.trim().length < 2) {
    throw new AppError('Query must be at least 2 characters', 400);
  }

  const searchTerm = q.trim().toLowerCase();
  const suggestions = [];

  // Get from recent searches
  const recentSearches = await query(
    `SELECT DISTINCT search_query 
     FROM search_history 
     WHERE user_id = $1 
       AND search_query ILIKE $2
     ORDER BY last_searched_at DESC
     LIMIT 5`,
    [userId, `${searchTerm}%`]
  );

  suggestions.push(...recentSearches.rows.map(r => ({
    type: 'recent',
    value: r.search_query
  })));

  // Get popular hashtags
  if (type === 'all' || type === 'hashtags') {
    const hashtagsResult = await query(
      `SELECT DISTINCT unnest(hashtags) as hashtag, COUNT(*) as count
       FROM posts
       WHERE neighborhood_id = $1
         AND is_deleted = false
       GROUP BY hashtag
       HAVING unnest(hashtags) ILIKE $2
       ORDER BY count DESC
       LIMIT 5`,
      [req.user.neighborhood_id, `${searchTerm}%`]
    );

    suggestions.push(...hashtagsResult.rows.map(r => ({
      type: 'hashtag',
      value: r.hashtag,
      count: parseInt(r.count)
    })));
  }

  // Get user suggestions
  if (type === 'all' || type === 'users') {
    const usersResult = await query(
      `SELECT id, first_name, last_name, username, profile_photo_url
       FROM users
       WHERE neighborhood_id = $1
         AND is_active = true
         AND (first_name ILIKE $2 OR last_name ILIKE $2 OR username ILIKE $2)
       ORDER BY 
         CASE WHEN username ILIKE $3 THEN 0 ELSE 1 END,
         created_at DESC
       LIMIT 5`,
      [req.user.neighborhood_id, `%${searchTerm}%`, `${searchTerm}%`]
    );

    suggestions.push(...usersResult.rows.map(r => ({
      type: 'user',
      value: `${r.first_name} ${r.last_name}`,
      username: r.username,
      id: r.id,
      photo: r.profile_photo_url
    })));
  }

  res.json({
    success: true,
    data: suggestions
  });
});

// Get trending searches
const getTrendingSearches = asyncHandler(async (req, res) => {
  const { period = 'day', limit = 10 } = req.query;

  let timeFilter;
  switch (period) {
    case 'hour':
      timeFilter = "last_searched_at > NOW() - INTERVAL '1 hour'";
      break;
    case 'day':
      timeFilter = "last_searched_at > NOW() - INTERVAL '24 hours'";
      break;
    case 'week':
      timeFilter = "last_searched_at > NOW() - INTERVAL '7 days'";
      break;
    default:
      timeFilter = "last_searched_at > NOW() - INTERVAL '24 hours'";
  }

  const trendingResult = await query(
    `SELECT search_query, COUNT(DISTINCT user_id) as unique_users, SUM(search_count) as total_searches
     FROM search_history
     WHERE ${timeFilter}
       AND search_query NOT IN (SELECT blocked_term FROM blocked_search_terms)
     GROUP BY search_query
     ORDER BY unique_users DESC, total_searches DESC
     LIMIT $1`,
    [limit]
  );

  // Get trending hashtags
  const trendingHashtags = await query(
    `SELECT unnest(hashtags) as hashtag, COUNT(*) as post_count
     FROM posts
     WHERE created_at > NOW() - INTERVAL '24 hours'
       AND is_deleted = false
     GROUP BY hashtag
     ORDER BY post_count DESC
     LIMIT $1`,
    [limit]
  );

  res.json({
    success: true,
    data: {
      trending_searches: trendingResult.rows,
      trending_hashtags: trendingHashtags.rows
    }
  });
});

// Save search query
const saveSearchQuery = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { query: searchQuery, name, filters } = req.body;

  if (!searchQuery || searchQuery.trim().length < 2) {
    throw new AppError('Search query must be at least 2 characters', 400);
  }

  // Check saved searches limit
  const savedCount = await query(
    'SELECT COUNT(*) as count FROM saved_searches WHERE user_id = $1',
    [userId]
  );

  if (savedCount.rows[0].count >= 20) {
    throw new AppError('Maximum saved searches limit reached (20)', 400);
  }

  const result = await query(
    `INSERT INTO saved_searches (user_id, search_query, search_name, filters)
     VALUES ($1, $2, $3, $4)
     ON CONFLICT (user_id, search_query)
     DO UPDATE SET search_name = $3, filters = $4, updated_at = NOW()
     RETURNING *`,
    [userId, searchQuery, name || searchQuery, filters || {}]
  );

  res.status(201).json({
    success: true,
    data: result.rows[0]
  });
});

// Get saved searches
const getSavedSearches = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const result = await query(
    `SELECT * FROM saved_searches
     WHERE user_id = $1
     ORDER BY created_at DESC`,
    [userId]
  );

  res.json({
    success: true,
    data: result.rows
  });
});

// Delete saved search
const deleteSavedSearch = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { searchId } = req.params;

  const result = await query(
    'DELETE FROM saved_searches WHERE id = $1 AND user_id = $2 RETURNING id',
    [searchId, userId]
  );

  if (result.rows.length === 0) {
    throw new AppError('Saved search not found', 404);
  }

  res.json({
    success: true,
    message: 'Search deleted successfully'
  });
});

// Get search history
const getSearchHistory = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { limit = 20, offset = 0 } = req.query;

  const result = await query(
    `SELECT search_query, search_type, search_count, last_searched_at
     FROM search_history
     WHERE user_id = $1
     ORDER BY last_searched_at DESC
     LIMIT $2 OFFSET $3`,
    [userId, limit, offset]
  );

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Clear search history
const clearSearchHistory = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  await query(
    'DELETE FROM search_history WHERE user_id = $1',
    [userId]
  );

  res.json({
    success: true,
    message: 'Search history cleared'
  });
});

// Get search filters and facets
const getSearchFilters = asyncHandler(async (req, res) => {
  const neighborhoodId = req.user.neighborhood_id;
  const { type } = req.query;

  const filters = {};

  if (!type || type === 'posts') {
    // Get post hashtags
    const hashtagsResult = await query(
      `SELECT DISTINCT unnest(hashtags) as hashtag, COUNT(*) as count
       FROM posts
       WHERE neighborhood_id = $1 AND is_deleted = false
       GROUP BY hashtag
       ORDER BY count DESC
       LIMIT 20`,
      [neighborhoodId]
    );
    filters.post_hashtags = hashtagsResult.rows;
  }

  if (!type || type === 'events') {
    // Get event types
    const eventTypesResult = await query(
      `SELECT DISTINCT event_type, COUNT(*) as count
       FROM events
       WHERE neighborhood_id = $1 AND status = 'active'
       GROUP BY event_type
       ORDER BY count DESC`,
      [neighborhoodId]
    );
    filters.event_types = eventTypesResult.rows;
  }

  if (!type || type === 'exchange') {
    // Get exchange categories
    const categoriesResult = await query(
      `SELECT category, COUNT(*) as count
       FROM exchange_listings
       WHERE neighborhood_id = $1 AND status = 'available'
       GROUP BY category
       ORDER BY count DESC`,
      [neighborhoodId]
    );
    filters.exchange_categories = categoriesResult.rows;

    // Get exchange types
    filters.exchange_types = [
      { value: 'give_away', label: 'Give Away' },
      { value: 'lend', label: 'Lend' },
      { value: 'borrow_request', label: 'Looking For' }
    ];

    // Get conditions
    filters.conditions = [
      { value: 'new', label: 'New' },
      { value: 'like_new', label: 'Like New' },
      { value: 'good', label: 'Good' },
      { value: 'fair', label: 'Fair' },
      { value: 'poor', label: 'Poor' }
    ];
  }

  res.json({
    success: true,
    data: filters
  });
});

module.exports = {
  globalSearch,
  searchPosts,
  searchUsers,
  searchEvents,
  searchExchange,
  getSearchSuggestions,
  getTrendingSearches,
  saveSearchQuery,
  getSavedSearches,
  deleteSavedSearch,
  getSearchHistory,
  clearSearchHistory,
  getSearchFilters
};