const { query, withTransaction } = require('../config/database');
const { cache } = require('../config/redis');
const { AppError, asyncHandler } = require('../middleware/errorHandler');
const logger = require('../config/logger');
const { 
  uploadToS3, 
  uploadImageWithProcessing,
  deleteFromS3,
  deleteMultipleFromS3,
  getSignedUrl,
  detectFileType,
  calculateUserStorage
} = require('../services/mediaUpload');

// Storage limits per user (in bytes)
const STORAGE_LIMITS = {
  free: 1 * 1024 * 1024 * 1024, // 1GB
  premium: 10 * 1024 * 1024 * 1024, // 10GB
  unlimited: null
};

// Upload single or multiple files
const uploadMedia = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const neighborhoodId = req.user.neighborhood_id;
  const files = req.files || [req.file];
  const { albumId, captions = {} } = req.body;

  if (!files || files.length === 0) {
    throw new AppError('No files provided', 400);
  }

  // Check storage limit
  const storageUsage = await calculateUserStorage(userId);
  const userLimit = STORAGE_LIMITS[req.user.subscription_type || 'free'];
  
  if (userLimit) {
    const totalNewSize = files.reduce((sum, file) => sum + file.size, 0);
    if (storageUsage.totalSize + totalNewSize > userLimit) {
      throw new AppError('Storage limit exceeded', 400, 'STORAGE_LIMIT_EXCEEDED');
    }
  }

  const uploadedMedia = [];

  await withTransaction(async (client) => {
    for (const file of files) {
      const fileType = detectFileType(file.mimetype);
      let uploadResult;

      // Process based on file type
      if (fileType === 'image') {
        uploadResult = await uploadImageWithProcessing(
          file, 
          'media/images', 
          userId,
          {
            maxWidth: 2048,
            maxHeight: 2048,
            quality: 90,
            generateThumbnail: true
          }
        );
      } else {
        uploadResult = await uploadToS3(file, `media/${fileType}s`, userId);
      }

      // Save to database
      const mediaResult = await client.query(
        `INSERT INTO media 
         (user_id, neighborhood_id, file_url, thumbnail_url, file_type, 
          mime_type, file_name, file_size, width, height, metadata, upload_status)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 'completed')
         RETURNING *`,
        [
          userId, neighborhoodId, uploadResult.url, uploadResult.thumbnailUrl,
          fileType, file.mimetype, file.originalname, file.size,
          uploadResult.width, uploadResult.height, { s3Key: uploadResult.key }
        ]
      );

      const media = mediaResult.rows[0];

      // Add to album if specified
      if (albumId) {
        const caption = captions[file.originalname] || null;
        await client.query(
          `INSERT INTO media_album_items (album_id, media_id, caption)
           VALUES ($1, $2, $3)`,
          [albumId, media.id, caption]
        );
      }

      // Track usage for other entities
      if (req.body.entityType && req.body.entityId) {
        await client.query(
          `INSERT INTO media_usage (media_id, entity_type, entity_id, usage_type)
           VALUES ($1, $2, $3, $4)`,
          [media.id, req.body.entityType, req.body.entityId, req.body.usageType]
        );
      }

      uploadedMedia.push(media);
    }
  });

  res.status(201).json({
    success: true,
    data: uploadedMedia.length === 1 ? uploadedMedia[0] : uploadedMedia
  });
});

// Get media details
const getMedia = asyncHandler(async (req, res) => {
  const { mediaId } = req.params;
  const userId = req.user.id;

  const mediaResult = await query(
    `SELECT m.*, 
            u.first_name, u.last_name, u.username,
            COUNT(DISTINCT mu.id) as usage_count
     FROM media m
     JOIN users u ON m.user_id = u.id
     LEFT JOIN media_usage mu ON m.id = mu.media_id
     WHERE m.id = $1 AND m.is_deleted = false
     GROUP BY m.id, u.first_name, u.last_name, u.username`,
    [mediaId]
  );

  if (mediaResult.rows.length === 0) {
    throw new AppError('Media not found', 404);
  }

  const media = mediaResult.rows[0];

  // Check access permissions
  if (media.user_id !== userId && media.neighborhood_id !== req.user.neighborhood_id) {
    throw new AppError('Access denied', 403);
  }

  res.json({
    success: true,
    data: media
  });
});

// Delete media
const deleteMedia = asyncHandler(async (req, res) => {
  const { mediaId } = req.params;
  const userId = req.user.id;

  const mediaResult = await query(
    'SELECT * FROM media WHERE id = $1 AND user_id = $2',
    [mediaId, userId]
  );

  if (mediaResult.rows.length === 0) {
    throw new AppError('Media not found or access denied', 404);
  }

  const media = mediaResult.rows[0];

  // Check if media is in use
  const usageCheck = await query(
    'SELECT COUNT(*) as count FROM media_usage WHERE media_id = $1',
    [mediaId]
  );

  if (usageCheck.rows[0].count > 0) {
    throw new AppError('Cannot delete media that is in use', 400);
  }

  await withTransaction(async (client) => {
    // Soft delete from database
    await client.query(
      `UPDATE media 
       SET is_deleted = true, deleted_at = NOW() 
       WHERE id = $1`,
      [mediaId]
    );

    // Remove from albums
    await client.query(
      'DELETE FROM media_album_items WHERE media_id = $1',
      [mediaId]
    );

    // Delete from S3
    const urls = [media.file_url];
    if (media.thumbnail_url) urls.push(media.thumbnail_url);
    await deleteMultipleFromS3(urls);
  });

  res.json({
    success: true,
    message: 'Media deleted successfully'
  });
});

// Update media metadata
const updateMedia = asyncHandler(async (req, res) => {
  const { mediaId } = req.params;
  const userId = req.user.id;
  const { caption, tags, metadata } = req.body;

  const mediaResult = await query(
    'SELECT * FROM media WHERE id = $1 AND user_id = $2',
    [mediaId, userId]
  );

  if (mediaResult.rows.length === 0) {
    throw new AppError('Media not found or access denied', 404);
  }

  const updates = [];
  const values = [];
  let paramCount = 1;

  if (caption !== undefined) {
    updates.push(`metadata = jsonb_set(metadata, '{caption}', $${paramCount++})`);
    values.push(JSON.stringify(caption));
  }

  if (tags !== undefined) {
    updates.push(`metadata = jsonb_set(metadata, '{tags}', $${paramCount++})`);
    values.push(JSON.stringify(tags));
  }

  if (metadata !== undefined) {
    updates.push(`metadata = metadata || $${paramCount++}`);
    values.push(JSON.stringify(metadata));
  }

  if (updates.length === 0) {
    throw new AppError('No updates provided', 400);
  }

  values.push(mediaId);

  const updateResult = await query(
    `UPDATE media 
     SET ${updates.join(', ')}, updated_at = NOW()
     WHERE id = $${paramCount}
     RETURNING *`,
    values
  );

  res.json({
    success: true,
    data: updateResult.rows[0]
  });
});

// Moderate media (admin/moderator only)
const moderateMedia = asyncHandler(async (req, res) => {
  const { mediaId } = req.params;
  const userId = req.user.id;
  const { status, notes } = req.body;

  // Check if user is moderator
  if (!req.user.is_moderator && !req.user.is_admin) {
    throw new AppError('Moderator access required', 403);
  }

  if (!['approved', 'rejected', 'flagged'].includes(status)) {
    throw new AppError('Invalid moderation status', 400);
  }

  const result = await query(
    `UPDATE media 
     SET moderation_status = $1, 
         moderation_notes = $2,
         moderated_by = $3,
         moderated_at = NOW()
     WHERE id = $4
     RETURNING *`,
    [status, notes, userId, mediaId]
  );

  if (result.rows.length === 0) {
    throw new AppError('Media not found', 404);
  }

  // If rejected, notify user
  if (status === 'rejected') {
    const { createNotification } = require('./notifications');
    await createNotification(
      result.rows[0].user_id,
      'media_rejected',
      'Media Rejected',
      `Your uploaded media was rejected. ${notes || ''}`,
      { media_id: mediaId }
    );
  }

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Get user's media gallery
const getGallery = asyncHandler(async (req, res) => {
  const targetUserId = req.params.userId || req.user.id;
  const {
    fileType,
    albumId,
    sort = 'newest',
    limit = 20,
    offset = 0
  } = req.query;

  let queryStr = `
    SELECT m.*, 
           COUNT(DISTINCT mai.album_id) as album_count,
           COUNT(DISTINCT mu.id) as usage_count
    FROM media m
    LEFT JOIN media_album_items mai ON m.id = mai.media_id
    LEFT JOIN media_usage mu ON m.id = mu.media_id
    WHERE m.user_id = $1 
      AND m.is_deleted = false
      AND m.moderation_status = 'approved'`;

  const params = [targetUserId];
  let paramCount = 1;

  if (fileType) {
    queryStr += ` AND m.file_type = $${++paramCount}`;
    params.push(fileType);
  }

  if (albumId) {
    queryStr += ` AND EXISTS(
      SELECT 1 FROM media_album_items 
      WHERE media_id = m.id AND album_id = $${++paramCount}
    )`;
    params.push(albumId);
  }

  queryStr += ` GROUP BY m.id`;

  // Apply sorting
  switch (sort) {
    case 'newest':
      queryStr += ` ORDER BY m.created_at DESC`;
      break;
    case 'oldest':
      queryStr += ` ORDER BY m.created_at ASC`;
      break;
    case 'largest':
      queryStr += ` ORDER BY m.file_size DESC`;
      break;
    case 'smallest':
      queryStr += ` ORDER BY m.file_size ASC`;
      break;
    default:
      queryStr += ` ORDER BY m.created_at DESC`;
  }

  queryStr += ` LIMIT $${++paramCount} OFFSET $${++paramCount}`;
  params.push(limit, offset);

  const result = await query(queryStr, params);

  // Get total count
  const countResult = await query(
    `SELECT COUNT(*) as total
     FROM media 
     WHERE user_id = $1 
       AND is_deleted = false 
       AND moderation_status = 'approved'`,
    [targetUserId]
  );

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: parseInt(countResult.rows[0].total)
    }
  });
});

// Create media album
const createAlbum = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { name, description, isPublic = true, mediaIds = [] } = req.body;

  if (!name || name.trim().length === 0) {
    throw new AppError('Album name is required', 400);
  }

  const albumData = await withTransaction(async (client) => {
    // Create album
    const albumResult = await client.query(
      `INSERT INTO media_albums 
       (user_id, neighborhood_id, name, description, is_public)
       VALUES ($1, $2, $3, $4, $5)
       RETURNING *`,
      [userId, req.user.neighborhood_id, name, description, isPublic]
    );

    const album = albumResult.rows[0];

    // Add media items if provided
    if (mediaIds.length > 0) {
      // Verify ownership of media
      const mediaCheck = await client.query(
        'SELECT id FROM media WHERE id = ANY($1) AND user_id = $2',
        [mediaIds, userId]
      );

      if (mediaCheck.rows.length !== mediaIds.length) {
        throw new AppError('Some media items not found or access denied', 400);
      }

      // Add media to album
      for (let i = 0; i < mediaIds.length; i++) {
        await client.query(
          `INSERT INTO media_album_items (album_id, media_id, position)
           VALUES ($1, $2, $3)`,
          [album.id, mediaIds[i], i]
        );
      }

      // Set first media as cover
      await client.query(
        'UPDATE media_albums SET cover_media_id = $1 WHERE id = $2',
        [mediaIds[0], album.id]
      );
    }

    return album;
  });

  res.status(201).json({
    success: true,
    data: albumData
  });
});

// Get user's albums
const getAlbums = asyncHandler(async (req, res) => {
  const targetUserId = req.params.userId || req.user.id;
  const { limit = 20, offset = 0 } = req.query;

  // Check if viewing own albums or public albums
  const isOwnAlbums = targetUserId === req.user.id;

  let queryStr = `
    SELECT ma.*, 
           u.first_name, u.last_name, u.username,
           cm.thumbnail_url as cover_thumbnail_url
    FROM media_albums ma
    JOIN users u ON ma.user_id = u.id
    LEFT JOIN media cm ON ma.cover_media_id = cm.id
    WHERE ma.user_id = $1`;

  if (!isOwnAlbums) {
    queryStr += ` AND ma.is_public = true`;
  }

  queryStr += ` ORDER BY ma.created_at DESC LIMIT $2 OFFSET $3`;

  const result = await query(queryStr, [targetUserId, limit, offset]);

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Update album
const updateAlbum = asyncHandler(async (req, res) => {
  const { albumId } = req.params;
  const userId = req.user.id;
  const { name, description, isPublic, coverMediaId, addMediaIds, removeMediaIds } = req.body;

  // Check ownership
  const albumCheck = await query(
    'SELECT * FROM media_albums WHERE id = $1 AND user_id = $2',
    [albumId, userId]
  );

  if (albumCheck.rows.length === 0) {
    throw new AppError('Album not found or access denied', 404);
  }

  await withTransaction(async (client) => {
    // Update album details
    const updates = [];
    const values = [];
    let paramCount = 1;

    if (name !== undefined) {
      updates.push(`name = $${paramCount++}`);
      values.push(name);
    }

    if (description !== undefined) {
      updates.push(`description = $${paramCount++}`);
      values.push(description);
    }

    if (isPublic !== undefined) {
      updates.push(`is_public = $${paramCount++}`);
      values.push(isPublic);
    }

    if (coverMediaId !== undefined) {
      updates.push(`cover_media_id = $${paramCount++}`);
      values.push(coverMediaId);
    }

    if (updates.length > 0) {
      values.push(albumId);
      await client.query(
        `UPDATE media_albums 
         SET ${updates.join(', ')}, updated_at = NOW()
         WHERE id = $${paramCount}`,
        values
      );
    }

    // Add new media
    if (addMediaIds && addMediaIds.length > 0) {
      for (const mediaId of addMediaIds) {
        await client.query(
          `INSERT INTO media_album_items (album_id, media_id)
           VALUES ($1, $2)
           ON CONFLICT DO NOTHING`,
          [albumId, mediaId]
        );
      }
    }

    // Remove media
    if (removeMediaIds && removeMediaIds.length > 0) {
      await client.query(
        'DELETE FROM media_album_items WHERE album_id = $1 AND media_id = ANY($2)',
        [albumId, removeMediaIds]
      );
    }
  });

  // Get updated album
  const updatedAlbum = await query(
    'SELECT * FROM media_albums WHERE id = $1',
    [albumId]
  );

  res.json({
    success: true,
    data: updatedAlbum.rows[0]
  });
});

// Generate thumbnail for media
const generateThumbnail = asyncHandler(async (req, res) => {
  const { mediaId } = req.params;
  const userId = req.user.id;
  const { timestamp = '00:00:01' } = req.body;

  // Get media
  const mediaResult = await query(
    'SELECT * FROM media WHERE id = $1 AND user_id = $2',
    [mediaId, userId]
  );

  if (mediaResult.rows.length === 0) {
    throw new AppError('Media not found or access denied', 404);
  }

  const media = mediaResult.rows[0];

  if (media.file_type !== 'video') {
    throw new AppError('Thumbnails can only be generated for videos', 400);
  }

  // Add to processing queue
  await query(
    `INSERT INTO media_processing_jobs 
     (media_id, job_type, priority, status)
     VALUES ($1, 'thumbnail', 5, 'queued')
     RETURNING id`,
    [mediaId]
  );

  res.json({
    success: true,
    message: 'Thumbnail generation queued',
    data: { mediaId }
  });
});

// Compress media
const compressMedia = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { mediaIds, quality = 80 } = req.body;

  if (!mediaIds || mediaIds.length === 0) {
    throw new AppError('Media IDs required', 400);
  }

  // Verify ownership
  const mediaCheck = await query(
    'SELECT id, file_type FROM media WHERE id = ANY($1) AND user_id = $2',
    [mediaIds, userId]
  );

  if (mediaCheck.rows.length !== mediaIds.length) {
    throw new AppError('Some media not found or access denied', 400);
  }

  // Queue compression jobs
  const jobs = [];
  for (const media of mediaCheck.rows) {
    if (['image', 'video'].includes(media.file_type)) {
      const jobResult = await query(
        `INSERT INTO media_processing_jobs 
         (media_id, job_type, priority, status, result)
         VALUES ($1, 'compress', 3, 'queued', $2)
         RETURNING id`,
        [media.id, { quality }]
      );
      jobs.push(jobResult.rows[0].id);
    }
  }

  res.json({
    success: true,
    message: `${jobs.length} compression jobs queued`,
    data: { jobIds: jobs }
  });
});

// Get storage usage stats
const getStorageUsage = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const usage = await calculateUserStorage(userId);
  const limit = STORAGE_LIMITS[req.user.subscription_type || 'free'];

  // Get usage by month
  const monthlyUsage = await query(
    `SELECT 
      DATE_TRUNC('month', created_at) as month,
      COUNT(*) as uploads,
      SUM(file_size) as size
     FROM media
     WHERE user_id = $1 
       AND is_deleted = false
       AND created_at > NOW() - INTERVAL '12 months'
     GROUP BY month
     ORDER BY month DESC`,
    [userId]
  );

  // Get largest files
  const largestFiles = await query(
    `SELECT id, file_name, file_type, file_size, created_at
     FROM media
     WHERE user_id = $1 AND is_deleted = false
     ORDER BY file_size DESC
     LIMIT 10`,
    [userId]
  );

  res.json({
    success: true,
    data: {
      current: usage,
      limit: limit,
      percentage: limit ? (usage.totalSize / limit * 100).toFixed(2) : 0,
      monthlyUsage: monthlyUsage.rows,
      largestFiles: largestFiles.rows
    }
  });
});

module.exports = {
  uploadMedia,
  getMedia,
  deleteMedia,
  updateMedia,
  moderateMedia,
  getGallery,
  createAlbum,
  getAlbums,
  updateAlbum,
  generateThumbnail,
  compressMedia,
  getStorageUsage
};