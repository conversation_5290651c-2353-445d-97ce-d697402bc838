const { query, withTransaction, geoQuery } = require('../config/database');
const { cache, locationTracking, socketConnections } = require('../config/redis');
const { AppError, asyncHandler } = require('../middleware/errorHandler');
const logger = require('../config/logger');
const { broadcastEmergency, sendEmergencyUpdate } = require('../websocket/emergency');
const { sendPushNotification } = require('../services/pushNotifications');

// Get user's emergency responder settings
const getSettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const result = await query(
    `SELECT is_emergency_responder, emergency_types, max_distance_miles,
            available_hours, phone_number, emergency_contact_name,
            emergency_contact_phone, medical_training, special_skills
     FROM user_emergency_settings
     WHERE user_id = $1`,
    [userId]
  );

  let settings = result.rows[0];
  
  // Create default settings if none exist
  if (!settings) {
    const insertResult = await query(
      `INSERT INTO user_emergency_settings (user_id)
       VALUES ($1)
       RETURNING *`,
      [userId]
    );
    settings = insertResult.rows[0];
  }

  res.json({
    success: true,
    data: settings
  });
});

// Update emergency responder settings
const updateSettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const {
    is_emergency_responder,
    emergency_types,
    max_distance_miles,
    available_hours,
    phone_number,
    emergency_contact_name,
    emergency_contact_phone,
    medical_training,
    special_skills
  } = req.body;

  const result = await query(
    `UPDATE user_emergency_settings
     SET is_emergency_responder = COALESCE($2, is_emergency_responder),
         emergency_types = COALESCE($3, emergency_types),
         max_distance_miles = COALESCE($4, max_distance_miles),
         available_hours = COALESCE($5, available_hours),
         phone_number = COALESCE($6, phone_number),
         emergency_contact_name = COALESCE($7, emergency_contact_name),
         emergency_contact_phone = COALESCE($8, emergency_contact_phone),
         medical_training = COALESCE($9, medical_training),
         special_skills = COALESCE($10, special_skills),
         updated_at = NOW()
     WHERE user_id = $1
     RETURNING *`,
    [userId, is_emergency_responder, emergency_types, max_distance_miles,
     available_hours, phone_number, emergency_contact_name,
     emergency_contact_phone, medical_training, special_skills]
  );

  // Update users table if emergency responder status changed
  if (is_emergency_responder !== undefined) {
    await query(
      'UPDATE users SET is_emergency_responder = $2 WHERE id = $1',
      [userId, is_emergency_responder]
    );
  }

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Preview emergency coverage
const previewEmergency = asyncHandler(async (req, res) => {
  const { latitude, longitude, emergency_type } = req.body;
  const radius = process.env.EMERGENCY_BROADCAST_RADIUS_MILES || 1.0;

  // Find potential responders
  const respondersQuery = `
    SELECT u.id, u.first_name, u.last_name,
           ues.emergency_types, ues.medical_training,
           ST_Distance(u.location::geography, ST_MakePoint($1, $2)::geography) / 1609.34 as distance_miles
    FROM users u
    JOIN user_emergency_settings ues ON u.id = ues.user_id
    WHERE u.is_emergency_responder = true
      AND u.status = 'active'
      AND ST_DWithin(u.location::geography, ST_MakePoint($1, $2)::geography, $3 * 1609.34)
      AND ($4 = ANY(ues.emergency_types) OR 'all' = ANY(ues.emergency_types))
    ORDER BY distance_miles ASC
    LIMIT 50`;

  const responders = await query(respondersQuery, [longitude, latitude, radius, emergency_type]);

  // Get neighborhood coverage stats
  const statsQuery = `
    SELECT COUNT(DISTINCT u.id) as total_neighbors,
           COUNT(DISTINCT CASE WHEN u.is_emergency_responder THEN u.id END) as total_responders
    FROM users u
    WHERE u.status = 'active'
      AND u.neighborhood_id = $1`;

  const stats = await query(statsQuery, [req.user.neighborhood_id]);

  // Check online responders
  const onlineCount = 0;
  for (const responder of responders.rows) {
    const isOnline = await socketConnections.isOnline(responder.id);
    if (isOnline) onlineCount++;
  }

  res.json({
    success: true,
    data: {
      estimated_responders: responders.rows.length,
      nearby_helpers: responders.rows.slice(0, 5).map(r => ({
        distance_miles: r.distance_miles.toFixed(1),
        has_medical_training: r.medical_training?.length > 0
      })),
      coverage: responders.rows.length >= 3 ? 'good' : responders.rows.length >= 1 ? 'fair' : 'limited',
      online_now: onlineCount,
      neighborhood_stats: stats.rows[0]
    }
  });
});

// Create emergency alert
const createEmergency = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { emergency_type, description, location, contact_phone, additional_info } = req.body;

  // Check for recent emergencies from same user (prevent abuse)
  const recentCheck = await query(
    `SELECT COUNT(*) as count
     FROM emergency_alerts
     WHERE requester_id = $1
       AND created_at > NOW() - INTERVAL '1 hour'
       AND status IN ('active', 'responding')`,
    [userId]
  );

  if (recentCheck.rows[0].count >= 2) {
    throw new AppError('Too many recent emergency requests', 429, 'TOO_MANY_EMERGENCIES');
  }

  const emergencyData = await withTransaction(async (client) => {
    // Create emergency alert
    const emergencyResult = await client.query(
      `INSERT INTO emergency_alerts 
       (requester_id, neighborhood_id, emergency_type, description, location, 
        contact_phone, additional_info, status)
       VALUES ($1, $2, $3, $4, ST_MakePoint($5, $6)::geography, $7, $8, 'active')
       RETURNING *`,
      [userId, req.user.neighborhood_id, emergency_type, description,
       location.longitude, location.latitude, contact_phone, additional_info]
    );

    const emergency = emergencyResult.rows[0];

    // Log initial location
    await client.query(
      `INSERT INTO emergency_locations
       (emergency_id, user_id, user_type, location, accuracy)
       VALUES ($1, $2, 'requester', ST_MakePoint($3, $4)::geography, $5)`,
      [emergency.id, userId, location.longitude, location.latitude, location.accuracy || 0]
    );

    // Find and notify responders
    const radius = process.env.EMERGENCY_BROADCAST_RADIUS_MILES || 1.0;
    const respondersQuery = `
      SELECT u.id, u.first_name, u.last_name, u.fcm_token,
             ST_Distance(u.location::geography, ST_MakePoint($1, $2)::geography) / 1609.34 as distance_miles
      FROM users u
      JOIN user_emergency_settings ues ON u.id = ues.user_id
      WHERE u.is_emergency_responder = true
        AND u.status = 'active'
        AND u.id != $3
        AND ST_DWithin(u.location::geography, ST_MakePoint($1, $2)::geography, $4 * 1609.34)
        AND ($5 = ANY(ues.emergency_types) OR 'all' = ANY(ues.emergency_types))
      ORDER BY distance_miles ASC
      LIMIT $6`;

    const responders = await client.query(
      respondersQuery,
      [location.longitude, location.latitude, userId, radius, emergency_type,
       process.env.EMERGENCY_RESPONDER_LIMIT || 50]
    );

    // Create notifications for responders
    const notifications = [];
    for (const responder of responders.rows) {
      const notifResult = await client.query(
        `INSERT INTO notifications
         (user_id, type, title, body, data, priority)
         VALUES ($1, 'emergency_alert', $2, $3, $4, 'critical')
         RETURNING id`,
        [
          responder.id,
          `🚨 ${emergency_type.toUpperCase()} Emergency`,
          `${description} - ${responder.distance_miles.toFixed(1)} miles away`,
          { emergency_id: emergency.id, emergency_type, distance: responder.distance_miles }
        ]
      );
      
      notifications.push({
        user_id: responder.id,
        notification_id: notifResult.rows[0].id,
        fcm_token: responder.fcm_token,
        distance: responder.distance_miles
      });
    }

    return { emergency, responders: responders.rows, notifications };
  });

  // Send push notifications asynchronously
  const { emergency, responders, notifications } = emergencyData;
  
  setImmediate(async () => {
    // Broadcast via WebSocket
    await broadcastEmergency(emergency, responders);

    // Send push notifications
    for (const notif of notifications) {
      if (notif.fcm_token) {
        await sendPushNotification(notif.user_id, {
          title: `🚨 ${emergency_type.toUpperCase()} Emergency`,
          body: description,
          data: {
            type: 'emergency_alert',
            emergency_id: emergency.id,
            click_action: 'EMERGENCY_RESPONSE'
          },
          priority: 'high'
        });
      }
    }
  });

  // Cache emergency data for quick access
  await cache.set(`emergency:${emergency.id}`, emergency, 3600);

  res.status(201).json({
    success: true,
    data: {
      emergency_id: emergency.id,
      status: emergency.status,
      notified_responders: responders.length,
      estimated_response_time: '3-5 minutes'
    }
  });
});

// Get active emergencies
const getActiveEmergencies = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { latitude, longitude } = req.query;

  let emergenciesQuery;
  let params;

  if (latitude && longitude) {
    // Location-based query
    emergenciesQuery = `
      SELECT e.*, u.first_name, u.last_name,
             ST_Distance(e.location::geography, ST_MakePoint($1, $2)::geography) / 1609.34 as distance_miles,
             COUNT(DISTINCT er.id) as responder_count
      FROM emergency_alerts e
      JOIN users u ON e.requester_id = u.id
      LEFT JOIN emergency_responders er ON e.id = er.emergency_id AND er.status != 'cancelled'
      WHERE e.status IN ('active', 'responding')
        AND e.neighborhood_id = $3
        AND ST_DWithin(e.location::geography, ST_MakePoint($1, $2)::geography, $4 * 1609.34)
      GROUP BY e.id, u.first_name, u.last_name
      ORDER BY e.created_at DESC`;
    
    params = [longitude, latitude, req.user.neighborhood_id, 
              process.env.EMERGENCY_BROADCAST_RADIUS_MILES || 1.0];
  } else {
    // Neighborhood-based query
    emergenciesQuery = `
      SELECT e.*, u.first_name, u.last_name,
             COUNT(DISTINCT er.id) as responder_count
      FROM emergency_alerts e
      JOIN users u ON e.requester_id = u.id
      LEFT JOIN emergency_responders er ON e.id = er.emergency_id AND er.status != 'cancelled'
      WHERE e.status IN ('active', 'responding')
        AND e.neighborhood_id = $1
      GROUP BY e.id, u.first_name, u.last_name
      ORDER BY e.created_at DESC`;
    
    params = [req.user.neighborhood_id];
  }

  const emergencies = await query(emergenciesQuery, params);

  res.json({
    success: true,
    data: emergencies.rows
  });
});

// Get emergency details
const getEmergencyDetails = asyncHandler(async (req, res) => {
  const { emergencyId } = req.params;
  const userId = req.user.id;

  // Get emergency details
  const emergencyResult = await query(
    `SELECT e.*, u.first_name, u.last_name, u.profile_photo_url
     FROM emergency_alerts e
     JOIN users u ON e.requester_id = u.id
     WHERE e.id = $1`,
    [emergencyId]
  );

  if (emergencyResult.rows.length === 0) {
    throw new AppError('Emergency not found', 404);
  }

  const emergency = emergencyResult.rows[0];

  // Check if user is involved
  const isRequester = emergency.requester_id === userId;
  const responderCheck = await query(
    'SELECT id, status FROM emergency_responders WHERE emergency_id = $1 AND responder_id = $2',
    [emergencyId, userId]
  );
  const isResponder = responderCheck.rows.length > 0;

  if (!isRequester && !isResponder && req.user.role !== 'admin') {
    throw new AppError('Not authorized to view this emergency', 403);
  }

  // Get responders
  const respondersResult = await query(
    `SELECT er.*, u.first_name, u.last_name, u.profile_photo_url
     FROM emergency_responders er
     JOIN users u ON er.responder_id = u.id
     WHERE er.emergency_id = $1
     ORDER BY er.responded_at ASC`,
    [emergencyId]
  );

  // Get recent messages
  const messagesResult = await query(
    `SELECT em.*, u.first_name, u.last_name
     FROM emergency_messages em
     JOIN users u ON em.sender_id = u.id
     WHERE em.emergency_id = $1
     ORDER BY em.created_at DESC
     LIMIT 10`,
    [emergencyId]
  );

  res.json({
    success: true,
    data: {
      emergency,
      responders: respondersResult.rows,
      recent_messages: messagesResult.rows,
      user_role: isRequester ? 'requester' : 'responder'
    }
  });
});

// Send emergency message
const sendEmergencyMessage = asyncHandler(async (req, res) => {
  const { emergencyId } = req.params;
  const userId = req.user.id;
  const { message, type = 'text', metadata } = req.body;

  // Verify user is involved in emergency
  const authCheck = await query(
    `SELECT 1 FROM emergency_alerts WHERE id = $1 AND requester_id = $2
     UNION
     SELECT 1 FROM emergency_responders WHERE emergency_id = $1 AND responder_id = $2`,
    [emergencyId, userId]
  );

  if (authCheck.rows.length === 0) {
    throw new AppError('Not authorized to send messages in this emergency', 403);
  }

  // Insert message
  const messageResult = await query(
    `INSERT INTO emergency_messages 
     (emergency_id, sender_id, message_type, content, metadata)
     VALUES ($1, $2, $3, $4, $5)
     RETURNING *, 
       (SELECT first_name FROM users WHERE id = $2) as sender_first_name,
       (SELECT last_name FROM users WHERE id = $2) as sender_last_name`,
    [emergencyId, userId, type, message, metadata || {}]
  );

  const newMessage = messageResult.rows[0];

  // Broadcast message via WebSocket
  await sendEmergencyUpdate(emergencyId, 'new_message', newMessage);

  res.status(201).json({
    success: true,
    data: newMessage
  });
});

// Get emergency history
const getEmergencyHistory = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { limit = 20, offset = 0, filter } = req.query;

  let query_str = `
    SELECT e.*, u.first_name, u.last_name,
           COUNT(DISTINCT er.id) as responder_count,
           CASE 
             WHEN e.requester_id = $1 THEN 'requester'
             WHEN EXISTS (SELECT 1 FROM emergency_responders WHERE emergency_id = e.id AND responder_id = $1) THEN 'responder'
             ELSE 'viewer'
           END as user_role
    FROM emergency_alerts e
    JOIN users u ON e.requester_id = u.id
    LEFT JOIN emergency_responders er ON e.id = er.emergency_id AND er.status != 'cancelled'
    WHERE e.neighborhood_id = $2`;

  const params = [userId, req.user.neighborhood_id];
  let paramCount = 2;

  // Apply filters
  if (filter === 'my_requests') {
    query_str += ` AND e.requester_id = $1`;
  } else if (filter === 'my_responses') {
    query_str += ` AND EXISTS (SELECT 1 FROM emergency_responders WHERE emergency_id = e.id AND responder_id = $1)`;
  }

  query_str += `
    GROUP BY e.id, u.first_name, u.last_name
    ORDER BY e.created_at DESC
    LIMIT $${++paramCount} OFFSET $${++paramCount}`;
  
  params.push(limit, offset);

  const result = await query(query_str, params);

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: result.rows.length
    }
  });
});

// Respond to emergency
const respondToEmergency = asyncHandler(async (req, res) => {
  const { emergencyId } = req.params;
  const userId = req.user.id;
  const { estimated_arrival_minutes, initial_message } = req.body;

  // Check if emergency is still active
  const emergencyCheck = await query(
    'SELECT status, requester_id FROM emergency_alerts WHERE id = $1',
    [emergencyId]
  );

  if (emergencyCheck.rows.length === 0) {
    throw new AppError('Emergency not found', 404);
  }

  const emergency = emergencyCheck.rows[0];
  
  if (emergency.status !== 'active') {
    throw new AppError('Emergency is no longer active', 400, 'EMERGENCY_NOT_ACTIVE');
  }

  if (emergency.requester_id === userId) {
    throw new AppError('Cannot respond to your own emergency', 400, 'SELF_RESPONSE');
  }

  // Check if already responding
  const existingResponse = await query(
    'SELECT id FROM emergency_responders WHERE emergency_id = $1 AND responder_id = $2',
    [emergencyId, userId]
  );

  if (existingResponse.rows.length > 0) {
    throw new AppError('Already responding to this emergency', 400, 'ALREADY_RESPONDING');
  }

  const responseData = await withTransaction(async (client) => {
    // Add responder
    const responderResult = await client.query(
      `INSERT INTO emergency_responders 
       (emergency_id, responder_id, status, estimated_arrival_minutes)
       VALUES ($1, $2, 'responding', $3)
       RETURNING *`,
      [emergencyId, userId, estimated_arrival_minutes]
    );

    // Update emergency status if first responder
    const responderCount = await client.query(
      'SELECT COUNT(*) as count FROM emergency_responders WHERE emergency_id = $1',
      [emergencyId]
    );

    if (responderCount.rows[0].count === 1) {
      await client.query(
        `UPDATE emergency_alerts SET status = 'responding', first_response_at = NOW() WHERE id = $1`,
        [emergencyId]
      );
    }

    // Send initial message if provided
    if (initial_message) {
      await client.query(
        `INSERT INTO emergency_messages 
         (emergency_id, sender_id, message_type, content)
         VALUES ($1, $2, 'text', $3)`,
        [emergencyId, userId, initial_message]
      );
    }

    // Create notification for requester
    const { createNotification } = require('./notifications');
    await createNotification(client, emergency.requester_id, {
      type: 'emergency_response',
      title: 'Help is on the way!',
      body: `A neighbor is responding to your emergency${estimated_arrival_minutes ? ` - ETA ${estimated_arrival_minutes} minutes` : ''}`,
      data: { emergency_id: emergencyId, responder_id: userId }
    });

    return responderResult.rows[0];
  });

  // Send WebSocket updates
  await sendEmergencyUpdate(emergencyId, 'new_responder', {
    responder_id: userId,
    estimated_arrival_minutes,
    status: 'responding'
  });

  res.status(201).json({
    success: true,
    data: responseData
  });
});

// Update responder status
const updateResponderStatus = asyncHandler(async (req, res) => {
  const { emergencyId } = req.params;
  const userId = req.user.id;
  const { status, location, notes } = req.body;

  // Verify responder
  const responderCheck = await query(
    'SELECT id, status as current_status FROM emergency_responders WHERE emergency_id = $1 AND responder_id = $2',
    [emergencyId, userId]
  );

  if (responderCheck.rows.length === 0) {
    throw new AppError('Not a responder for this emergency', 403);
  }

  const updates = [];
  const values = [status];
  let paramCount = 1;

  updates.push(`status = $${paramCount}`);

  if (status === 'on_site') {
    updates.push(`arrived_at = NOW()`);
  } else if (status === 'cancelled') {
    updates.push(`cancelled_at = NOW()`);
  }

  if (notes) {
    updates.push(`notes = $${++paramCount}`);
    values.push(notes);
  }

  values.push(emergencyId, userId);

  const result = await query(
    `UPDATE emergency_responders 
     SET ${updates.join(', ')}, updated_at = NOW()
     WHERE emergency_id = $${++paramCount} AND responder_id = $${++paramCount}
     RETURNING *`,
    values
  );

  // Update location if provided
  if (location && location.latitude && location.longitude) {
    await query(
      `INSERT INTO emergency_locations
       (emergency_id, user_id, user_type, location, accuracy)
       VALUES ($1, $2, 'responder', ST_MakePoint($3, $4)::geography, $5)`,
      [emergencyId, userId, location.longitude, location.latitude, location.accuracy || 0]
    );
  }

  // Send WebSocket update
  await sendEmergencyUpdate(emergencyId, 'responder_status_update', {
    responder_id: userId,
    status,
    location
  });

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Update emergency status (requester only)
const updateEmergencyStatus = asyncHandler(async (req, res) => {
  const { emergencyId } = req.params;
  const userId = req.user.id;
  const { status, resolution_notes, responder_feedback } = req.body;

  // Verify ownership
  const emergencyCheck = await query(
    'SELECT requester_id, status as current_status FROM emergency_alerts WHERE id = $1',
    [emergencyId]
  );

  if (emergencyCheck.rows.length === 0) {
    throw new AppError('Emergency not found', 404);
  }

  if (emergencyCheck.rows[0].requester_id !== userId) {
    throw new AppError('Only the requester can update emergency status', 403);
  }

  if (emergencyCheck.rows[0].current_status === 'resolved' || emergencyCheck.rows[0].current_status === 'cancelled') {
    throw new AppError('Emergency already closed', 400, 'EMERGENCY_CLOSED');
  }

  const updateData = await withTransaction(async (client) => {
    // Update emergency
    const result = await client.query(
      `UPDATE emergency_alerts 
       SET status = $2, resolution_notes = $3, resolved_at = NOW(), updated_at = NOW()
       WHERE id = $1
       RETURNING *`,
      [emergencyId, status, resolution_notes]
    );

    // Update all active responders
    await client.query(
      `UPDATE emergency_responders 
       SET status = 'completed', completed_at = NOW()
       WHERE emergency_id = $1 AND status IN ('responding', 'on_site')`,
      [emergencyId]
    );

    // Add responder feedback if provided
    if (responder_feedback && Array.isArray(responder_feedback)) {
      for (const feedback of responder_feedback) {
        await client.query(
          `UPDATE emergency_responders 
           SET rating = $3, feedback = $4
           WHERE emergency_id = $1 AND responder_id = $2`,
          [emergencyId, feedback.responder_id, feedback.rating, feedback.comment]
        );
      }
    }

    return result.rows[0];
  });

  // Send WebSocket update
  await sendEmergencyUpdate(emergencyId, 'emergency_closed', {
    status,
    resolution_notes
  });

  res.json({
    success: true,
    data: updateData
  });
});

// Get emergency messages
const getEmergencyMessages = asyncHandler(async (req, res) => {
  const { emergencyId } = req.params;
  const userId = req.user.id;
  const { limit = 50, offset = 0 } = req.query;

  // Verify access
  const accessCheck = await query(
    `SELECT 1 FROM emergency_alerts WHERE id = $1 AND requester_id = $2
     UNION
     SELECT 1 FROM emergency_responders WHERE emergency_id = $1 AND responder_id = $2`,
    [emergencyId, userId]
  );

  if (accessCheck.rows.length === 0) {
    throw new AppError('Not authorized to view messages', 403);
  }

  const messages = await query(
    `SELECT em.*, u.first_name, u.last_name, u.profile_photo_url,
            CASE WHEN em.sender_id = $2 THEN true ELSE false END as is_mine
     FROM emergency_messages em
     JOIN users u ON em.sender_id = u.id
     WHERE em.emergency_id = $1
     ORDER BY em.created_at DESC
     LIMIT $3 OFFSET $4`,
    [emergencyId, userId, limit, offset]
  );

  res.json({
    success: true,
    data: messages.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Send direct message to specific user
const sendDirectMessage = asyncHandler(async (req, res) => {
  const { emergencyId, userId: targetUserId } = req.params;
  const senderId = req.user.id;
  const { message } = req.body;

  // Verify both users are involved in emergency
  const accessCheck = await query(
    `SELECT 
       (SELECT 1 FROM emergency_alerts WHERE id = $1 AND (requester_id = $2 OR requester_id = $3)) as sender_ok,
       (SELECT 1 FROM emergency_responders WHERE emergency_id = $1 AND (responder_id = $2 OR responder_id = $3)) as responder_ok`,
    [emergencyId, senderId, targetUserId]
  );

  const check = accessCheck.rows[0];
  if (!check.sender_ok && !check.responder_ok) {
    throw new AppError('Users must be involved in the emergency', 403);
  }

  // Insert message with recipient
  const messageResult = await query(
    `INSERT INTO emergency_messages 
     (emergency_id, sender_id, recipient_id, message_type, content, is_direct)
     VALUES ($1, $2, $3, 'text', $4, true)
     RETURNING *, 
       (SELECT first_name FROM users WHERE id = $2) as sender_first_name,
       (SELECT last_name FROM users WHERE id = $2) as sender_last_name`,
    [emergencyId, senderId, targetUserId, message]
  );

  const newMessage = messageResult.rows[0];

  // Send push notification to recipient
  const { createNotification } = require('./notifications');
  await createNotification(null, targetUserId, {
    type: 'emergency_direct_message',
    title: 'Emergency Direct Message',
    body: `${newMessage.sender_first_name}: ${message.substring(0, 50)}${message.length > 50 ? '...' : ''}`,
    data: { emergency_id: emergencyId, message_id: newMessage.id }
  });

  res.status(201).json({
    success: true,
    data: newMessage
  });
});

// Get quick reply templates
const getQuickReplies = asyncHandler(async (req, res) => {
  const { emergencyId } = req.params;
  const userId = req.user.id;

  // Determine user role
  const roleCheck = await query(
    `SELECT 
       CASE 
         WHEN requester_id = $2 THEN 'requester'
         WHEN EXISTS (SELECT 1 FROM emergency_responders WHERE emergency_id = $1 AND responder_id = $2) THEN 'responder'
         ELSE 'none'
       END as role,
       emergency_type
     FROM emergency_alerts
     WHERE id = $1`,
    [emergencyId, userId]
  );

  if (roleCheck.rows.length === 0 || roleCheck.rows[0].role === 'none') {
    throw new AppError('Not authorized', 403);
  }

  const { role, emergency_type } = roleCheck.rows[0];

  // Define quick replies based on role and emergency type
  const quickReplies = {
    responder: {
      medical: [
        'On my way - ETA 5 minutes',
        'I have first aid training',
        'Should I call 911?',
        'Can you describe the symptoms?',
        'Is the person conscious?'
      ],
      fire: [
        'Everyone evacuate immediately!',
        'Fire department has been called',
        'Is anyone trapped inside?',
        'Move to safe distance',
        'Do not use elevators'
      ],
      safety: [
        'Police have been notified',
        'Stay indoors and lock doors',
        'I am nearby and coming to help',
        'Are you in immediate danger?',
        'Can you move to a safe location?'
      ],
      default: [
        'On my way to help',
        'ETA 5 minutes',
        'Stay calm, help is coming',
        'Can you provide more details?',
        'Is anyone injured?'
      ]
    },
    requester: {
      all: [
        'Thank you for responding!',
        'Please hurry',
        'Situation is getting worse',
        'Situation is stable now',
        'No longer need assistance'
      ]
    }
  };

  const replies = role === 'responder' 
    ? (quickReplies.responder[emergency_type] || quickReplies.responder.default)
    : quickReplies.requester.all;

  res.json({
    success: true,
    data: replies.map((text, index) => ({
      id: `quick_${index}`,
      text,
      category: role
    }))
  });
});

// Update requester location
const updateRequesterLocation = asyncHandler(async (req, res) => {
  const { emergencyId } = req.params;
  const userId = req.user.id;
  const { latitude, longitude, accuracy, address } = req.body;

  // Verify ownership
  const emergencyCheck = await query(
    'SELECT requester_id, status FROM emergency_alerts WHERE id = $1',
    [emergencyId]
  );

  if (emergencyCheck.rows.length === 0) {
    throw new AppError('Emergency not found', 404);
  }

  if (emergencyCheck.rows[0].requester_id !== userId) {
    throw new AppError('Only requester can update their location', 403);
  }

  if (emergencyCheck.rows[0].status === 'resolved' || emergencyCheck.rows[0].status === 'cancelled') {
    throw new AppError('Cannot update location for closed emergency', 400);
  }

  // Update location
  await withTransaction(async (client) => {
    // Update emergency location
    await client.query(
      `UPDATE emergency_alerts 
       SET location = ST_MakePoint($2, $3)::geography,
           address = COALESCE($4, address),
           updated_at = NOW()
       WHERE id = $1`,
      [emergencyId, longitude, latitude, address]
    );

    // Log location history
    await client.query(
      `INSERT INTO emergency_locations
       (emergency_id, user_id, user_type, location, accuracy, address)
       VALUES ($1, $2, 'requester', ST_MakePoint($3, $4)::geography, $5, $6)`,
      [emergencyId, userId, longitude, latitude, accuracy || 0, address]
    );
  });

  // Cache updated location
  await locationTracking.updateUserLocation(userId, { latitude, longitude, accuracy });

  // Broadcast location update
  await sendEmergencyUpdate(emergencyId, 'requester_location_update', {
    latitude,
    longitude,
    accuracy,
    address
  });

  res.json({
    success: true,
    message: 'Location updated successfully'
  });
});

// Get responder locations
const getResponderLocations = asyncHandler(async (req, res) => {
  const { emergencyId } = req.params;
  const userId = req.user.id;

  // Verify access (requester or responder)
  const accessCheck = await query(
    `SELECT 
       (SELECT requester_id FROM emergency_alerts WHERE id = $1) as requester_id,
       (SELECT 1 FROM emergency_responders WHERE emergency_id = $1 AND responder_id = $2) as is_responder`,
    [emergencyId, userId]
  );

  if (!accessCheck.rows[0] || (accessCheck.rows[0].requester_id !== userId && !accessCheck.rows[0].is_responder)) {
    throw new AppError('Not authorized to view responder locations', 403);
  }

  // Get latest location for each responder
  const locations = await query(
    `SELECT DISTINCT ON (el.user_id)
       el.user_id, el.location, el.accuracy, el.created_at,
       u.first_name, u.last_name,
       er.status, er.estimated_arrival_minutes, er.arrived_at,
       ST_X(el.location::geometry) as longitude,
       ST_Y(el.location::geometry) as latitude
     FROM emergency_locations el
     JOIN emergency_responders er ON el.emergency_id = er.emergency_id AND el.user_id = er.responder_id
     JOIN users u ON el.user_id = u.id
     WHERE el.emergency_id = $1 AND el.user_type = 'responder'
     ORDER BY el.user_id, el.created_at DESC`,
    [emergencyId]
  );

  res.json({
    success: true,
    data: locations.rows
  });
});

// Update responder location
const updateResponderLocation = asyncHandler(async (req, res) => {
  const { emergencyId } = req.params;
  const userId = req.user.id;
  const { latitude, longitude, accuracy, eta } = req.body;

  // Verify responder
  const responderCheck = await query(
    'SELECT id, status FROM emergency_responders WHERE emergency_id = $1 AND responder_id = $2',
    [emergencyId, userId]
  );

  if (responderCheck.rows.length === 0) {
    throw new AppError('Not a responder for this emergency', 403);
  }

  if (responderCheck.rows[0].status === 'cancelled' || responderCheck.rows[0].status === 'completed') {
    throw new AppError('Cannot update location for inactive response', 400);
  }

  await withTransaction(async (client) => {
    // Log location
    await client.query(
      `INSERT INTO emergency_locations
       (emergency_id, user_id, user_type, location, accuracy)
       VALUES ($1, $2, 'responder', ST_MakePoint($3, $4)::geography, $5)`,
      [emergencyId, userId, longitude, latitude, accuracy || 0]
    );

    // Update ETA if provided
    if (eta !== undefined) {
      await client.query(
        `UPDATE emergency_responders 
         SET estimated_arrival_minutes = $3, updated_at = NOW()
         WHERE emergency_id = $1 AND responder_id = $2`,
        [emergencyId, userId, eta]
      );
    }
  });

  // Cache location
  await locationTracking.updateUserLocation(userId, { latitude, longitude, accuracy });

  // Broadcast update
  await sendEmergencyUpdate(emergencyId, 'responder_location_update', {
    responder_id: userId,
    latitude,
    longitude,
    accuracy,
    eta
  });

  res.json({
    success: true,
    message: 'Location updated successfully'
  });
});

// Get available emergency services
const getEmergencyServices = asyncHandler(async (req, res) => {
  const { latitude, longitude } = req.query;
  
  // Get neighborhood emergency services configuration
  const servicesConfig = await query(
    `SELECT emergency_services_config 
     FROM neighborhoods 
     WHERE id = $1`,
    [req.user.neighborhood_id]
  );

  const config = servicesConfig.rows[0]?.emergency_services_config || {};

  // Base services available to all neighborhoods
  const services = [
    {
      type: 'neighbor_responders',
      name: 'Neighborhood Responders',
      description: 'Trained neighbors ready to help',
      available: true,
      average_response_time: '3-5 minutes'
    },
    {
      type: '911',
      name: 'Emergency Services (911)',
      description: 'Police, Fire, Medical emergency',
      available: true,
      phone: '911',
      average_response_time: '5-10 minutes'
    }
  ];

  // Add configured services
  if (config.local_police) {
    services.push({
      type: 'local_police',
      name: config.local_police.name || 'Local Police',
      phone: config.local_police.phone,
      available: true,
      average_response_time: config.local_police.response_time || '10-15 minutes'
    });
  }

  if (config.local_fire) {
    services.push({
      type: 'local_fire',
      name: config.local_fire.name || 'Local Fire Department',
      phone: config.local_fire.phone,
      available: true,
      average_response_time: config.local_fire.response_time || '5-10 minutes'
    });
  }

  if (config.local_medical) {
    services.push({
      type: 'local_medical',
      name: config.local_medical.name || 'Local Medical Services',
      phone: config.local_medical.phone,
      available: true,
      average_response_time: config.local_medical.response_time || '10-15 minutes'
    });
  }

  res.json({
    success: true,
    data: services
  });
});

// Test emergency activation
const testEmergencyActivation = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  // Create test emergency that auto-expires
  const testEmergency = await withTransaction(async (client) => {
    // Create test emergency
    const result = await client.query(
      `INSERT INTO emergency_alerts 
       (requester_id, neighborhood_id, emergency_type, description, location, 
        status, is_test, auto_expire_at)
       VALUES ($1, $2, 'other', 'TEST EMERGENCY - This is a practice alert', 
               (SELECT location FROM users WHERE id = $1), 'active', true, 
               NOW() + INTERVAL '5 minutes')
       RETURNING *`,
      [userId, req.user.neighborhood_id]
    );

    const emergency = result.rows[0];

    // Create test notification
    await client.query(
      `INSERT INTO notifications
       (user_id, type, title, body, data)
       VALUES ($1, 'emergency_test', 'Test Emergency Alert', 
               'This is what an emergency alert looks like. It will auto-expire in 5 minutes.', $2)`,
      [userId, { emergency_id: emergency.id, is_test: true }]
    );

    return emergency;
  });

  // Schedule auto-cancellation
  setTimeout(async () => {
    await query(
      `UPDATE emergency_alerts 
       SET status = 'cancelled', resolution_notes = 'Test emergency auto-expired'
       WHERE id = $1 AND is_test = true`,
      [testEmergency.id]
    );
  }, 5 * 60 * 1000); // 5 minutes

  res.status(201).json({
    success: true,
    data: {
      emergency_id: testEmergency.id,
      message: 'Test emergency created. It will auto-expire in 5 minutes.',
      instructions: [
        'Check your notifications for the test alert',
        'Try responding to see the responder flow',
        'Practice sending emergency messages',
        'The test will automatically end in 5 minutes'
      ]
    }
  });
});

module.exports = {
  getSettings,
  updateSettings,
  previewEmergency,
  createEmergency,
  getActiveEmergencies,
  getEmergencyDetails,
  sendEmergencyMessage,
  getEmergencyHistory,
  respondToEmergency,
  updateResponderStatus,
  updateEmergencyStatus,
  getEmergencyMessages,
  sendDirectMessage,
  getQuickReplies,
  updateRequesterLocation,
  getResponderLocations,
  updateResponderLocation,
  getEmergencyServices,
  testEmergencyActivation
};