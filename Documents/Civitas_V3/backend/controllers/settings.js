const { query, withTransaction } = require('../config/database');
const { cache } = require('../config/redis');
const { AppError, asyncHandler } = require('../middleware/errorHandler');
const logger = require('../config/logger');
const crypto = require('crypto');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const bcrypt = require('bcrypt');

// Get all user settings
const getAllSettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // Get all settings categories
  const settingsResult = await query(
    'SELECT category, settings FROM user_settings WHERE user_id = $1',
    [userId]
  );

  // Get default templates
  const defaultsResult = await query(
    'SELECT category, settings FROM default_settings_templates WHERE is_active = true'
  );

  const defaults = {};
  defaultsResult.rows.forEach(row => {
    defaults[row.category] = row.settings;
  });

  // Merge user settings with defaults
  const userSettings = {};
  settingsResult.rows.forEach(row => {
    userSettings[row.category] = row.settings;
  });

  const allSettings = {
    notifications: { ...defaults.notifications, ...userSettings.notifications },
    privacy: { ...defaults.privacy, ...userSettings.privacy },
    display: { ...defaults.display, ...userSettings.display },
    accessibility: { ...defaults.accessibility, ...userSettings.accessibility }
  };

  res.json({
    success: true,
    data: allSettings
  });
});

// Get settings for specific category
const getSettingsByCategory = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { category } = req.params;

  const validCategories = ['notifications', 'privacy', 'display', 'accessibility'];
  if (!validCategories.includes(category)) {
    throw new AppError('Invalid settings category', 400);
  }

  // Get user settings
  const settingsResult = await query(
    'SELECT settings FROM user_settings WHERE user_id = $1 AND category = $2',
    [userId, category]
  );

  if (settingsResult.rows.length > 0) {
    res.json({
      success: true,
      data: settingsResult.rows[0].settings
    });
  } else {
    // Return defaults
    const defaultResult = await query(
      'SELECT settings FROM default_settings_templates WHERE category = $1 AND is_active = true',
      [category]
    );

    res.json({
      success: true,
      data: defaultResult.rows[0]?.settings || {}
    });
  }
});

// Update notification settings
const updateNotificationSettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const settings = req.body;

  await _updateSettingsCategory(userId, 'notifications', settings);

  res.json({
    success: true,
    data: settings
  });
});

// Update privacy settings
const updatePrivacySettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const settings = req.body;

  // Update main user table for location sharing
  if (settings.share_location !== undefined) {
    await query(
      'UPDATE users SET location_sharing_enabled = $2 WHERE id = $1',
      [userId, settings.share_location]
    );
  }

  await _updateSettingsCategory(userId, 'privacy', settings);

  res.json({
    success: true,
    data: settings
  });
});

// Update display settings
const updateDisplaySettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const settings = req.body;

  // Update user's language preference
  if (settings.language) {
    await query(
      'UPDATE users SET preferred_language = $2 WHERE id = $1',
      [userId, settings.language]
    );
  }

  await _updateSettingsCategory(userId, 'display', settings);

  res.json({
    success: true,
    data: settings
  });
});

// Update accessibility settings
const updateAccessibilitySettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const settings = req.body;

  await _updateSettingsCategory(userId, 'accessibility', settings);

  res.json({
    success: true,
    data: settings
  });
});

// Reset settings to defaults
const resetSettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { category } = req.params;

  if (category === 'all') {
    // Delete all user settings
    await query('DELETE FROM user_settings WHERE user_id = $1', [userId]);
    
    res.json({
      success: true,
      message: 'All settings reset to defaults'
    });
  } else {
    const validCategories = ['notifications', 'privacy', 'display', 'accessibility'];
    if (!validCategories.includes(category)) {
      throw new AppError('Invalid settings category', 400);
    }

    await query(
      'DELETE FROM user_settings WHERE user_id = $1 AND category = $2',
      [userId, category]
    );

    res.json({
      success: true,
      message: `${category} settings reset to defaults`
    });
  }
});

// Export user settings
const exportSettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const settingsResult = await query(
    'SELECT category, settings, updated_at FROM user_settings WHERE user_id = $1 ORDER BY category',
    [userId]
  );

  const exportData = {
    user_id: userId,
    exported_at: new Date().toISOString(),
    version: '1.0',
    settings: {}
  };

  settingsResult.rows.forEach(row => {
    exportData.settings[row.category] = {
      data: row.settings,
      updated_at: row.updated_at
    };
  });

  res.json({
    success: true,
    data: exportData
  });
});

// Import user settings
const importSettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { settings } = req.body;

  if (!settings || typeof settings !== 'object') {
    throw new AppError('Invalid import format', 400);
  }

  const imported = [];
  const errors = [];
  const validCategories = ['notifications', 'privacy', 'display', 'accessibility'];

  await withTransaction(async (client) => {
    for (const [category, data] of Object.entries(settings)) {
      if (!validCategories.includes(category)) {
        errors.push(`Unknown category: ${category}`);
        continue;
      }

      try {
        const settingsData = data.data || data;
        await _updateSettingsCategory(userId, category, settingsData, client);
        imported.push(category);
      } catch (error) {
        errors.push(`Error importing ${category}: ${error.message}`);
      }
    }
  });

  res.json({
    success: true,
    message: 'Settings import completed',
    imported,
    errors
  });
});

// Toggle notifications
const toggleNotifications = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { enabled } = req.body;

  // Get current settings
  const currentResult = await query(
    'SELECT settings FROM user_settings WHERE user_id = $1 AND category = $2',
    [userId, 'notifications']
  );

  const currentSettings = currentResult.rows[0]?.settings || {};
  const updatedSettings = {
    ...currentSettings,
    push_enabled: enabled,
    email_enabled: enabled
  };

  await _updateSettingsCategory(userId, 'notifications', updatedSettings);

  // Track toggle
  await query(
    `INSERT INTO settings_toggle_history (user_id, toggle_type, old_value, new_value)
     VALUES ($1, 'notifications', $2, $3)`,
    [userId, { push_enabled: currentSettings.push_enabled, email_enabled: currentSettings.email_enabled },
     { push_enabled: enabled, email_enabled: enabled }]
  );

  res.json({
    success: true,
    message: `Notifications ${enabled ? 'enabled' : 'disabled'}`
  });
});

// Toggle dark mode
const toggleDarkMode = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  // Get current theme
  const currentResult = await query(
    'SELECT settings FROM user_settings WHERE user_id = $1 AND category = $2',
    [userId, 'display']
  );

  const currentSettings = currentResult.rows[0]?.settings || {};
  const currentTheme = currentSettings.theme || 'light';
  const newTheme = currentTheme === 'light' ? 'dark' : 'light';

  const updatedSettings = {
    ...currentSettings,
    theme: newTheme
  };

  await _updateSettingsCategory(userId, 'display', updatedSettings);

  // Track toggle
  await query(
    `INSERT INTO settings_toggle_history (user_id, toggle_type, old_value, new_value)
     VALUES ($1, 'dark_mode', $2, $3)`,
    [userId, { theme: currentTheme }, { theme: newTheme }]
  );

  res.json({
    success: true,
    theme: newTheme
  });
});

// Toggle location sharing
const toggleLocationSharing = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { enabled } = req.body;

  // Update privacy settings
  const currentResult = await query(
    'SELECT settings FROM user_settings WHERE user_id = $1 AND category = $2',
    [userId, 'privacy']
  );

  const currentSettings = currentResult.rows[0]?.settings || {};
  const updatedSettings = {
    ...currentSettings,
    share_location: enabled
  };

  await withTransaction(async (client) => {
    // Update settings
    await _updateSettingsCategory(userId, 'privacy', updatedSettings, client);

    // Update user table
    await client.query(
      'UPDATE users SET location_sharing_enabled = $2 WHERE id = $1',
      [userId, enabled]
    );
  });

  // Track toggle
  await query(
    `INSERT INTO settings_toggle_history (user_id, toggle_type, old_value, new_value)
     VALUES ($1, 'location_sharing', $2, $3)`,
    [userId, { share_location: currentSettings.share_location }, { share_location: enabled }]
  );

  res.json({
    success: true,
    message: `Location sharing ${enabled ? 'enabled' : 'disabled'}`
  });
});

// Get active sessions
const getSessions = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const sessionsResult = await query(
    `SELECT id, device_type, device_name, ip_address, location_city, 
            location_country, last_active, created_at,
            CASE WHEN token_hash = $2 THEN true ELSE false END as is_current
     FROM user_sessions
     WHERE user_id = $1 AND is_active = true AND expires_at > NOW()
     ORDER BY last_active DESC`,
    [userId, crypto.createHash('sha256').update(req.headers.authorization?.split(' ')[1] || '').digest('hex')]
  );

  res.json({
    success: true,
    data: sessionsResult.rows
  });
});

// Revoke session
const revokeSession = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { sessionId } = req.params;

  const result = await query(
    'UPDATE user_sessions SET is_active = false WHERE id = $1 AND user_id = $2 RETURNING id',
    [sessionId, userId]
  );

  if (result.rows.length === 0) {
    throw new AppError('Session not found', 404);
  }

  res.json({
    success: true,
    message: 'Session revoked successfully'
  });
});

// Enable 2FA
const enable2FA = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { password } = req.body;

  // Verify password
  const userResult = await query(
    'SELECT password_hash FROM users WHERE id = $1',
    [userId]
  );

  const isPasswordValid = await bcrypt.compare(password, userResult.rows[0].password_hash);
  if (!isPasswordValid) {
    throw new AppError('Invalid password', 401);
  }

  // Check if already enabled
  const existingResult = await query(
    'SELECT is_enabled FROM user_2fa_settings WHERE user_id = $1',
    [userId]
  );

  if (existingResult.rows[0]?.is_enabled) {
    throw new AppError('2FA is already enabled', 400);
  }

  // Generate secret
  const secret = speakeasy.generateSecret({
    name: `Civitas (${req.user.email})`,
    issuer: 'Civitas'
  });

  // Generate backup codes
  const backupCodes = Array.from({ length: 10 }, () => 
    crypto.randomBytes(4).toString('hex').toUpperCase()
  );

  // Store settings
  await query(
    `INSERT INTO user_2fa_settings (user_id, secret, backup_codes)
     VALUES ($1, $2, $3)
     ON CONFLICT (user_id) DO UPDATE
     SET secret = $2, backup_codes = $3, is_enabled = false`,
    [userId, secret.base32, backupCodes]
  );

  // Generate QR code
  const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

  res.json({
    success: true,
    data: {
      secret: secret.base32,
      qr_code: qrCodeUrl,
      backup_codes: backupCodes
    }
  });
});

// Disable 2FA
const disable2FA = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { password, code } = req.body;

  // Verify password
  const userResult = await query(
    'SELECT password_hash FROM users WHERE id = $1',
    [userId]
  );

  const isPasswordValid = await bcrypt.compare(password, userResult.rows[0].password_hash);
  if (!isPasswordValid) {
    throw new AppError('Invalid password', 401);
  }

  // Verify 2FA code
  const settingsResult = await query(
    'SELECT secret, is_enabled FROM user_2fa_settings WHERE user_id = $1',
    [userId]
  );

  if (!settingsResult.rows[0]?.is_enabled) {
    throw new AppError('2FA is not enabled', 400);
  }

  const verified = speakeasy.totp.verify({
    secret: settingsResult.rows[0].secret,
    encoding: 'base32',
    token: code,
    window: 2
  });

  if (!verified) {
    throw new AppError('Invalid 2FA code', 401);
  }

  // Disable 2FA
  await query(
    'UPDATE user_2fa_settings SET is_enabled = false WHERE user_id = $1',
    [userId]
  );

  res.json({
    success: true,
    message: '2FA disabled successfully'
  });
});

// Request data export
const requestDataExport = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { exportType = 'full' } = req.body;

  // Check for pending exports
  const pendingResult = await query(
    `SELECT id FROM data_export_requests 
     WHERE user_id = $1 AND status IN ('pending', 'processing')`,
    [userId]
  );

  if (pendingResult.rows.length > 0) {
    throw new AppError('You already have a pending export request', 400);
  }

  // Create export request
  const requestResult = await query(
    `INSERT INTO data_export_requests (user_id, export_type)
     VALUES ($1, $2)
     RETURNING id`,
    [userId, exportType]
  );

  // Queue export job (would be handled by background worker)
  // For now, just mark it as processing
  await query(
    'UPDATE data_export_requests SET status = $2 WHERE id = $1',
    [requestResult.rows[0].id, 'processing']
  );

  res.json({
    success: true,
    data: {
      request_id: requestResult.rows[0].id,
      message: 'Your data export request has been submitted. You will be notified when it is ready.'
    }
  });
});

// Helper function to update settings category
async function _updateSettingsCategory(userId, category, settings, client = null) {
  const queryFn = client || query;
  
  await queryFn(
    `INSERT INTO user_settings (user_id, category, settings)
     VALUES ($1, $2, $3)
     ON CONFLICT (user_id, category)
     DO UPDATE SET settings = $3, updated_at = NOW()`,
    [userId, category, settings]
  );

  // Clear cache
  await cache.del(`settings:${userId}:${category}`);
  await cache.del(`settings:${userId}:all`);
}

module.exports = {
  getAllSettings,
  getSettingsByCategory,
  updateNotificationSettings,
  updatePrivacySettings,
  updateDisplaySettings,
  updateAccessibilitySettings,
  resetSettings,
  exportSettings,
  importSettings,
  toggleNotifications,
  toggleDarkMode,
  toggleLocationSharing,
  getSessions,
  revokeSession,
  enable2FA,
  disable2FA,
  requestDataExport
};