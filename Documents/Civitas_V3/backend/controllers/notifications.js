const { query, pool } = require('../config/database');
const { cache } = require('../config/redis');
const asyncHandler = require('../middleware/asyncHandler');
const AppError = require('../utils/AppError');
const logger = require('../config/logger');
const { sendNotification, sendBulkNotifications } = require('../services/pushNotifications');

// Notification types
const NOTIFICATION_TYPES = {
  POST: 'post',
  COMMENT: 'comment',
  MESSAGE: 'message',
  EMERGENCY: 'emergency',
  EVENT: 'event',
  HELP_REQUEST: 'help_request',
  HELP_OFFER: 'help_offer',
  KUDOS: 'kudos',
  SYSTEM: 'system',
  DAILY_KINDNESS: 'daily_kindness'
};

// Daily kindness suggestions
const KINDNESS_SUGGESTIONS = [
  { category: 'greeting', points: 10, suggestion: 'Say hello to 3 neighbors today' },
  { category: 'help', points: 20, suggestion: 'Offer to help carry groceries for an elderly neighbor' },
  { category: 'sharing', points: 15, suggestion: 'Share a homemade treat with a neighbor' },
  { category: 'community', points: 25, suggestion: 'Organize a small gathering for your street' },
  { category: 'environment', points: 15, suggestion: 'Pick up litter in your neighborhood' },
  { category: 'compliment', points: 10, suggestion: 'Give genuine compliments to 5 neighbors' },
  { category: 'help', points: 30, suggestion: 'Volunteer to walk a neighbor\'s dog' },
  { category: 'sharing', points: 20, suggestion: 'Leave a positive note for a neighbor' }
];

// Get user notifications
const getNotifications = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { page = 1, limit = 20, type, unread_only } = req.query;
  const offset = (page - 1) * limit;

  let notificationQuery = `
    SELECT 
      n.*,
      CASE 
        WHEN n.type = 'message' THEN (
          SELECT CONCAT(u.first_name, ' ', u.last_name)
          FROM users u
          WHERE u.id = (n.data->>'sender_id')::uuid
        )
        WHEN n.type IN ('post', 'comment', 'help_offer') THEN (
          SELECT CONCAT(u.first_name, ' ', u.last_name)
          FROM users u
          WHERE u.id = (n.data->>'user_id')::uuid
        )
        ELSE NULL
      END as actor_name,
      CASE 
        WHEN n.type = 'message' THEN (
          SELECT u.profile_photo_url
          FROM users u
          WHERE u.id = (n.data->>'sender_id')::uuid
        )
        WHEN n.type IN ('post', 'comment', 'help_offer') THEN (
          SELECT u.profile_photo_url
          FROM users u
          WHERE u.id = (n.data->>'user_id')::uuid
        )
        ELSE NULL
      END as actor_avatar
    FROM notifications n
    WHERE n.user_id = $1
      AND (n.expires_at IS NULL OR n.expires_at > NOW())
  `;

  const params = [userId];
  let paramCount = 2;

  if (type) {
    notificationQuery += ` AND n.type = $${paramCount++}`;
    params.push(type);
  }

  if (unread_only === 'true') {
    notificationQuery += ` AND n.is_read = false`;
  }

  notificationQuery += ` ORDER BY n.created_at DESC LIMIT $${paramCount++} OFFSET $${paramCount}`;
  params.push(limit, offset);

  const result = await query(notificationQuery, params);

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      hasMore: result.rows.length === parseInt(limit)
    }
  });
});

// Get unread notification count
const getUnreadCount = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const result = await query(
    `SELECT 
      COUNT(*) as total,
      COUNT(*) FILTER (WHERE type = $2) as messages,
      COUNT(*) FILTER (WHERE type = $3) as posts,
      COUNT(*) FILTER (WHERE type = $4) as emergency,
      COUNT(*) FILTER (WHERE type = $5) as events,
      COUNT(*) FILTER (WHERE type = $6) as help_requests
     FROM notifications
     WHERE user_id = $1
       AND is_read = false
       AND (expires_at IS NULL OR expires_at > NOW())`,
    [
      userId,
      NOTIFICATION_TYPES.MESSAGE,
      NOTIFICATION_TYPES.POST,
      NOTIFICATION_TYPES.EMERGENCY,
      NOTIFICATION_TYPES.EVENT,
      NOTIFICATION_TYPES.HELP_REQUEST
    ]
  );

  res.json({
    success: true,
    data: {
      count: parseInt(result.rows[0].total),
      by_type: {
        messages: parseInt(result.rows[0].messages),
        posts: parseInt(result.rows[0].posts),
        emergency: parseInt(result.rows[0].emergency),
        events: parseInt(result.rows[0].events),
        help_requests: parseInt(result.rows[0].help_requests)
      }
    }
  });
});

// Get notification preferences
const getPreferences = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  let preferences = await query(
    'SELECT * FROM notification_preferences WHERE user_id = $1',
    [userId]
  );

  // Create default preferences if none exist
  if (preferences.rows.length === 0) {
    const defaultCategories = {
      posts: true,
      comments: true,
      messages: true,
      events: true,
      emergency: true,
      help_requests: true,
      kudos: true,
      system: true,
      daily_kindness: true
    };

    preferences = await query(
      `INSERT INTO notification_preferences 
       (user_id, categories)
       VALUES ($1, $2)
       RETURNING *`,
      [userId, JSON.stringify(defaultCategories)]
    );
  }

  res.json({
    success: true,
    data: preferences.rows[0]
  });
});

// Update notification preferences
const updatePreferences = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { push_enabled, email_enabled, sms_enabled, quiet_hours_start, quiet_hours_end } = req.body;

  const updateFields = [];
  const values = [];
  let paramCount = 1;

  if (push_enabled !== undefined) {
    updateFields.push(`push_enabled = $${paramCount++}`);
    values.push(push_enabled);
  }
  if (email_enabled !== undefined) {
    updateFields.push(`email_enabled = $${paramCount++}`);
    values.push(email_enabled);
  }
  if (sms_enabled !== undefined) {
    updateFields.push(`sms_enabled = $${paramCount++}`);
    values.push(sms_enabled);
  }
  if (quiet_hours_start !== undefined) {
    updateFields.push(`quiet_hours_start = $${paramCount++}`);
    values.push(quiet_hours_start);
  }
  if (quiet_hours_end !== undefined) {
    updateFields.push(`quiet_hours_end = $${paramCount++}`);
    values.push(quiet_hours_end);
  }

  if (updateFields.length === 0) {
    throw new AppError('No fields to update', 400);
  }

  updateFields.push('updated_at = NOW()');
  values.push(userId);

  const result = await query(
    `UPDATE notification_preferences 
     SET ${updateFields.join(', ')}
     WHERE user_id = $${paramCount}
     RETURNING *`,
    values
  );

  // If no rows updated, create new preferences
  if (result.rows.length === 0) {
    const insertResult = await query(
      `INSERT INTO notification_preferences 
       (user_id, push_enabled, email_enabled, sms_enabled, quiet_hours_start, quiet_hours_end)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING *`,
      [userId, push_enabled ?? true, email_enabled ?? true, sms_enabled ?? false, quiet_hours_start, quiet_hours_end]
    );
    return res.json({
      success: true,
      data: insertResult.rows[0]
    });
  }

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Update category preferences
const updateCategoryPreferences = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { categories } = req.body;

  // Ensure preferences exist
  await query(
    `INSERT INTO notification_preferences (user_id, categories)
     VALUES ($1, $2)
     ON CONFLICT (user_id) DO UPDATE
     SET categories = $2, updated_at = NOW()`,
    [userId, JSON.stringify(categories)]
  );

  const result = await query(
    'SELECT * FROM notification_preferences WHERE user_id = $1',
    [userId]
  );

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Set quiet hours
const setQuietHours = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { quiet_hours_start, quiet_hours_end, enabled = true } = req.body;

  if (enabled && (!quiet_hours_start || !quiet_hours_end)) {
    throw new AppError('Start and end times required when enabling quiet hours', 400);
  }

  const values = enabled 
    ? [userId, quiet_hours_start, quiet_hours_end]
    : [userId, null, null];

  const result = await query(
    `INSERT INTO notification_preferences (user_id, quiet_hours_start, quiet_hours_end)
     VALUES ($1, $2, $3)
     ON CONFLICT (user_id) DO UPDATE
     SET quiet_hours_start = $2, quiet_hours_end = $3, updated_at = NOW()
     RETURNING *`,
    values
  );

  res.json({
    success: true,
    data: {
      quiet_hours_enabled: enabled,
      quiet_hours_start: result.rows[0].quiet_hours_start,
      quiet_hours_end: result.rows[0].quiet_hours_end
    }
  });
});

// Register device for push notifications
const registerDevice = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { device_token, device_type, device_name, app_version } = req.body;

  // Deactivate other tokens for this device type
  await query(
    `UPDATE push_subscriptions 
     SET is_active = false 
     WHERE user_id = $1 AND device_type = $2`,
    [userId, device_type]
  );

  // Insert or update device token
  const result = await query(
    `INSERT INTO push_subscriptions 
     (user_id, device_token, device_type, device_name, app_version, last_used_at)
     VALUES ($1, $2, $3, $4, $5, NOW())
     ON CONFLICT (user_id, device_token) DO UPDATE
     SET device_type = $3, 
         device_name = $4, 
         app_version = $5,
         is_active = true,
         last_used_at = NOW()
     RETURNING *`,
    [userId, device_token, device_type, device_name, app_version]
  );

  res.status(201).json({
    success: true,
    data: result.rows[0]
  });
});

// Get user's registered devices
const getDevices = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const result = await query(
    `SELECT 
      id,
      device_token,
      device_type,
      device_name,
      app_version,
      is_active,
      last_used_at,
      created_at
     FROM push_subscriptions
     WHERE user_id = $1
     ORDER BY last_used_at DESC`,
    [userId]
  );

  res.json({
    success: true,
    data: result.rows
  });
});

// Unregister device
const unregisterDevice = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { deviceToken } = req.params;

  const result = await query(
    `DELETE FROM push_subscriptions 
     WHERE user_id = $1 AND device_token = $2
     RETURNING *`,
    [userId, deviceToken]
  );

  if (result.rows.length === 0) {
    throw new AppError('Device not found', 404);
  }

  res.json({
    success: true,
    message: 'Device unregistered successfully'
  });
});

// Send test notification
const sendTestNotification = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { device_token } = req.body;

  // Create test notification in database
  const notificationResult = await query(
    `INSERT INTO notifications 
     (user_id, type, title, body, data)
     VALUES ($1, $2, $3, $4, $5)
     RETURNING *`,
    [
      userId,
      NOTIFICATION_TYPES.SYSTEM,
      'Test Notification',
      'This is a test notification from Civitas',
      JSON.stringify({ test: true, timestamp: new Date().toISOString() })
    ]
  );

  // Send push notification
  if (device_token) {
    // Send to specific device
    await sendNotification(userId, {
      title: 'Test Notification',
      body: 'This is a test notification from Civitas',
      data: { notification_id: notificationResult.rows[0].id }
    });
  } else {
    // Send to all user's devices
    const devices = await query(
      'SELECT device_token FROM push_subscriptions WHERE user_id = $1 AND is_active = true',
      [userId]
    );

    if (devices.rows.length > 0) {
      await sendBulkNotifications(
        [userId],
        {
          title: 'Test Notification',
          body: 'This is a test notification from Civitas',
          data: { notification_id: notificationResult.rows[0].id }
        }
      );
    }
  }

  res.json({
    success: true,
    message: 'Test notification sent',
    notification_id: notificationResult.rows[0].id
  });
});

// Get notification details
const getNotification = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { notificationId } = req.params;

  const result = await query(
    `SELECT * FROM notifications 
     WHERE id = $1 AND user_id = $2`,
    [notificationId, userId]
  );

  if (result.rows.length === 0) {
    throw new AppError('Notification not found', 404);
  }

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Mark notification as read
const markAsRead = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { notificationId } = req.params;

  const result = await query(
    `UPDATE notifications 
     SET is_read = true, read_at = NOW()
     WHERE id = $1 AND user_id = $2 AND is_read = false
     RETURNING *`,
    [notificationId, userId]
  );

  if (result.rows.length === 0) {
    throw new AppError('Notification not found or already read', 404);
  }

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Mark all notifications as read
const markAllAsRead = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const result = await query(
    `UPDATE notifications 
     SET is_read = true, read_at = NOW()
     WHERE user_id = $1 AND is_read = false
     RETURNING id`,
    [userId]
  );

  res.json({
    success: true,
    message: `Marked ${result.rows.length} notifications as read`
  });
});

// Delete notification
const deleteNotification = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { notificationId } = req.params;

  const result = await query(
    `DELETE FROM notifications 
     WHERE id = $1 AND user_id = $2
     RETURNING *`,
    [notificationId, userId]
  );

  if (result.rows.length === 0) {
    throw new AppError('Notification not found', 404);
  }

  res.json({
    success: true,
    message: 'Notification deleted'
  });
});

// Bulk delete notifications
const bulkDelete = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { notification_ids } = req.body;

  if (!notification_ids || notification_ids.length === 0) {
    throw new AppError('No notification IDs provided', 400);
  }

  const result = await query(
    `DELETE FROM notifications 
     WHERE user_id = $1 AND id = ANY($2::uuid[])
     RETURNING id`,
    [userId, notification_ids]
  );

  res.json({
    success: true,
    message: `Deleted ${result.rows.length} notifications`
  });
});

// Get daily kindness suggestion
const getDailyKindness = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const today = new Date().toISOString().split('T')[0];

  // Check if already completed today
  const completedCheck = await query(
    `SELECT * FROM daily_kindness_tracking 
     WHERE user_id = $1 AND action_date = $2`,
    [userId, today]
  );

  // Get today's suggestion (deterministic based on date and user)
  const dayOfYear = Math.floor((new Date() - new Date(new Date().getFullYear(), 0, 0)) / 86400000);
  const userHash = userId.charCodeAt(0) + userId.charCodeAt(1);
  const suggestionIndex = (dayOfYear + userHash) % KINDNESS_SUGGESTIONS.length;
  const suggestion = KINDNESS_SUGGESTIONS[suggestionIndex];

  res.json({
    success: true,
    data: {
      date: today,
      suggestion: suggestion.suggestion,
      category: suggestion.category,
      points: suggestion.points,
      completed: completedCheck.rows.length > 0,
      completion_details: completedCheck.rows[0] || null
    }
  });
});

// Complete daily kindness
const completeDailyKindness = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { notes } = req.body;
  const today = new Date().toISOString().split('T')[0];

  // Get today's suggestion
  const dayOfYear = Math.floor((new Date() - new Date(new Date().getFullYear(), 0, 0)) / 86400000);
  const userHash = userId.charCodeAt(0) + userId.charCodeAt(1);
  const suggestionIndex = (dayOfYear + userHash) % KINDNESS_SUGGESTIONS.length;
  const suggestion = KINDNESS_SUGGESTIONS[suggestionIndex];

  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Record completion
    const trackingResult = await client.query(
      `INSERT INTO daily_kindness_tracking 
       (user_id, action_date, action_type, points_earned, details)
       VALUES ($1, $2, $3, $4, $5)
       ON CONFLICT (user_id, action_date, action_type) DO UPDATE
       SET points_earned = $4, details = $5
       RETURNING *`,
      [
        userId,
        today,
        suggestion.category,
        suggestion.points,
        JSON.stringify({ notes, suggestion: suggestion.suggestion })
      ]
    );

    // Update user points
    await client.query(
      `UPDATE users 
       SET kindness_points = COALESCE(kindness_points, 0) + $2
       WHERE id = $1`,
      [userId, suggestion.points]
    );

    // Create notification
    await client.query(
      `INSERT INTO notifications 
       (user_id, type, title, body, data)
       VALUES ($1, $2, $3, $4, $5)`,
      [
        userId,
        NOTIFICATION_TYPES.DAILY_KINDNESS,
        'Daily Kindness Completed!',
        `You earned ${suggestion.points} kindness points`,
        JSON.stringify({ 
          points: suggestion.points,
          category: suggestion.category,
          date: today
        })
      ]
    );

    await client.query('COMMIT');

    res.json({
      success: true,
      data: {
        tracking: trackingResult.rows[0],
        points_earned: suggestion.points,
        message: 'Thank you for spreading kindness in your neighborhood!'
      }
    });
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
});

// Helper function to create notifications (used by other controllers)
const createNotification = async (userId, type, title, body, data = {}, action_url = null) => {
  try {
    const result = await query(
      `INSERT INTO notifications 
       (user_id, type, title, body, data, action_url)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING *`,
      [userId, type, title, body, JSON.stringify(data), action_url]
    );

    // Check user preferences
    const prefs = await query(
      'SELECT * FROM notification_preferences WHERE user_id = $1',
      [userId]
    );

    if (prefs.rows.length > 0) {
      const preferences = prefs.rows[0];
      const categories = preferences.categories || {};
      
      // Check if this notification type is enabled
      const categoryMap = {
        [NOTIFICATION_TYPES.POST]: 'posts',
        [NOTIFICATION_TYPES.COMMENT]: 'comments',
        [NOTIFICATION_TYPES.MESSAGE]: 'messages',
        [NOTIFICATION_TYPES.EMERGENCY]: 'emergency',
        [NOTIFICATION_TYPES.EVENT]: 'events',
        [NOTIFICATION_TYPES.HELP_REQUEST]: 'help_requests',
        [NOTIFICATION_TYPES.KUDOS]: 'kudos',
        [NOTIFICATION_TYPES.SYSTEM]: 'system'
      };

      const category = categoryMap[type];
      if (category && categories[category] === false) {
        return result.rows[0]; // Don't send push notification
      }

      // Check quiet hours
      if (preferences.quiet_hours_start && preferences.quiet_hours_end && preferences.push_enabled) {
        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();
        const [startHour, startMin] = preferences.quiet_hours_start.split(':').map(Number);
        const [endHour, endMin] = preferences.quiet_hours_end.split(':').map(Number);
        const quietStart = startHour * 60 + startMin;
        const quietEnd = endHour * 60 + endMin;

        const inQuietHours = quietEnd > quietStart
          ? currentTime >= quietStart && currentTime < quietEnd
          : currentTime >= quietStart || currentTime < quietEnd;

        if (inQuietHours) {
          return result.rows[0]; // Don't send push during quiet hours
        }
      }

      // Send push notification if enabled
      if (preferences.push_enabled) {
        await sendNotification(userId, {
          title,
          body,
          data: { notification_id: result.rows[0].id, ...data }
        });
      }
    }

    return result.rows[0];
  } catch (error) {
    logger.error('Error creating notification:', error);
    throw error;
  }
};

module.exports = {
  getNotifications,
  getUnreadCount,
  getPreferences,
  updatePreferences,
  updateCategoryPreferences,
  setQuietHours,
  registerDevice,
  getDevices,
  unregisterDevice,
  sendTestNotification,
  getNotification,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  bulkDelete,
  getDailyKindness,
  completeDailyKindness,
  createNotification,
  NOTIFICATION_TYPES
};