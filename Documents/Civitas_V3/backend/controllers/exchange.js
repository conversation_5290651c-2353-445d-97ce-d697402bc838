const { query, withTransaction } = require('../config/database');
const { cache } = require('../config/redis');
const { AppError, asyncHandler } = require('../middleware/errorHandler');
const logger = require('../config/logger');
const { sendRealtimeNotification } = require('../websocket/notifications');
const { broadcastMessage } = require('../websocket/messages');

// Categories for exchange items
const EXCHANGE_CATEGORIES = [
  'electronics', 'furniture', 'books', 'clothing', 'toys', 'sports',
  'tools', 'garden', 'kitchen', 'decor', 'baby', 'other'
];

// Create new exchange listing
const createListing = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const {
    title,
    description,
    category,
    condition,
    exchange_type,
    location,
    address,
    tags,
    images,
    pickup_availability,
    duration_days
  } = req.body;

  // Validate exchange type
  if (!['give_away', 'lend', 'borrow_request'].includes(exchange_type)) {
    throw new AppError('Invalid exchange type', 400);
  }

  // Validate condition
  if (!['new', 'like_new', 'good', 'fair', 'poor'].includes(condition)) {
    throw new AppError('Invalid condition', 400);
  }

  // Check user's active listings limit (prevent spam)
  const activeCount = await query(
    `SELECT COUNT(*) as count 
     FROM exchange_listings 
     WHERE user_id = $1 AND status = 'available' AND is_active = true`,
    [userId]
  );

  if (activeCount.rows[0].count >= 20) {
    throw new AppError('Maximum active listings limit reached (20)', 400);
  }

  const listingData = await withTransaction(async (client) => {
    // Set expiration date (30 days default)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    // Create listing
    const listingResult = await client.query(
      `INSERT INTO exchange_listings 
       (user_id, neighborhood_id, title, description, category, condition,
        exchange_type, location, address, tags, images, pickup_availability,
        duration_days, expires_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, 
               ${location ? 'ST_MakePoint($8, $9)::geography' : 'NULL'}, 
               $10, $11, $12, $13, $14, $15)
       RETURNING *`,
      [userId, req.user.neighborhood_id, title, description, category, condition,
       exchange_type, location?.longitude, location?.latitude, address,
       tags || [], images || [], pickup_availability, duration_days, expiresAt]
    );

    const listing = listingResult.rows[0];

    // Create activity feed entry
    await client.query(
      `INSERT INTO user_activities 
       (user_id, activity_type, activity_data, neighborhood_id)
       VALUES ($1, 'exchange_listing_created', $2, $3)`,
      [userId, { listing_id: listing.id, title, exchange_type }, req.user.neighborhood_id]
    );

    return listing;
  });

  // Notify neighborhood about new listing
  setImmediate(async () => {
    const { sendNeighborhoodNotification } = require('../websocket/notifications');
    await sendNeighborhoodNotification(req.user.neighborhood_id, {
      type: 'new_exchange_listing',
      title: 'New Item Available',
      body: `${req.user.first_name} posted: ${title}`,
      data: { listing_id: listingData.id, exchange_type }
    });
  });

  res.status(201).json({
    success: true,
    data: listingData
  });
});

// Browse exchange listings
const getListings = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const {
    category,
    exchange_type,
    condition,
    search,
    sort = 'newest',
    limit = 20,
    offset = 0
  } = req.query;

  let queryStr = `
    SELECT el.*, 
           u.first_name, u.last_name, u.profile_photo_url,
           EXISTS(
             SELECT 1 FROM exchange_saved_listings 
             WHERE listing_id = el.id AND user_id = $1
           ) as is_saved,
           EXISTS(
             SELECT 1 FROM exchange_requests 
             WHERE listing_id = el.id AND requester_id = $1
           ) as has_requested
    FROM exchange_listings el
    JOIN users u ON el.user_id = u.id
    WHERE el.neighborhood_id = $2
      AND el.status = 'available'
      AND el.is_active = true
      AND el.expires_at > NOW()
      AND el.user_id != $1`;

  const params = [userId, req.user.neighborhood_id];
  let paramCount = 2;

  // Apply filters
  if (category) {
    queryStr += ` AND el.category = $${++paramCount}`;
    params.push(category);
  }

  if (exchange_type) {
    queryStr += ` AND el.exchange_type = $${++paramCount}`;
    params.push(exchange_type);
  }

  if (condition) {
    queryStr += ` AND el.condition = $${++paramCount}`;
    params.push(condition);
  }

  if (search) {
    queryStr += ` AND (el.title ILIKE $${++paramCount} OR el.description ILIKE $${paramCount})`;
    params.push(`%${search}%`);
  }

  // Apply sorting
  switch (sort) {
    case 'newest':
      queryStr += ` ORDER BY el.created_at DESC`;
      break;
    case 'oldest':
      queryStr += ` ORDER BY el.created_at ASC`;
      break;
    case 'most_viewed':
      queryStr += ` ORDER BY el.views_count DESC`;
      break;
    default:
      queryStr += ` ORDER BY el.created_at DESC`;
  }

  queryStr += ` LIMIT $${++paramCount} OFFSET $${++paramCount}`;
  params.push(limit, offset);

  const result = await query(queryStr, params);

  // Get total count for pagination
  const countQuery = `
    SELECT COUNT(*) as total
    FROM exchange_listings el
    WHERE el.neighborhood_id = $1
      AND el.status = 'available'
      AND el.is_active = true
      AND el.expires_at > NOW()
      AND el.user_id != $2`;
  
  const countResult = await query(countQuery, [req.user.neighborhood_id, userId]);

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: parseInt(countResult.rows[0].total)
    }
  });
});

// Get nearby listings
const getNearbyListings = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { latitude, longitude, radius = 5 } = req.query;

  if (!latitude || !longitude) {
    throw new AppError('Location coordinates required', 400);
  }

  const listingsResult = await query(
    `SELECT el.*, 
            u.first_name, u.last_name, u.profile_photo_url,
            ST_Distance(el.location::geography, ST_MakePoint($1, $2)::geography) / 1609.34 as distance_miles,
            ST_X(el.location::geometry) as listing_longitude,
            ST_Y(el.location::geometry) as listing_latitude
     FROM exchange_listings el
     JOIN users u ON el.user_id = u.id
     WHERE el.status = 'available'
       AND el.is_active = true
       AND el.expires_at > NOW()
       AND el.user_id != $3
       AND ST_DWithin(el.location::geography, ST_MakePoint($1, $2)::geography, $4 * 1609.34)
     ORDER BY distance_miles ASC
     LIMIT 50`,
    [longitude, latitude, userId, radius]
  );

  res.json({
    success: true,
    data: listingsResult.rows.map(listing => ({
      ...listing,
      distance: listing.distance_miles.toFixed(1)
    }))
  });
});

// Get single listing details
const getListing = asyncHandler(async (req, res) => {
  const { listingId } = req.params;
  const userId = req.user.id;

  // Get listing with owner info
  const listingResult = await query(
    `SELECT el.*, 
            u.first_name, u.last_name, u.profile_photo_url, u.bio,
            EXISTS(
              SELECT 1 FROM exchange_saved_listings 
              WHERE listing_id = el.id AND user_id = $2
            ) as is_saved,
            EXISTS(
              SELECT 1 FROM exchange_requests 
              WHERE listing_id = el.id AND requester_id = $2
            ) as has_requested,
            (
              SELECT json_build_object(
                'status', status,
                'message', message,
                'created_at', created_at
              )
              FROM exchange_requests
              WHERE listing_id = el.id AND requester_id = $2
              LIMIT 1
            ) as user_request,
            ST_X(el.location::geometry) as longitude,
            ST_Y(el.location::geometry) as latitude
     FROM exchange_listings el
     JOIN users u ON el.user_id = u.id
     WHERE el.id = $1`,
    [listingId, userId]
  );

  if (listingResult.rows.length === 0) {
    throw new AppError('Listing not found', 404);
  }

  const listing = listingResult.rows[0];

  // Increment view count if not owner
  if (listing.user_id !== userId) {
    await query(
      'UPDATE exchange_listings SET views_count = views_count + 1 WHERE id = $1',
      [listingId]
    );
  }

  // Get related listings
  const relatedResult = await query(
    `SELECT el.id, el.title, el.images, el.exchange_type, el.condition
     FROM exchange_listings el
     WHERE el.category = $1
       AND el.id != $2
       AND el.status = 'available'
       AND el.is_active = true
       AND el.neighborhood_id = $3
     ORDER BY el.created_at DESC
     LIMIT 4`,
    [listing.category, listingId, listing.neighborhood_id]
  );

  res.json({
    success: true,
    data: {
      ...listing,
      related_listings: relatedResult.rows
    }
  });
});

// Update listing
const updateListing = asyncHandler(async (req, res) => {
  const { listingId } = req.params;
  const userId = req.user.id;
  const updates = req.body;

  // Check ownership
  const ownerCheck = await query(
    'SELECT user_id, status FROM exchange_listings WHERE id = $1',
    [listingId]
  );

  if (ownerCheck.rows.length === 0) {
    throw new AppError('Listing not found', 404);
  }

  if (ownerCheck.rows[0].user_id !== userId) {
    throw new AppError('Only the owner can update this listing', 403);
  }

  // Check if has active requests
  const activeRequests = await query(
    `SELECT COUNT(*) as count 
     FROM exchange_requests 
     WHERE listing_id = $1 AND status IN ('pending', 'accepted')`,
    [listingId]
  );

  if (activeRequests.rows[0].count > 0) {
    throw new AppError('Cannot update listing with active requests', 400);
  }

  // Build update query
  const allowedUpdates = [
    'title', 'description', 'condition', 'tags', 'images',
    'pickup_availability', 'duration_days', 'address'
  ];

  const updateFields = [];
  const values = [];
  let paramCount = 1;

  for (const field of allowedUpdates) {
    if (updates[field] !== undefined) {
      updateFields.push(`${field} = $${paramCount++}`);
      values.push(updates[field]);
    }
  }

  if (updates.location) {
    updateFields.push(`location = ST_MakePoint($${paramCount++}, $${paramCount++})::geography`);
    values.push(updates.location.longitude, updates.location.latitude);
  }

  if (updateFields.length === 0) {
    throw new AppError('No valid updates provided', 400);
  }

  values.push(listingId);

  const result = await query(
    `UPDATE exchange_listings 
     SET ${updateFields.join(', ')}, updated_at = NOW()
     WHERE id = $${paramCount}
     RETURNING *`,
    values
  );

  // Notify watchers
  const watchers = await query(
    `SELECT user_id FROM exchange_saved_listings WHERE listing_id = $1`,
    [listingId]
  );

  for (const watcher of watchers.rows) {
    await sendRealtimeNotification(watcher.user_id, {
      type: 'exchange_listing_updated',
      title: 'Saved Listing Updated',
      body: `"${result.rows[0].title}" has been updated`,
      data: { listing_id: listingId }
    });
  }

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Delete/deactivate listing
const deleteListing = asyncHandler(async (req, res) => {
  const { listingId } = req.params;
  const userId = req.user.id;

  // Check ownership
  const listingCheck = await query(
    'SELECT user_id, title, status FROM exchange_listings WHERE id = $1',
    [listingId]
  );

  if (listingCheck.rows.length === 0) {
    throw new AppError('Listing not found', 404);
  }

  if (listingCheck.rows[0].user_id !== userId) {
    throw new AppError('Only the owner can delete this listing', 403);
  }

  // Check for active exchanges
  const activeExchanges = await query(
    `SELECT COUNT(*) as count 
     FROM exchange_requests 
     WHERE listing_id = $1 AND status = 'accepted'`,
    [listingId]
  );

  if (activeExchanges.rows[0].count > 0) {
    throw new AppError('Cannot delete listing with active exchange', 400);
  }

  await withTransaction(async (client) => {
    // Soft delete the listing
    await client.query(
      `UPDATE exchange_listings 
       SET is_active = false, status = 'cancelled', updated_at = NOW()
       WHERE id = $1`,
      [listingId]
    );

    // Cancel all pending requests
    const pendingRequests = await client.query(
      `UPDATE exchange_requests 
       SET status = 'cancelled', updated_at = NOW()
       WHERE listing_id = $1 AND status = 'pending'
       RETURNING requester_id`,
      [listingId]
    );

    // Notify requesters
    for (const request of pendingRequests.rows) {
      await sendRealtimeNotification(request.requester_id, {
        type: 'exchange_request_cancelled',
        title: 'Exchange Cancelled',
        body: `"${listingCheck.rows[0].title}" is no longer available`,
        data: { listing_id: listingId }
      });
    }
  });

  res.json({
    success: true,
    message: 'Listing deleted successfully'
  });
});

// Request an item
const requestItem = asyncHandler(async (req, res) => {
  const { listingId } = req.params;
  const userId = req.user.id;
  const { message, pickup_date, return_date } = req.body;

  // Get listing details
  const listingResult = await query(
    `SELECT el.*, u.first_name as owner_first_name
     FROM exchange_listings el
     JOIN users u ON el.user_id = u.id
     WHERE el.id = $1`,
    [listingId]
  );

  if (listingResult.rows.length === 0) {
    throw new AppError('Listing not found', 404);
  }

  const listing = listingResult.rows[0];

  // Validate listing status
  if (listing.status !== 'available' || !listing.is_active) {
    throw new AppError('Listing is not available', 400);
  }

  // Check if user already requested
  const existingRequest = await query(
    `SELECT id FROM exchange_requests 
     WHERE listing_id = $1 AND requester_id = $2 AND status != 'cancelled'`,
    [listingId, userId]
  );

  if (existingRequest.rows.length > 0) {
    throw new AppError('You have already requested this item', 400);
  }

  // Cannot request own item
  if (listing.user_id === userId) {
    throw new AppError('Cannot request your own item', 400);
  }

  // Validate return date for lending
  if (listing.exchange_type === 'lend' && !return_date) {
    throw new AppError('Return date required for borrowed items', 400);
  }

  const requestData = await withTransaction(async (client) => {
    // Create request
    const requestResult = await client.query(
      `INSERT INTO exchange_requests 
       (listing_id, requester_id, owner_id, message, pickup_date, return_date)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING *`,
      [listingId, userId, listing.user_id, message, pickup_date, return_date]
    );

    const request = requestResult.rows[0];

    // Create conversation
    const conversationResult = await client.query(
      `INSERT INTO conversations (conversation_type, created_by_id)
       VALUES ('direct', $1)
       RETURNING id`,
      [userId]
    );

    const conversationId = conversationResult.rows[0].id;

    // Add participants
    await client.query(
      `INSERT INTO conversation_participants (conversation_id, user_id)
       VALUES ($1, $2), ($1, $3)`,
      [conversationId, userId, listing.user_id]
    );

    // Send initial message
    const messageContent = `Hi! I'm interested in your listing "${listing.title}". ${message || ''}`;
    await client.query(
      `INSERT INTO direct_messages (conversation_id, sender_id, content)
       VALUES ($1, $2, $3)`,
      [conversationId, userId, messageContent]
    );

    return request;
  });

  // Send notification to owner
  await sendRealtimeNotification(listing.user_id, {
    type: 'exchange_request_received',
    title: 'New Exchange Request',
    body: `${req.user.first_name} wants your "${listing.title}"`,
    data: { 
      listing_id: listingId, 
      request_id: requestData.id 
    }
  });

  res.status(201).json({
    success: true,
    data: requestData
  });
});

// Get exchange requests
const getRequests = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { type = 'all', status, limit = 20, offset = 0 } = req.query;

  let queryStr = `
    SELECT er.*, 
           el.title, el.images, el.exchange_type, el.category,
           requester.first_name as requester_first_name,
           requester.last_name as requester_last_name,
           requester.profile_photo_url as requester_photo,
           owner.first_name as owner_first_name,
           owner.last_name as owner_last_name,
           owner.profile_photo_url as owner_photo
    FROM exchange_requests er
    JOIN exchange_listings el ON er.listing_id = el.id
    JOIN users requester ON er.requester_id = requester.id
    JOIN users owner ON er.owner_id = owner.id
    WHERE `;

  const params = [];
  let paramCount = 0;

  // Filter by type
  if (type === 'sent') {
    queryStr += `er.requester_id = $${++paramCount}`;
    params.push(userId);
  } else if (type === 'received') {
    queryStr += `er.owner_id = $${++paramCount}`;
    params.push(userId);
  } else {
    queryStr += `(er.requester_id = $${++paramCount} OR er.owner_id = $${paramCount})`;
    params.push(userId);
  }

  // Filter by status
  if (status) {
    queryStr += ` AND er.status = $${++paramCount}`;
    params.push(status);
  }

  queryStr += ` ORDER BY er.created_at DESC LIMIT $${++paramCount} OFFSET $${++paramCount}`;
  params.push(limit, offset);

  const result = await query(queryStr, params);

  // Get unread count
  const unreadCount = await query(
    `SELECT COUNT(*) as count
     FROM exchange_requests er
     WHERE er.owner_id = $1 AND er.status = 'pending'`,
    [userId]
  );

  res.json({
    success: true,
    data: result.rows,
    unread_count: parseInt(unreadCount.rows[0].count),
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Update request status
const updateRequest = asyncHandler(async (req, res) => {
  const { requestId } = req.params;
  const userId = req.user.id;
  const { status } = req.body;

  if (!['accepted', 'rejected', 'completed', 'cancelled'].includes(status)) {
    throw new AppError('Invalid status', 400);
  }

  // Get request details
  const requestResult = await query(
    `SELECT er.*, el.title, el.exchange_type
     FROM exchange_requests er
     JOIN exchange_listings el ON er.listing_id = el.id
     WHERE er.id = $1`,
    [requestId]
  );

  if (requestResult.rows.length === 0) {
    throw new AppError('Request not found', 404);
  }

  const request = requestResult.rows[0];

  // Validate permissions
  if (status === 'accepted' || status === 'rejected') {
    if (request.owner_id !== userId) {
      throw new AppError('Only the owner can accept/reject requests', 403);
    }
  } else if (status === 'cancelled') {
    if (request.requester_id !== userId) {
      throw new AppError('Only the requester can cancel', 403);
    }
  } else if (status === 'completed') {
    if (request.owner_id !== userId && request.requester_id !== userId) {
      throw new AppError('Only participants can mark as completed', 403);
    }
  }

  // Update request
  const updateResult = await withTransaction(async (client) => {
    const result = await client.query(
      `UPDATE exchange_requests 
       SET status = $2, updated_at = NOW()
       WHERE id = $1
       RETURNING *`,
      [requestId, status]
    );

    // If accepted, update listing status
    if (status === 'accepted') {
      await client.query(
        `UPDATE exchange_listings 
         SET status = 'pending'
         WHERE id = $1`,
        [request.listing_id]
      );

      // Reject other pending requests
      const rejectedRequests = await client.query(
        `UPDATE exchange_requests 
         SET status = 'rejected', updated_at = NOW()
         WHERE listing_id = $1 AND id != $2 AND status = 'pending'
         RETURNING requester_id`,
        [request.listing_id, requestId]
      );

      // Notify rejected users
      for (const rejected of rejectedRequests.rows) {
        await sendRealtimeNotification(rejected.requester_id, {
          type: 'exchange_request_rejected',
          title: 'Request Not Accepted',
          body: `Your request for "${request.title}" was not accepted`,
          data: { listing_id: request.listing_id }
        });
      }
    } else if (status === 'completed') {
      await client.query(
        `UPDATE exchange_listings 
         SET status = 'completed', is_active = false
         WHERE id = $1`,
        [request.listing_id]
      );
    }

    return result.rows[0];
  });

  // Send notification
  const notifyUserId = request.owner_id === userId ? request.requester_id : request.owner_id;
  const notificationMessages = {
    accepted: 'Your exchange request was accepted!',
    rejected: 'Your exchange request was not accepted',
    completed: 'Exchange marked as completed',
    cancelled: 'Exchange request was cancelled'
  };

  await sendRealtimeNotification(notifyUserId, {
    type: `exchange_request_${status}`,
    title: 'Exchange Update',
    body: notificationMessages[status],
    data: { 
      request_id: requestId,
      listing_id: request.listing_id 
    }
  });

  res.json({
    success: true,
    data: updateResult
  });
});

// Rate exchange
const rateExchange = asyncHandler(async (req, res) => {
  const { requestId } = req.params;
  const userId = req.user.id;
  const { rating, comment } = req.body;

  // Get request details
  const requestResult = await query(
    `SELECT er.*, el.title
     FROM exchange_requests er
     JOIN exchange_listings el ON er.listing_id = el.id
     WHERE er.id = $1`,
    [requestId]
  );

  if (requestResult.rows.length === 0) {
    throw new AppError('Exchange request not found', 404);
  }

  const request = requestResult.rows[0];

  // Verify user is part of exchange
  if (request.owner_id !== userId && request.requester_id !== userId) {
    throw new AppError('Only participants can rate this exchange', 403);
  }

  // Verify exchange is completed
  if (request.status !== 'completed') {
    throw new AppError('Can only rate completed exchanges', 400);
  }

  // Check if already rated
  const existingRating = await query(
    `SELECT id FROM exchange_ratings 
     WHERE exchange_request_id = $1 AND rater_id = $2`,
    [requestId, userId]
  );

  if (existingRating.rows.length > 0) {
    throw new AppError('You have already rated this exchange', 400);
  }

  // Determine who is being rated
  const ratedUserId = request.owner_id === userId ? request.requester_id : request.owner_id;

  // Create rating
  const ratingResult = await query(
    `INSERT INTO exchange_ratings 
     (exchange_request_id, rater_id, rated_user_id, rating, comment)
     VALUES ($1, $2, $3, $4, $5)
     RETURNING *`,
    [requestId, userId, ratedUserId, rating, comment]
  );

  // Update user's average rating
  await query(
    `UPDATE users 
     SET exchange_rating = (
       SELECT AVG(rating)::DECIMAL(3,2) 
       FROM exchange_ratings 
       WHERE rated_user_id = $1
     )
     WHERE id = $1`,
    [ratedUserId]
  );

  // Notify rated user
  await sendRealtimeNotification(ratedUserId, {
    type: 'exchange_rating_received',
    title: 'New Exchange Rating',
    body: `You received a ${rating}-star rating for "${request.title}"`,
    data: { 
      request_id: requestId,
      rating 
    }
  });

  res.status(201).json({
    success: true,
    data: ratingResult.rows[0]
  });
});

// Get categories with stats
const getCategories = asyncHandler(async (req, res) => {
  const neighborhoodId = req.user.neighborhood_id;

  // Get category counts
  const categoryStats = await query(
    `SELECT category, COUNT(*) as count
     FROM exchange_listings
     WHERE neighborhood_id = $1
       AND status = 'available'
       AND is_active = true
     GROUP BY category
     ORDER BY count DESC`,
    [neighborhoodId]
  );

  // Get user's activity
  const userActivity = await query(
    `SELECT el.category, COUNT(*) as interaction_count
     FROM exchange_listings el
     LEFT JOIN exchange_requests er ON el.id = er.listing_id
     LEFT JOIN exchange_saved_listings esl ON el.id = esl.listing_id
     WHERE (er.requester_id = $1 OR esl.user_id = $1)
     GROUP BY el.category
     ORDER BY interaction_count DESC
     LIMIT 5`,
    [req.user.id]
  );

  const categories = EXCHANGE_CATEGORIES.map(cat => {
    const stats = categoryStats.rows.find(s => s.category === cat);
    const isPopular = userActivity.rows.some(a => a.category === cat);
    
    return {
      name: cat,
      count: stats?.count || 0,
      is_suggested: isPopular
    };
  });

  res.json({
    success: true,
    data: {
      categories,
      popular: categoryStats.rows.slice(0, 5),
      suggested: userActivity.rows.map(a => a.category)
    }
  });
});

// Save/unsave listing
const saveListing = asyncHandler(async (req, res) => {
  const { listingId } = req.params;
  const userId = req.user.id;

  // Check if listing exists
  const listingCheck = await query(
    'SELECT id FROM exchange_listings WHERE id = $1',
    [listingId]
  );

  if (listingCheck.rows.length === 0) {
    throw new AppError('Listing not found', 404);
  }

  // Check if already saved
  const savedCheck = await query(
    'SELECT 1 FROM exchange_saved_listings WHERE user_id = $1 AND listing_id = $2',
    [userId, listingId]
  );

  let action;
  if (savedCheck.rows.length > 0) {
    // Unsave
    await query(
      'DELETE FROM exchange_saved_listings WHERE user_id = $1 AND listing_id = $2',
      [userId, listingId]
    );
    action = 'unsaved';
  } else {
    // Check saved limit
    const savedCount = await query(
      'SELECT COUNT(*) as count FROM exchange_saved_listings WHERE user_id = $1',
      [userId]
    );

    if (savedCount.rows[0].count >= 50) {
      throw new AppError('Maximum saved listings limit reached (50)', 400);
    }

    // Save
    await query(
      'INSERT INTO exchange_saved_listings (user_id, listing_id) VALUES ($1, $2)',
      [userId, listingId]
    );
    action = 'saved';
  }

  res.json({
    success: true,
    data: { action }
  });
});

// Get user's exchange dashboard
const getDashboard = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  // Get active listings
  const activeListings = await query(
    `SELECT COUNT(*) as count, 
            SUM(views_count) as total_views
     FROM exchange_listings
     WHERE user_id = $1 AND status = 'available' AND is_active = true`,
    [userId]
  );

  // Get pending requests (received)
  const pendingRequests = await query(
    `SELECT COUNT(*) as count
     FROM exchange_requests er
     JOIN exchange_listings el ON er.listing_id = el.id
     WHERE el.user_id = $1 AND er.status = 'pending'`,
    [userId]
  );

  // Get exchange history
  const exchangeHistory = await query(
    `SELECT 
       COUNT(*) as total_exchanges,
       COUNT(CASE WHEN er.owner_id = $1 THEN 1 END) as items_given,
       COUNT(CASE WHEN er.requester_id = $1 THEN 1 END) as items_received
     FROM exchange_requests er
     WHERE (er.owner_id = $1 OR er.requester_id = $1)
       AND er.status = 'completed'`,
    [userId]
  );

  // Get rating summary
  const ratingSummary = await query(
    `SELECT 
       AVG(rating)::DECIMAL(3,2) as average_rating,
       COUNT(*) as total_ratings
     FROM exchange_ratings
     WHERE rated_user_id = $1`,
    [userId]
  );

  // Get recent activity
  const recentActivity = await query(
    `(SELECT 'listing' as type, el.id, el.title, el.created_at
      FROM exchange_listings el
      WHERE el.user_id = $1
      ORDER BY el.created_at DESC
      LIMIT 5)
     UNION ALL
     (SELECT 'request' as type, er.id, el.title, er.created_at
      FROM exchange_requests er
      JOIN exchange_listings el ON er.listing_id = el.id
      WHERE er.requester_id = $1
      ORDER BY er.created_at DESC
      LIMIT 5)
     ORDER BY created_at DESC
     LIMIT 10`,
    [userId]
  );

  // Get neighborhood stats
  const neighborhoodStats = await query(
    `SELECT 
       COUNT(DISTINCT el.id) as total_listings,
       COUNT(DISTINCT el.user_id) as active_users,
       COUNT(DISTINCT CASE WHEN er.status = 'completed' THEN er.id END) as completed_exchanges
     FROM exchange_listings el
     LEFT JOIN exchange_requests er ON el.id = er.listing_id
     WHERE el.neighborhood_id = $1
       AND el.created_at > NOW() - INTERVAL '30 days'`,
    [req.user.neighborhood_id]
  );

  res.json({
    success: true,
    data: {
      my_listings: {
        active: parseInt(activeListings.rows[0].count),
        total_views: parseInt(activeListings.rows[0].total_views) || 0
      },
      pending_requests: parseInt(pendingRequests.rows[0].count),
      exchange_history: exchangeHistory.rows[0],
      rating: {
        average: ratingSummary.rows[0].average_rating || 0,
        count: parseInt(ratingSummary.rows[0].total_ratings)
      },
      recent_activity: recentActivity.rows,
      neighborhood_stats: neighborhoodStats.rows[0]
    }
  });
});

module.exports = {
  createListing,
  getListings,
  getNearbyListings,
  getListing,
  updateListing,
  deleteListing,
  requestItem,
  getRequests,
  updateRequest,
  rateExchange,
  getCategories,
  saveListing,
  getDashboard
};