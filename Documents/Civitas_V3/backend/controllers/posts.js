const { query, withTransaction, geoQuery } = require('../config/database');
const { cache } = require('../config/redis');
const { AppError, asyncHandler } = require('../middleware/errorHandler');
const logger = require('../config/logger');
const { sendNotification } = require('../services/pushNotifications');
const { uploadToS3 } = require('../services/mediaUpload');

// Create a new post
const createPost = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const neighborhoodId = req.user.neighborhood_id;
  const {
    post_type,
    content,
    help_type,
    urgency_level,
    location,
    address,
    is_anonymous,
    visibility
  } = req.body;

  // Build location point if provided
  let locationPoint = null;
  if (location?.latitude && location?.longitude) {
    locationPoint = `POINT(${location.longitude} ${location.latitude})`;
  }

  const result = await query(
    `INSERT INTO posts (
      user_id, neighborhood_id, post_type, content, help_type, 
      urgency_level, location, address, is_anonymous, visibility
    )
    VALUES ($1, $2, $3, $4, $5, $6, ST_GeomFromText($7, 4326), $8, $9, $10)
    RETURNING *,
      ST_X(location::geometry) as longitude,
      ST_Y(location::geometry) as latitude`,
    [
      userId, neighborhoodId, post_type, content, help_type,
      urgency_level, locationPoint, address, is_anonymous || false, visibility || 'neighbors'
    ]
  );

  const post = result.rows[0];

  // Get user info for response
  const userResult = await query(
    `SELECT id, first_name, last_name, profile_photo_url, trust_score
     FROM users WHERE id = $1`,
    [userId]
  );

  post.user = is_anonymous ? {
    id: null,
    first_name: 'Anonymous',
    last_name: 'Neighbor',
    profile_photo_url: null
  } : userResult.rows[0];

  // Send notifications for urgent help requests
  if (post_type === 'help_request' && urgency_level === 'high') {
    // TODO: Send push notifications to nearby neighbors
  }

  // Clear feed cache
  await cache.del(`feed:${neighborhoodId}:*`);

  res.status(201).json({
    success: true,
    data: post
  });
});

// Get neighborhood feed
const getFeed = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const neighborhoodId = req.user.neighborhood_id;
  const { post_type, help_status, limit = 20, offset = 0 } = req.query;

  // Build cache key
  const cacheKey = `feed:${neighborhoodId}:${post_type || 'all'}:${help_status || 'all'}:${limit}:${offset}`;
  
  // Check cache
  const cached = await cache.get(cacheKey);
  if (cached) {
    return res.json({
      success: true,
      data: cached.data,
      pagination: cached.pagination
    });
  }

  // Build query conditions
  let conditions = ['p.neighborhood_id = $1', 'p.status = \'active\''];
  let params = [neighborhoodId];
  let paramCount = 1;

  if (post_type) {
    conditions.push(`p.post_type = $${++paramCount}`);
    params.push(post_type);
  }

  if (help_status) {
    conditions.push(`p.help_status = $${++paramCount}`);
    params.push(help_status);
  }

  // Check for blocked users
  conditions.push(`NOT EXISTS (
    SELECT 1 FROM user_blocks 
    WHERE blocker_id = $${++paramCount} AND blocked_id = p.user_id
  )`);
  params.push(userId);

  // Get posts
  const result = await query(
    `SELECT 
      p.*,
      ST_X(p.location::geometry) as longitude,
      ST_Y(p.location::geometry) as latitude,
      CASE 
        WHEN p.is_anonymous THEN NULL 
        ELSE u.id 
      END as user_id,
      CASE 
        WHEN p.is_anonymous THEN 'Anonymous' 
        ELSE u.first_name 
      END as user_first_name,
      CASE 
        WHEN p.is_anonymous THEN 'Neighbor' 
        ELSE u.last_name 
      END as user_last_name,
      CASE 
        WHEN p.is_anonymous THEN NULL 
        ELSE u.profile_photo_url 
      END as user_profile_photo,
      u.trust_score as user_trust_score,
      u.is_verified as user_verified,
      EXISTS(SELECT 1 FROM post_reactions WHERE post_id = p.id AND user_id = $${++paramCount}) as user_reacted,
      (SELECT reaction_type FROM post_reactions WHERE post_id = p.id AND user_id = $${paramCount} LIMIT 1) as user_reaction,
      COALESCE(
        (SELECT json_agg(json_build_object(
          'id', pm.id,
          'media_type', pm.media_type,
          'media_url', pm.media_url,
          'thumbnail_url', pm.thumbnail_url,
          'caption', pm.caption
        ) ORDER BY pm.display_order)
        FROM post_media pm
        WHERE pm.post_id = p.id
      ), '[]'::json) as media
    FROM posts p
    JOIN users u ON p.user_id = u.id
    WHERE ${conditions.join(' AND ')}
    ORDER BY 
      CASE WHEN p.urgency_level = 'emergency' THEN 0
           WHEN p.urgency_level = 'high' THEN 1
           WHEN p.urgency_level = 'medium' THEN 2
           ELSE 3 END,
      p.created_at DESC
    LIMIT $${++paramCount} OFFSET $${++paramCount}`,
    [...params, userId, limit, offset]
  );

  // Get total count
  const countResult = await query(
    `SELECT COUNT(*) as total 
     FROM posts p
     WHERE ${conditions.join(' AND ')}`,
    params
  );

  const response = {
    data: result.rows,
    pagination: {
      total: parseInt(countResult.rows[0].total),
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  };

  // Cache for 5 minutes
  await cache.set(cacheKey, response, 300);

  res.json({
    success: true,
    ...response
  });
});

// Get single post
const getPost = asyncHandler(async (req, res) => {
  const { postId } = req.params;
  const userId = req.user.id;

  const result = await query(
    `SELECT 
      p.*,
      ST_X(p.location::geometry) as longitude,
      ST_Y(p.location::geometry) as latitude,
      CASE 
        WHEN p.is_anonymous THEN NULL 
        ELSE u.id 
      END as user_id,
      CASE 
        WHEN p.is_anonymous THEN 'Anonymous' 
        ELSE u.first_name 
      END as user_first_name,
      CASE 
        WHEN p.is_anonymous THEN 'Neighbor' 
        ELSE u.last_name 
      END as user_last_name,
      CASE 
        WHEN p.is_anonymous THEN NULL 
        ELSE u.profile_photo_url 
      END as user_profile_photo,
      u.trust_score as user_trust_score,
      u.is_verified as user_verified,
      EXISTS(SELECT 1 FROM post_reactions WHERE post_id = p.id AND user_id = $2) as user_reacted,
      (SELECT reaction_type FROM post_reactions WHERE post_id = p.id AND user_id = $2 LIMIT 1) as user_reaction,
      COALESCE(
        (SELECT json_agg(json_build_object(
          'id', pm.id,
          'media_type', pm.media_type,
          'media_url', pm.media_url,
          'thumbnail_url', pm.thumbnail_url,
          'caption', pm.caption
        ) ORDER BY pm.display_order)
        FROM post_media pm
        WHERE pm.post_id = p.id
      ), '[]'::json) as media
    FROM posts p
    JOIN users u ON p.user_id = u.id
    WHERE p.id = $1 AND p.status = 'active'`,
    [postId, userId]
  );

  if (result.rows.length === 0) {
    throw new AppError('Post not found', 404);
  }

  // Increment view count
  await query(
    'UPDATE posts SET view_count = view_count + 1 WHERE id = $1',
    [postId]
  );

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Update post
const updatePost = asyncHandler(async (req, res) => {
  const { postId } = req.params;
  const userId = req.user.id;
  const { content, help_status } = req.body;

  // Check ownership
  const ownerCheck = await query(
    'SELECT user_id FROM posts WHERE id = $1',
    [postId]
  );

  if (ownerCheck.rows.length === 0) {
    throw new AppError('Post not found', 404);
  }

  if (ownerCheck.rows[0].user_id !== userId) {
    throw new AppError('Not authorized to update this post', 403);
  }

  // Update post
  const result = await query(
    `UPDATE posts
     SET content = COALESCE($2, content),
         help_status = COALESCE($3, help_status),
         edited_at = NOW(),
         updated_at = NOW()
     WHERE id = $1
     RETURNING *`,
    [postId, content, help_status]
  );

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Delete post
const deletePost = asyncHandler(async (req, res) => {
  const { postId } = req.params;
  const userId = req.user.id;

  // Check ownership
  const ownerCheck = await query(
    'SELECT user_id, neighborhood_id FROM posts WHERE id = $1',
    [postId]
  );

  if (ownerCheck.rows.length === 0) {
    throw new AppError('Post not found', 404);
  }

  if (ownerCheck.rows[0].user_id !== userId) {
    throw new AppError('Not authorized to delete this post', 403);
  }

  // Soft delete
  await query(
    'UPDATE posts SET status = \'deleted\', updated_at = NOW() WHERE id = $1',
    [postId]
  );

  // Clear cache
  await cache.del(`feed:${ownerCheck.rows[0].neighborhood_id}:*`);

  res.json({
    success: true,
    message: 'Post deleted successfully'
  });
});

// Upload media to post
const uploadPostMedia = asyncHandler(async (req, res) => {
  const { postId } = req.params;
  const userId = req.user.id;
  const files = req.files;
  const captions = req.body.captions || [];

  // Check ownership
  const ownerCheck = await query(
    'SELECT user_id FROM posts WHERE id = $1',
    [postId]
  );

  if (ownerCheck.rows.length === 0) {
    throw new AppError('Post not found', 404);
  }

  if (ownerCheck.rows[0].user_id !== userId) {
    throw new AppError('Not authorized to add media to this post', 403);
  }

  const mediaItems = [];

  await withTransaction(async (client) => {
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const caption = captions[i] || null;

      // TODO: Upload to S3
      const mediaUrl = file.location || `/uploads/${file.filename}`;
      const mediaType = file.mimetype.startsWith('video/') ? 'video' : 'image';

      const result = await client.query(
        `INSERT INTO post_media (post_id, media_type, media_url, caption, display_order)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING *`,
        [postId, mediaType, mediaUrl, caption, i]
      );

      mediaItems.push(result.rows[0]);
    }
  });

  res.json({
    success: true,
    data: mediaItems
  });
});

// Add reaction to post
const addReaction = asyncHandler(async (req, res) => {
  const { postId } = req.params;
  const userId = req.user.id;
  const { reaction_type } = req.body;

  await withTransaction(async (client) => {
    // Insert or update reaction
    await client.query(
      `INSERT INTO post_reactions (post_id, user_id, reaction_type)
       VALUES ($1, $2, $3)
       ON CONFLICT (post_id, user_id)
       DO UPDATE SET reaction_type = $3, created_at = NOW()`,
      [postId, userId, reaction_type]
    );

    // Update reaction count
    await client.query(
      `UPDATE posts 
       SET reaction_count = (
         SELECT COUNT(*) FROM post_reactions WHERE post_id = $1
       )
       WHERE id = $1`,
      [postId]
    );
  });

  // Send notification to post owner
  const postResult = await query(
    'SELECT user_id, content FROM posts WHERE id = $1',
    [postId]
  );

  if (postResult.rows[0].user_id !== userId) {
    // TODO: Send notification
  }

  res.json({
    success: true,
    message: 'Reaction added successfully'
  });
});

// Remove reaction from post
const removeReaction = asyncHandler(async (req, res) => {
  const { postId } = req.params;
  const userId = req.user.id;

  await withTransaction(async (client) => {
    // Remove reaction
    const result = await client.query(
      'DELETE FROM post_reactions WHERE post_id = $1 AND user_id = $2 RETURNING id',
      [postId, userId]
    );

    if (result.rows.length === 0) {
      throw new AppError('Reaction not found', 404);
    }

    // Update reaction count
    await client.query(
      `UPDATE posts 
       SET reaction_count = (
         SELECT COUNT(*) FROM post_reactions WHERE post_id = $1
       )
       WHERE id = $1`,
      [postId]
    );
  });

  res.json({
    success: true,
    message: 'Reaction removed successfully'
  });
});

// Get post comments
const getComments = asyncHandler(async (req, res) => {
  const { postId } = req.params;
  const { limit = 20, offset = 0 } = req.query;
  const userId = req.user.id;

  const result = await query(
    `SELECT 
      c.*,
      CASE 
        WHEN c.is_anonymous THEN NULL 
        ELSE u.id 
      END as user_id,
      CASE 
        WHEN c.is_anonymous THEN 'Anonymous' 
        ELSE u.first_name 
      END as user_first_name,
      CASE 
        WHEN c.is_anonymous THEN 'Neighbor' 
        ELSE u.last_name 
      END as user_last_name,
      CASE 
        WHEN c.is_anonymous THEN NULL 
        ELSE u.profile_photo_url 
      END as user_profile_photo,
      EXISTS(SELECT 1 FROM comment_reactions WHERE comment_id = c.id AND user_id = $2) as user_reacted,
      (SELECT reaction_type FROM comment_reactions WHERE comment_id = c.id AND user_id = $2 LIMIT 1) as user_reaction,
      (SELECT COUNT(*) FROM post_comments WHERE parent_comment_id = c.id) as reply_count
    FROM post_comments c
    JOIN users u ON c.user_id = u.id
    WHERE c.post_id = $1 AND c.status = 'active' AND c.parent_comment_id IS NULL
    ORDER BY c.created_at ASC
    LIMIT $3 OFFSET $4`,
    [postId, userId, limit, offset]
  );

  const countResult = await query(
    'SELECT COUNT(*) as total FROM post_comments WHERE post_id = $1 AND status = \'active\' AND parent_comment_id IS NULL',
    [postId]
  );

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      total: parseInt(countResult.rows[0].total),
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Add comment to post
const addComment = asyncHandler(async (req, res) => {
  const { postId } = req.params;
  const userId = req.user.id;
  const { content, parent_comment_id, is_anonymous } = req.body;

  const comment = await withTransaction(async (client) => {
    // Insert comment
    const result = await client.query(
      `INSERT INTO post_comments (post_id, user_id, parent_comment_id, content, is_anonymous)
       VALUES ($1, $2, $3, $4, $5)
       RETURNING *`,
      [postId, userId, parent_comment_id, content, is_anonymous || false]
    );

    // Update comment count
    await client.query(
      `UPDATE posts 
       SET comment_count = (
         SELECT COUNT(*) FROM post_comments 
         WHERE post_id = $1 AND status = 'active'
       )
       WHERE id = $1`,
      [postId]
    );

    return result.rows[0];
  });

  // Get user info for response
  const userResult = await query(
    'SELECT id, first_name, last_name, profile_photo_url FROM users WHERE id = $1',
    [userId]
  );

  comment.user = is_anonymous ? {
    id: null,
    first_name: 'Anonymous',
    last_name: 'Neighbor',
    profile_photo_url: null
  } : userResult.rows[0];

  // Send notification
  const postResult = await query(
    'SELECT user_id, content FROM posts WHERE id = $1',
    [postId]
  );

  if (postResult.rows[0].user_id !== userId) {
    // TODO: Send notification
  }

  res.status(201).json({
    success: true,
    data: comment
  });
});

// Offer help for a post
const offerHelp = asyncHandler(async (req, res) => {
  const { postId } = req.params;
  const helperId = req.user.id;
  const { message } = req.body;

  // Check if post is a help request
  const postCheck = await query(
    'SELECT user_id, post_type, help_status FROM posts WHERE id = $1',
    [postId]
  );

  if (postCheck.rows.length === 0) {
    throw new AppError('Post not found', 404);
  }

  if (postCheck.rows[0].post_type !== 'help_request') {
    throw new AppError('This is not a help request', 400);
  }

  if (postCheck.rows[0].help_status === 'completed') {
    throw new AppError('This help request has been completed', 400);
  }

  if (postCheck.rows[0].user_id === helperId) {
    throw new AppError('Cannot offer help on your own request', 400);
  }

  // Check if already offered
  const existingOffer = await query(
    'SELECT id FROM help_offers WHERE post_id = $1 AND helper_id = $2',
    [postId, helperId]
  );

  if (existingOffer.rows.length > 0) {
    throw new AppError('You have already offered help for this request', 400);
  }

  // Create help offer
  const result = await query(
    `INSERT INTO help_offers (post_id, helper_id, message)
     VALUES ($1, $2, $3)
     RETURNING *`,
    [postId, helperId, message]
  );

  // TODO: Send notification to post owner

  res.status(201).json({
    success: true,
    data: result.rows[0]
  });
});

// Accept help offer
const acceptHelpOffer = asyncHandler(async (req, res) => {
  const { postId, offerId } = req.params;
  const userId = req.user.id;

  // Check ownership and offer validity
  const checkResult = await query(
    `SELECT p.user_id, p.help_status, ho.helper_id, ho.status
     FROM posts p
     JOIN help_offers ho ON p.id = ho.post_id
     WHERE p.id = $1 AND ho.id = $2`,
    [postId, offerId]
  );

  if (checkResult.rows.length === 0) {
    throw new AppError('Help offer not found', 404);
  }

  const { user_id, help_status, helper_id, status } = checkResult.rows[0];

  if (user_id !== userId) {
    throw new AppError('Not authorized to accept this help offer', 403);
  }

  if (help_status !== 'open') {
    throw new AppError('Help request is no longer open', 400);
  }

  if (status !== 'pending') {
    throw new AppError('Help offer has already been processed', 400);
  }

  await withTransaction(async (client) => {
    // Accept the offer
    await client.query(
      `UPDATE help_offers 
       SET status = 'accepted', accepted_at = NOW()
       WHERE id = $1`,
      [offerId]
    );

    // Update post status
    await client.query(
      `UPDATE posts 
       SET help_status = 'in_progress'
       WHERE id = $1`,
      [postId]
    );

    // Decline other pending offers
    await client.query(
      `UPDATE help_offers 
       SET status = 'declined'
       WHERE post_id = $1 AND id != $2 AND status = 'pending'`,
      [postId, offerId]
    );
  });

  // TODO: Send notifications

  res.json({
    success: true,
    message: 'Help offer accepted successfully'
  });
});

// Complete help
const completeHelp = asyncHandler(async (req, res) => {
  const { postId, offerId } = req.params;
  const userId = req.user.id;
  const { rating, feedback } = req.body;

  // Check ownership
  const checkResult = await query(
    `SELECT p.user_id, ho.status, ho.helper_id
     FROM posts p
     JOIN help_offers ho ON p.id = ho.post_id
     WHERE p.id = $1 AND ho.id = $2`,
    [postId, offerId]
  );

  if (checkResult.rows.length === 0) {
    throw new AppError('Help offer not found', 404);
  }

  if (checkResult.rows[0].user_id !== userId) {
    throw new AppError('Not authorized to complete this help', 403);
  }

  if (checkResult.rows[0].status !== 'accepted') {
    throw new AppError('Help offer was not accepted', 400);
  }

  await withTransaction(async (client) => {
    // Complete the help offer
    await client.query(
      `UPDATE help_offers 
       SET status = 'completed', 
           completed_at = NOW(),
           rating = $3,
           feedback = $4
       WHERE id = $2`,
      [postId, offerId, rating, feedback]
    );

    // Update post status
    await client.query(
      `UPDATE posts 
       SET help_status = 'completed'
       WHERE id = $1`,
      [postId]
    );

    // Update helper's trust score if rated well
    if (rating >= 4) {
      await client.query(
        `UPDATE users 
         SET trust_score = LEAST(trust_score + 2, 100)
         WHERE id = $1`,
        [checkResult.rows[0].helper_id]
      );
    }
  });

  res.json({
    success: true,
    message: 'Help completed successfully'
  });
});

// Share post
const sharePost = asyncHandler(async (req, res) => {
  const { postId } = req.params;
  const userId = req.user.id;
  const { platform } = req.body;

  // Track share
  await withTransaction(async (client) => {
    await client.query(
      `INSERT INTO share_tracking (post_id, shared_by, share_platform)
       VALUES ($1, $2, $3)`,
      [postId, userId, platform || 'internal']
    );

    await client.query(
      `UPDATE posts 
       SET share_count = share_count + 1
       WHERE id = $1`,
      [postId]
    );
  });

  res.json({
    success: true,
    message: 'Post shared successfully'
  });
});

module.exports = {
  createPost,
  getFeed,
  getPost,
  updatePost,
  deletePost,
  uploadPostMedia,
  addReaction,
  removeReaction,
  getComments,
  addComment,
  offerHelp,
  acceptHelpOffer,
  completeHelp,
  sharePost
};