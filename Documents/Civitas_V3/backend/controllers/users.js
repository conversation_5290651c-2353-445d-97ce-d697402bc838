const { query, withTransaction } = require('../config/database');
const { cache } = require('../config/redis');
const { AppError, asyncHandler } = require('../middleware/errorHandler');
const logger = require('../config/logger');

// Get current user profile
const getProfile = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const result = await query(
    `SELECT id, email, first_name, last_name, profile_photo_url, cover_photo_url,
            bio, phone_number, is_verified, is_emergency_responder, trust_score,
            neighborhood_id, created_at, 
            ST_X(location::geometry) as longitude,
            ST_Y(location::geometry) as latitude
     FROM users
     WHERE id = $1`,
    [userId]
  );

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Update user profile
const updateProfile = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { first_name, last_name, bio, phone_number } = req.body;

  const result = await query(
    `UPDATE users
     SET first_name = COALESCE($2, first_name),
         last_name = COALESCE($3, last_name),
         bio = COALESCE($4, bio),
         phone_number = COALESCE($5, phone_number),
         updated_at = NOW()
     WHERE id = $1
     RETURNING id, email, first_name, last_name, bio, phone_number, 
               profile_photo_url, is_verified, trust_score`,
    [userId, first_name, last_name, bio, phone_number]
  );

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Upload profile photo
const uploadProfilePhoto = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // TODO: Implement actual file upload to S3
  const photoUrl = `https://cdn.civitas.app/profiles/${userId}-${Date.now()}.jpg`;

  const result = await query(
    `UPDATE users
     SET profile_photo_url = $2, updated_at = NOW()
     WHERE id = $1
     RETURNING profile_photo_url`,
    [userId, photoUrl]
  );

  res.json({
    success: true,
    data: {
      profile_photo_url: result.rows[0].profile_photo_url
    }
  });
});

// Upload cover photo
const uploadCoverPhoto = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // TODO: Implement actual file upload
  const photoUrl = `https://cdn.civitas.app/covers/${userId}-${Date.now()}.jpg`;

  const result = await query(
    `UPDATE users
     SET cover_photo_url = $2, updated_at = NOW()
     WHERE id = $1
     RETURNING cover_photo_url`,
    [userId, photoUrl]
  );

  res.json({
    success: true,
    data: {
      cover_photo_url: result.rows[0].cover_photo_url
    }
  });
});

// Get user by ID
const getUserById = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const currentUserId = req.user.id;

  // Check if user is blocked
  const blockCheck = await query(
    'SELECT 1 FROM user_blocks WHERE blocker_id = $1 AND blocked_id = $2',
    [currentUserId, userId]
  );

  if (blockCheck.rows.length > 0) {
    throw new AppError('User not found', 404);
  }

  const result = await query(
    `SELECT id, first_name, last_name, profile_photo_url, bio,
            is_verified, is_emergency_responder, trust_score, created_at,
            (SELECT COUNT(*) FROM user_followers WHERE following_id = $1) as followers_count,
            (SELECT COUNT(*) FROM user_followers WHERE follower_id = $1) as following_count,
            EXISTS(SELECT 1 FROM user_followers WHERE follower_id = $2 AND following_id = $1) as is_following
     FROM users
     WHERE id = $1 AND status = 'active'`,
    [userId, currentUserId]
  );

  if (result.rows.length === 0) {
    throw new AppError('User not found', 404);
  }

  res.json({
    success: true,
    data: result.rows[0]
  });
});

// Get user statistics
const getUserStats = asyncHandler(async (req, res) => {
  const { userId } = req.params;

  const stats = await query(
    `SELECT 
      (SELECT COUNT(*) FROM posts WHERE user_id = $1 AND status = 'active') as posts_count,
      (SELECT COUNT(*) FROM help_offers WHERE helper_id = $1 AND status = 'completed') as helps_given,
      (SELECT COUNT(*) FROM posts p 
       JOIN help_offers ho ON p.id = ho.post_id 
       WHERE p.user_id = $1 AND ho.status = 'completed') as helps_received,
      (SELECT COUNT(*) FROM user_followers WHERE following_id = $1) as followers_count,
      (SELECT COUNT(*) FROM user_followers WHERE follower_id = $1) as following_count,
      (SELECT trust_score FROM users WHERE id = $1) as trust_score`,
    [userId]
  );

  res.json({
    success: true,
    data: stats.rows[0]
  });
});

// Follow user
const followUser = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const followerId = req.user.id;

  if (userId === followerId) {
    throw new AppError('Cannot follow yourself', 400);
  }

  // Check if already following
  const existingFollow = await query(
    'SELECT 1 FROM user_followers WHERE follower_id = $1 AND following_id = $2',
    [followerId, userId]
  );

  if (existingFollow.rows.length > 0) {
    throw new AppError('Already following this user', 400);
  }

  await query(
    'INSERT INTO user_followers (follower_id, following_id) VALUES ($1, $2)',
    [followerId, userId]
  );

  // TODO: Send notification to followed user

  res.json({
    success: true,
    message: 'User followed successfully'
  });
});

// Unfollow user
const unfollowUser = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const followerId = req.user.id;

  const result = await query(
    'DELETE FROM user_followers WHERE follower_id = $1 AND following_id = $2 RETURNING id',
    [followerId, userId]
  );

  if (result.rows.length === 0) {
    throw new AppError('Not following this user', 400);
  }

  res.json({
    success: true,
    message: 'User unfollowed successfully'
  });
});

// Get followers
const getFollowers = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { limit = 20, offset = 0 } = req.query;

  const result = await query(
    `SELECT u.id, u.first_name, u.last_name, u.profile_photo_url, u.bio,
            u.is_verified, u.trust_score,
            EXISTS(SELECT 1 FROM user_followers WHERE follower_id = $4 AND following_id = u.id) as is_following
     FROM user_followers uf
     JOIN users u ON uf.follower_id = u.id
     WHERE uf.following_id = $1 AND u.status = 'active'
     ORDER BY uf.created_at DESC
     LIMIT $2 OFFSET $3`,
    [userId, limit, offset, req.user.id]
  );

  const countResult = await query(
    'SELECT COUNT(*) as total FROM user_followers WHERE following_id = $1',
    [userId]
  );

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      total: parseInt(countResult.rows[0].total),
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Get following
const getFollowing = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { limit = 20, offset = 0 } = req.query;

  const result = await query(
    `SELECT u.id, u.first_name, u.last_name, u.profile_photo_url, u.bio,
            u.is_verified, u.trust_score,
            EXISTS(SELECT 1 FROM user_followers WHERE follower_id = $4 AND following_id = u.id) as is_following
     FROM user_followers uf
     JOIN users u ON uf.following_id = u.id
     WHERE uf.follower_id = $1 AND u.status = 'active'
     ORDER BY uf.created_at DESC
     LIMIT $2 OFFSET $3`,
    [userId, limit, offset, req.user.id]
  );

  const countResult = await query(
    'SELECT COUNT(*) as total FROM user_followers WHERE follower_id = $1',
    [userId]
  );

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      total: parseInt(countResult.rows[0].total),
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Get skills
const getSkills = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const result = await query(
    `SELECT id, skill_name, category, verified, created_at
     FROM user_skills
     WHERE user_id = $1
     ORDER BY skill_name`,
    [userId]
  );

  res.json({
    success: true,
    data: result.rows
  });
});

// Add skills
const addSkills = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { skills } = req.body;

  const addedSkills = [];

  await withTransaction(async (client) => {
    for (const skill of skills) {
      // Check if skill already exists
      const existing = await client.query(
        'SELECT 1 FROM user_skills WHERE user_id = $1 AND skill_name = $2',
        [userId, skill]
      );

      if (existing.rows.length === 0) {
        const result = await client.query(
          `INSERT INTO user_skills (user_id, skill_name, category)
           VALUES ($1, $2, 'general')
           RETURNING id, skill_name, category, verified`,
          [userId, skill]
        );
        addedSkills.push(result.rows[0]);
      }
    }
  });

  res.json({
    success: true,
    data: addedSkills,
    message: `${addedSkills.length} skills added`
  });
});

// Remove skill
const removeSkill = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { skillId } = req.params;

  const result = await query(
    'DELETE FROM user_skills WHERE id = $1 AND user_id = $2 RETURNING id',
    [skillId, userId]
  );

  if (result.rows.length === 0) {
    throw new AppError('Skill not found', 404);
  }

  res.json({
    success: true,
    message: 'Skill removed successfully'
  });
});

// Block user
const blockUser = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const blockerId = req.user.id;
  const { reason } = req.body;

  if (userId === blockerId) {
    throw new AppError('Cannot block yourself', 400);
  }

  await withTransaction(async (client) => {
    // Add block
    await client.query(
      `INSERT INTO user_blocks (blocker_id, blocked_id, reason)
       VALUES ($1, $2, $3)
       ON CONFLICT (blocker_id, blocked_id) DO NOTHING`,
      [blockerId, userId, reason]
    );

    // Remove follows in both directions
    await client.query(
      'DELETE FROM user_followers WHERE (follower_id = $1 AND following_id = $2) OR (follower_id = $2 AND following_id = $1)',
      [blockerId, userId]
    );
  });

  res.json({
    success: true,
    message: 'User blocked successfully'
  });
});

// Unblock user
const unblockUser = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const blockerId = req.user.id;

  const result = await query(
    'DELETE FROM user_blocks WHERE blocker_id = $1 AND blocked_id = $2 RETURNING id',
    [blockerId, userId]
  );

  if (result.rows.length === 0) {
    throw new AppError('User not blocked', 400);
  }

  res.json({
    success: true,
    message: 'User unblocked successfully'
  });
});

// Get blocked users
const getBlockedUsers = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { limit = 20, offset = 0 } = req.query;

  const result = await query(
    `SELECT u.id, u.first_name, u.last_name, u.profile_photo_url,
            ub.reason, ub.created_at as blocked_at
     FROM user_blocks ub
     JOIN users u ON ub.blocked_id = u.id
     WHERE ub.blocker_id = $1
     ORDER BY ub.created_at DESC
     LIMIT $2 OFFSET $3`,
    [userId, limit, offset]
  );

  res.json({
    success: true,
    data: result.rows
  });
});

// Report user
const reportUser = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const reporterId = req.user.id;
  const { reason, description } = req.body;

  if (userId === reporterId) {
    throw new AppError('Cannot report yourself', 400);
  }

  await query(
    `INSERT INTO user_reports (reporter_id, reported_id, reason, description)
     VALUES ($1, $2, $3, $4)`,
    [reporterId, userId, reason, description]
  );

  res.json({
    success: true,
    message: 'Report submitted successfully'
  });
});

// Get user suggestions
const getUserSuggestions = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { limit = 10 } = req.query;

  // Get suggestions based on neighborhood and common connections
  const result = await query(
    `SELECT DISTINCT u.id, u.first_name, u.last_name, u.profile_photo_url, 
            u.bio, u.is_verified, u.trust_score,
            COUNT(DISTINCT uf2.follower_id) as mutual_connections
     FROM users u
     LEFT JOIN user_followers uf1 ON u.id = uf1.following_id
     LEFT JOIN user_followers uf2 ON uf1.follower_id = uf2.following_id AND uf2.follower_id = $1
     WHERE u.neighborhood_id = (SELECT neighborhood_id FROM users WHERE id = $1)
       AND u.id != $1
       AND u.status = 'active'
       AND NOT EXISTS (SELECT 1 FROM user_followers WHERE follower_id = $1 AND following_id = u.id)
       AND NOT EXISTS (SELECT 1 FROM user_blocks WHERE blocker_id = $1 AND blocked_id = u.id)
     GROUP BY u.id
     ORDER BY mutual_connections DESC, u.trust_score DESC
     LIMIT $2`,
    [userId, limit]
  );

  res.json({
    success: true,
    data: result.rows
  });
});

// Get privacy settings
const getPrivacySettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const result = await query(
    'SELECT privacy_settings FROM users WHERE id = $1',
    [userId]
  );

  const defaultSettings = {
    profile_visibility: 'neighbors_only',
    show_location: true,
    allow_messages_from: 'neighbors_only'
  };

  res.json({
    success: true,
    data: result.rows[0]?.privacy_settings || defaultSettings
  });
});

// Update privacy settings
const updatePrivacySettings = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const settings = req.body;

  const result = await query(
    `UPDATE users
     SET privacy_settings = privacy_settings || $2::jsonb,
         updated_at = NOW()
     WHERE id = $1
     RETURNING privacy_settings`,
    [userId, JSON.stringify(settings)]
  );

  res.json({
    success: true,
    data: result.rows[0].privacy_settings
  });
});

// Delete account
const deleteAccount = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { password, reason } = req.body;

  // TODO: Verify password before deletion

  await withTransaction(async (client) => {
    // Soft delete user
    await client.query(
      `UPDATE users 
       SET status = 'deleted', 
           email = CONCAT('deleted_', id, '_', email),
           deleted_at = NOW(),
           deletion_reason = $2
       WHERE id = $1`,
      [userId, reason]
    );

    // TODO: Clean up user data, posts, etc.
  });

  res.json({
    success: true,
    message: 'Account deleted successfully'
  });
});

module.exports = {
  getProfile,
  updateProfile,
  uploadProfilePhoto,
  uploadCoverPhoto,
  getUserById,
  getUserStats,
  followUser,
  unfollowUser,
  getFollowers,
  getFollowing,
  getSkills,
  addSkills,
  removeSkill,
  blockUser,
  unblockUser,
  getBlockedUsers,
  reportUser,
  getUserSuggestions,
  getPrivacySettings,
  updatePrivacySettings,
  deleteAccount
};