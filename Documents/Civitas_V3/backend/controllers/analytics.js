const { query, withTransaction } = require('../config/database');
const { cache } = require('../config/redis');
const { AppError, asyncHandler } = require('../middleware/errorHandler');
const logger = require('../config/logger');
const { Parser } = require('json2csv');

// Get community statistics
const getCommunityStats = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const neighborhoodId = req.user.neighborhood_id;
  const { startDate, endDate = new Date(), days = 30 } = req.query;

  const effectiveStartDate = startDate || new Date(Date.now() - days * 24 * 60 * 60 * 1000);

  // Get comprehensive stats
  const statsQuery = `
    WITH date_range AS (
      SELECT $1::date as start_date, $2::date as end_date
    ),
    user_stats AS (
      SELECT 
        COUNT(*) as total_users,
        COUNT(*) FILTER (WHERE created_at >= (SELECT start_date FROM date_range)) as new_users,
        COUNT(*) FILTER (WHERE last_login >= NOW() - INTERVAL '1 day') as active_today,
        COUNT(*) FILTER (WHERE last_login >= NOW() - INTERVAL '7 days') as active_week,
        COUNT(*) FILTER (WHERE last_login >= NOW() - INTERVAL '30 days') as active_month
      FROM users
      WHERE neighborhood_id = $3
    ),
    content_stats AS (
      SELECT 
        COUNT(DISTINCT p.id) as total_posts,
        COUNT(DISTINCT p.id) FILTER (WHERE p.created_at >= (SELECT start_date FROM date_range)) as new_posts,
        COUNT(DISTINCT p.user_id) as unique_posters,
        AVG(p.like_count) as avg_likes_per_post,
        AVG(p.comment_count) as avg_comments_per_post
      FROM posts p
      WHERE p.neighborhood_id = $3
    ),
    event_stats AS (
      SELECT 
        COUNT(DISTINCT e.id) as total_events,
        COUNT(DISTINCT e.id) FILTER (WHERE e.created_at >= (SELECT start_date FROM date_range)) as new_events,
        COUNT(DISTINCT e.id) FILTER (WHERE e.start_time > NOW()) as upcoming_events,
        AVG(e.rsvp_count) as avg_rsvps_per_event
      FROM events e
      WHERE e.neighborhood_id = $3
    ),
    exchange_stats AS (
      SELECT 
        COUNT(DISTINCT el.id) as total_listings,
        COUNT(DISTINCT el.id) FILTER (WHERE el.status = 'available') as active_listings,
        COUNT(DISTINCT el.id) FILTER (WHERE el.status = 'completed') as completed_exchanges,
        COUNT(DISTINCT el.user_id) as unique_exchangers
      FROM exchange_listings el
      WHERE el.neighborhood_id = $3
    ),
    engagement_metrics AS (
      SELECT 
        AVG(engagement_rate) as avg_engagement_rate,
        MAX(created_at) as last_calculated
      FROM community_metrics
      WHERE neighborhood_id = $3
      AND metric_date >= (SELECT start_date FROM date_range)
    )
    SELECT 
      row_to_json(user_stats) as users,
      row_to_json(content_stats) as content,
      row_to_json(event_stats) as events,
      row_to_json(exchange_stats) as exchange,
      row_to_json(engagement_metrics) as engagement
    FROM user_stats, content_stats, event_stats, exchange_stats, engagement_metrics
  `;

  const result = await query(statsQuery, [effectiveStartDate, endDate, neighborhoodId]);
  const stats = result.rows[0];

  res.json({
    success: true,
    data: {
      period: {
        start_date: effectiveStartDate,
        end_date: endDate,
        days: Math.ceil((endDate - effectiveStartDate) / (1000 * 60 * 60 * 24))
      },
      ...stats
    }
  });
});

// Get community health score
const getCommunityHealth = asyncHandler(async (req, res) => {
  const neighborhoodId = req.user.neighborhood_id;
  const { days = 30 } = req.query;

  // Use the stored function
  const healthResult = await query(
    'SELECT * FROM calculate_community_health_score($1, $2)',
    [neighborhoodId, days]
  );

  const health = healthResult.rows[0];

  // Get recommendations based on scores
  const recommendations = [];
  
  if (health.engagement_score < 60) {
    recommendations.push({
      category: 'engagement',
      priority: 'high',
      message: 'Consider organizing community events to boost engagement',
      actions: ['Create recurring events', 'Start discussion topics', 'Share local news']
    });
  }

  if (health.environmental_score < 60) {
    recommendations.push({
      category: 'environmental',
      priority: 'medium',
      message: 'Increase environmental impact through sharing and carpooling',
      actions: ['Promote item exchanges', 'Organize carpool groups', 'Share sustainability tips']
    });
  }

  if (health.activity_score < 60) {
    recommendations.push({
      category: 'activity',
      priority: 'medium',
      message: 'Encourage more community participation',
      actions: ['Welcome new members', 'Highlight active contributors', 'Create challenges']
    });
  }

  res.json({
    success: true,
    data: {
      scores: health,
      recommendations,
      calculated_at: new Date()
    }
  });
});

// Get user activity analytics
const getUserActivity = asyncHandler(async (req, res) => {
  const requestedUserId = req.query.userId || req.user.id;
  const { days = 30 } = req.query;

  // Check permission
  if (requestedUserId !== req.user.id && !['admin', 'moderator'].includes(req.user.role)) {
    throw new AppError('Cannot view other user analytics', 403);
  }

  const activityQuery = `
    WITH date_range AS (
      SELECT CURRENT_DATE - INTERVAL '1 day' * $2 as start_date
    ),
    daily_activity AS (
      SELECT 
        summary_date,
        posts_created,
        comments_made,
        likes_given,
        events_created,
        events_attended,
        exchanges_created,
        exchanges_completed,
        messages_sent,
        help_requests,
        help_responses,
        points_earned,
        engagement_score
      FROM user_activity_summaries
      WHERE user_id = $1
      AND summary_date >= (SELECT start_date FROM date_range)
      ORDER BY summary_date
    ),
    summary_stats AS (
      SELECT 
        SUM(posts_created) as total_posts,
        SUM(comments_made) as total_comments,
        SUM(likes_given) as total_likes,
        SUM(events_created) as total_events_created,
        SUM(events_attended) as total_events_attended,
        SUM(exchanges_created) as total_exchanges_created,
        SUM(exchanges_completed) as total_exchanges_completed,
        SUM(points_earned) as total_points,
        AVG(engagement_score) as avg_engagement_score
      FROM daily_activity
    )
    SELECT 
      json_agg(daily_activity ORDER BY summary_date) as daily_data,
      row_to_json(summary_stats) as summary
    FROM daily_activity, summary_stats
    GROUP BY summary_stats.*
  `;

  const result = await query(activityQuery, [requestedUserId, days]);

  res.json({
    success: true,
    data: {
      user_id: requestedUserId,
      period_days: days,
      ...result.rows[0]
    }
  });
});

// Get environmental impact
const getEnvironmentalImpact = asyncHandler(async (req, res) => {
  const neighborhoodId = req.user.neighborhood_id;
  const { days = 30 } = req.query;

  const impactQuery = `
    SELECT 
      SUM(exchanges_count) as total_exchanges,
      SUM(items_reused) as total_items_reused,
      SUM(estimated_weight_kg) as total_weight_saved,
      SUM(co2_saved_kg) as total_co2_saved,
      SUM(water_saved_liters) as total_water_saved,
      SUM(landfill_diverted_kg) as total_landfill_diverted,
      SUM(carpool_trips) as total_carpool_trips,
      SUM(carpool_people) as total_people_carpooled,
      SUM(carpool_distance_km) as total_distance_saved,
      json_agg(
        json_build_object(
          'date', date,
          'exchanges', exchanges_count,
          'co2_saved', co2_saved_kg,
          'carpool_trips', carpool_trips
        ) ORDER BY date
      ) as daily_data
    FROM environmental_impact
    WHERE neighborhood_id = $1
    AND date >= CURRENT_DATE - INTERVAL '1 day' * $2
  `;

  const result = await query(impactQuery, [neighborhoodId, days]);
  const impact = result.rows[0];

  // Calculate equivalents
  const equivalents = {
    trees_planted: Math.round((impact.total_co2_saved || 0) / 21.77), // kg CO2 per tree per year
    cars_off_road_days: Math.round((impact.total_co2_saved || 0) / 19.6), // kg CO2 per car per day
    plastic_bottles_saved: Math.round((impact.total_items_reused || 0) * 2.5),
    gallons_of_gas_saved: Math.round((impact.total_distance_saved || 0) / 40) // assuming 40km per gallon
  };

  res.json({
    success: true,
    data: {
      period_days: days,
      totals: {
        exchanges: impact.total_exchanges || 0,
        items_reused: impact.total_items_reused || 0,
        weight_saved_kg: impact.total_weight_saved || 0,
        co2_saved_kg: impact.total_co2_saved || 0,
        water_saved_liters: impact.total_water_saved || 0,
        landfill_diverted_kg: impact.total_landfill_diverted || 0,
        carpool_trips: impact.total_carpool_trips || 0,
        people_carpooled: impact.total_people_carpooled || 0,
        distance_saved_km: impact.total_distance_saved || 0
      },
      equivalents,
      daily_breakdown: impact.daily_data || []
    }
  });
});

// Get trending content
const getTrendingContent = asyncHandler(async (req, res) => {
  const neighborhoodId = req.user.neighborhood_id;
  const { contentType, days = 7, limit = 20 } = req.query;

  let trendingQuery = `
    WITH trending_posts AS (
      SELECT 
        'post' as type,
        p.id,
        p.content as title,
        p.user_id,
        u.first_name || ' ' || u.last_name as author_name,
        u.profile_photo_url as author_photo,
        p.like_count as engagement,
        p.view_count as views,
        p.created_at,
        (p.like_count * 2 + p.comment_count * 3 + p.view_count * 0.1) as score
      FROM posts p
      JOIN users u ON p.user_id = u.id
      WHERE p.neighborhood_id = $1
      AND p.created_at >= CURRENT_DATE - INTERVAL '1 day' * $2
      AND p.is_deleted = false
    ),
    trending_events AS (
      SELECT 
        'event' as type,
        e.id,
        e.title,
        e.host_id as user_id,
        u.first_name || ' ' || u.last_name as author_name,
        u.profile_photo_url as author_photo,
        e.rsvp_count as engagement,
        e.view_count as views,
        e.created_at,
        (e.rsvp_count * 5 + e.view_count * 0.2) as score
      FROM events e
      JOIN users u ON e.host_id = u.id
      WHERE e.neighborhood_id = $1
      AND e.created_at >= CURRENT_DATE - INTERVAL '1 day' * $2
      AND e.is_cancelled = false
    ),
    trending_listings AS (
      SELECT 
        'listing' as type,
        el.id,
        el.title,
        el.user_id,
        u.first_name || ' ' || u.last_name as author_name,
        u.profile_photo_url as author_photo,
        el.view_count as engagement,
        el.view_count as views,
        el.created_at,
        (el.view_count * 0.5 + el.saved_count * 3) as score
      FROM exchange_listings el
      JOIN users u ON el.user_id = u.id
      WHERE el.neighborhood_id = $1
      AND el.created_at >= CURRENT_DATE - INTERVAL '1 day' * $2
      AND el.status = 'available'
    )
  `;

  // Build combined query based on content type
  if (contentType === 'posts') {
    trendingQuery += `
      SELECT * FROM trending_posts
      ORDER BY score DESC
      LIMIT $3
    `;
  } else if (contentType === 'events') {
    trendingQuery += `
      SELECT * FROM trending_events
      ORDER BY score DESC
      LIMIT $3
    `;
  } else if (contentType === 'listings') {
    trendingQuery += `
      SELECT * FROM trending_listings
      ORDER BY score DESC
      LIMIT $3
    `;
  } else {
    trendingQuery += `
      SELECT * FROM (
        SELECT * FROM trending_posts
        UNION ALL
        SELECT * FROM trending_events
        UNION ALL
        SELECT * FROM trending_listings
      ) combined
      ORDER BY score DESC
      LIMIT $3
    `;
  }

  const result = await query(trendingQuery, [neighborhoodId, days, limit]);

  res.json({
    success: true,
    data: result.rows
  });
});

// Create report
const createReport = asyncHandler(async (req, res) => {
  const reporterId = req.user.id;
  const { reportedType, reportedId, reason, description } = req.body;

  // Check if already reported by this user
  const existingReport = await query(
    `SELECT id FROM reports 
     WHERE reporter_id = $1 AND reported_type = $2 AND reported_id = $3 
     AND status IN ('pending', 'investigating')`,
    [reporterId, reportedType, reportedId]
  );

  if (existingReport.rows.length > 0) {
    throw new AppError('You have already reported this content', 400);
  }

  // Create report
  const reportResult = await query(
    `INSERT INTO reports (reporter_id, reported_type, reported_id, reason, description)
     VALUES ($1, $2, $3, $4, $5)
     RETURNING *`,
    [reporterId, reportedType, reportedId, reason, description]
  );

  // Track analytics event
  await query(
    `SELECT track_analytics_event($1, 'content_reported', $2)`,
    [reporterId, { reported_type: reportedType, reason }]
  );

  res.status(201).json({
    success: true,
    data: reportResult.rows[0]
  });
});

// Get reports (admin/moderator only)
const getReports = asyncHandler(async (req, res) => {
  if (!['admin', 'moderator'].includes(req.user.role)) {
    throw new AppError('Admin or moderator access required', 403);
  }

  const { status, reportedType, limit = 50, offset = 0 } = req.query;

  let reportsQuery = `
    SELECT 
      r.*,
      u.first_name || ' ' || u.last_name as reporter_name,
      u.profile_photo_url as reporter_photo,
      (SELECT COUNT(*) FROM report_actions WHERE report_id = r.id) as action_count
    FROM reports r
    JOIN users u ON r.reporter_id = u.id
    WHERE 1=1
  `;

  const params = [];
  let paramCount = 0;

  if (status) {
    reportsQuery += ` AND r.status = $${++paramCount}`;
    params.push(status);
  }

  if (reportedType) {
    reportsQuery += ` AND r.reported_type = $${++paramCount}`;
    params.push(reportedType);
  }

  reportsQuery += ` ORDER BY 
    CASE r.priority 
      WHEN 'urgent' THEN 1
      WHEN 'high' THEN 2
      WHEN 'normal' THEN 3
      WHEN 'low' THEN 4
    END,
    r.created_at DESC
    LIMIT $${++paramCount} OFFSET $${++paramCount}
  `;
  params.push(limit, offset);

  const result = await query(reportsQuery, params);

  res.json({
    success: true,
    data: result.rows,
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
});

// Update report status (admin/moderator only)
const updateReport = asyncHandler(async (req, res) => {
  if (!['admin', 'moderator'].includes(req.user.role)) {
    throw new AppError('Admin or moderator access required', 403);
  }

  const { reportId } = req.params;
  const { status, actionType, resolution, notes } = req.body;

  await withTransaction(async (client) => {
    // Update report
    const updateResult = await client.query(
      'UPDATE reports SET status = $2, updated_at = NOW() WHERE id = $1 RETURNING *',
      [reportId, status]
    );

    if (updateResult.rows.length === 0) {
      throw new AppError('Report not found', 404);
    }

    // Add action record
    if (actionType || resolution || notes) {
      await client.query(
        `INSERT INTO report_actions (report_id, moderator_id, action_type, resolution, notes)
         VALUES ($1, $2, $3, $4, $5)`,
        [reportId, req.user.id, actionType, resolution, notes]
      );
    }

    return updateResult.rows[0];
  });

  res.json({
    success: true,
    message: 'Report updated successfully'
  });
});

// Get dashboard metrics
const getDashboard = asyncHandler(async (req, res) => {
  const neighborhoodId = req.user.neighborhood_id;
  const { days = 7 } = req.query;

  // Complex dashboard query
  const dashboardQuery = `
    WITH metrics AS (
      SELECT 
        -- User metrics
        (SELECT COUNT(*) FROM users WHERE neighborhood_id = $1) as total_users,
        (SELECT COUNT(*) FROM users WHERE neighborhood_id = $1 AND created_at >= CURRENT_DATE - INTERVAL '1 day' * $2) as new_users,
        (SELECT COUNT(*) FROM users WHERE neighborhood_id = $1 AND last_login >= NOW() - INTERVAL '24 hours') as active_today,
        
        -- Content metrics
        (SELECT COUNT(*) FROM posts WHERE neighborhood_id = $1 AND created_at >= CURRENT_DATE - INTERVAL '1 day' * $2) as new_posts,
        (SELECT COUNT(*) FROM events WHERE neighborhood_id = $1 AND start_time > NOW()) as upcoming_events,
        (SELECT COUNT(*) FROM exchange_listings WHERE neighborhood_id = $1 AND status = 'available') as active_listings,
        
        -- Engagement metrics
        (SELECT AVG(engagement_rate) FROM community_metrics WHERE neighborhood_id = $1 AND metric_date >= CURRENT_DATE - INTERVAL '1 day' * $2) as avg_engagement,
        
        -- Safety metrics
        (SELECT COUNT(*) FROM emergency_alerts WHERE neighborhood_id = $1 AND created_at >= CURRENT_DATE - INTERVAL '1 day' * $2) as emergency_alerts,
        (SELECT COUNT(*) FROM emergency_alerts WHERE neighborhood_id = $1 AND status = 'resolved' AND created_at >= CURRENT_DATE - INTERVAL '1 day' * $2) as resolved_alerts
    ),
    trends AS (
      SELECT 
        json_agg(
          json_build_object(
            'date', metric_date,
            'users', total_users,
            'active', active_users,
            'posts', total_posts,
            'engagement', engagement_rate
          ) ORDER BY metric_date
        ) as daily_trends
      FROM community_metrics
      WHERE neighborhood_id = $1
      AND metric_date >= CURRENT_DATE - INTERVAL '1 day' * $2
    )
    SELECT 
      row_to_json(metrics) as current_metrics,
      trends.daily_trends
    FROM metrics, trends
  `;

  const result = await query(dashboardQuery, [neighborhoodId, days]);
  const dashboard = result.rows[0];

  // Get community health
  const healthResult = await query(
    'SELECT * FROM calculate_community_health_score($1, $2)',
    [neighborhoodId, days]
  );

  res.json({
    success: true,
    data: {
      period: {
        days,
        generated_at: new Date()
      },
      metrics: dashboard.current_metrics,
      trends: dashboard.daily_trends || [],
      health: healthResult.rows[0]
    }
  });
});

// Export analytics data (admin only)
const exportData = asyncHandler(async (req, res) => {
  if (req.user.role !== 'admin') {
    throw new AppError('Admin access required', 403);
  }

  const { reportType, format = 'json', startDate, endDate = new Date() } = req.query;
  const effectiveStartDate = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

  let data;
  let filename;

  switch (reportType) {
    case 'user_activity':
      data = await _exportUserActivity(effectiveStartDate, endDate, req.user.neighborhood_id);
      filename = `user_activity_${effectiveStartDate.toISOString().split('T')[0]}_${endDate.toISOString().split('T')[0]}`;
      break;
    
    case 'community_stats':
      data = await _exportCommunityStats(effectiveStartDate, endDate, req.user.neighborhood_id);
      filename = `community_stats_${effectiveStartDate.toISOString().split('T')[0]}_${endDate.toISOString().split('T')[0]}`;
      break;
    
    case 'environmental_impact':
      data = await _exportEnvironmentalImpact(effectiveStartDate, endDate, req.user.neighborhood_id);
      filename = `environmental_impact_${effectiveStartDate.toISOString().split('T')[0]}_${endDate.toISOString().split('T')[0]}`;
      break;
    
    default:
      throw new AppError('Invalid report type', 400);
  }

  if (format === 'csv' && data.length > 0) {
    const parser = new Parser({ fields: Object.keys(data[0]) });
    const csv = parser.parse(data);
    
    res.header('Content-Type', 'text/csv');
    res.header('Content-Disposition', `attachment; filename="${filename}.csv"`);
    res.send(csv);
  } else {
    res.json({
      success: true,
      data: {
        report_type: reportType,
        start_date: effectiveStartDate,
        end_date: endDate,
        record_count: data.length,
        data
      }
    });
  }
});

// Get engagement metrics
const getEngagementMetrics = asyncHandler(async (req, res) => {
  const neighborhoodId = req.user.neighborhood_id;
  const { days = 30 } = req.query;

  const engagementQuery = `
    WITH user_engagement AS (
      SELECT 
        DATE(created_at) as date,
        COUNT(DISTINCT user_id) as daily_active_users,
        COUNT(*) as total_actions
      FROM analytics_events
      WHERE neighborhood_id = $1
      AND created_at >= CURRENT_DATE - INTERVAL '1 day' * $2
      GROUP BY DATE(created_at)
    ),
    content_engagement AS (
      SELECT 
        DATE(created_at) as date,
        SUM(CASE WHEN event_type = 'post_view' THEN 1 ELSE 0 END) as post_views,
        SUM(CASE WHEN event_type = 'post_like' THEN 1 ELSE 0 END) as post_likes,
        SUM(CASE WHEN event_type = 'post_comment' THEN 1 ELSE 0 END) as post_comments,
        SUM(CASE WHEN event_type = 'event_rsvp' THEN 1 ELSE 0 END) as event_rsvps
      FROM analytics_events
      WHERE neighborhood_id = $1
      AND created_at >= CURRENT_DATE - INTERVAL '1 day' * $2
      GROUP BY DATE(created_at)
    )
    SELECT 
      ue.date,
      ue.daily_active_users,
      ue.total_actions,
      ce.post_views,
      ce.post_likes,
      ce.post_comments,
      ce.event_rsvps,
      ROUND((ue.daily_active_users::numeric / NULLIF((SELECT COUNT(*) FROM users WHERE neighborhood_id = $1), 0)) * 100, 2) as dau_percentage
    FROM user_engagement ue
    JOIN content_engagement ce ON ue.date = ce.date
    ORDER BY ue.date DESC
  `;

  const result = await query(engagementQuery, [neighborhoodId, days]);

  res.json({
    success: true,
    data: {
      period_days: days,
      metrics: result.rows,
      summary: {
        avg_dau: Math.round(result.rows.reduce((sum, row) => sum + row.daily_active_users, 0) / result.rows.length),
        total_actions: result.rows.reduce((sum, row) => sum + row.total_actions, 0),
        engagement_trend: _calculateTrend(result.rows.map(r => r.daily_active_users))
      }
    }
  });
});

// Get growth metrics
const getGrowthMetrics = asyncHandler(async (req, res) => {
  const neighborhoodId = req.user.neighborhood_id;
  const { period = 'month' } = req.query;

  const periodDays = period === 'week' ? 7 : period === 'month' ? 30 : period === 'quarter' ? 90 : 365;

  const growthQuery = `
    WITH growth_data AS (
      SELECT 
        DATE_TRUNC('${period === 'week' ? 'day' : 'week'}', created_at) as period,
        COUNT(*) as new_users
      FROM users
      WHERE neighborhood_id = $1
      AND created_at >= CURRENT_DATE - INTERVAL '1 day' * $2
      GROUP BY period
    ),
    cumulative AS (
      SELECT 
        period,
        new_users,
        SUM(new_users) OVER (ORDER BY period) as total_users
      FROM growth_data
    )
    SELECT 
      period,
      new_users,
      total_users,
      ROUND((new_users::numeric / NULLIF(LAG(new_users) OVER (ORDER BY period), 0) - 1) * 100, 2) as growth_rate
    FROM cumulative
    ORDER BY period
  `;

  const result = await query(growthQuery, [neighborhoodId, periodDays]);

  res.json({
    success: true,
    data: {
      period,
      metrics: result.rows,
      summary: {
        total_growth: result.rows[result.rows.length - 1]?.total_users || 0,
        avg_growth_rate: _calculateAverage(result.rows.map(r => r.growth_rate).filter(r => r !== null))
      }
    }
  });
});

// Get safety metrics
const getSafetyMetrics = asyncHandler(async (req, res) => {
  const neighborhoodId = req.user.neighborhood_id;
  const { days = 30 } = req.query;

  const safetyQuery = `
    SELECT 
      COUNT(*) as total_alerts,
      COUNT(*) FILTER (WHERE type = 'emergency') as emergency_alerts,
      COUNT(*) FILTER (WHERE type = 'suspicious_activity') as suspicious_activities,
      COUNT(*) FILTER (WHERE type = 'safety_concern') as safety_concerns,
      COUNT(*) FILTER (WHERE status = 'resolved') as resolved_alerts,
      COUNT(*) FILTER (WHERE status IN ('active', 'responding')) as active_alerts,
      AVG(EXTRACT(EPOCH FROM (resolved_at - created_at))/60) as avg_resolution_minutes,
      COUNT(DISTINCT responder_id) as unique_responders
    FROM emergency_alerts
    WHERE neighborhood_id = $1
    AND created_at >= CURRENT_DATE - INTERVAL '1 day' * $2
  `;

  const result = await query(safetyQuery, [neighborhoodId, days]);

  res.json({
    success: true,
    data: {
      period_days: days,
      metrics: result.rows[0],
      safety_score: _calculateSafetyScore(result.rows[0])
    }
  });
});

// Track custom event
const trackEvent = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { eventType, eventData = {} } = req.body;

  const eventId = await query(
    'SELECT track_analytics_event($1, $2, $3)',
    [userId, eventType, eventData]
  );

  res.json({
    success: true,
    data: {
      event_id: eventId.rows[0].track_analytics_event
    }
  });
});

// Get funnel analytics
const getFunnel = asyncHandler(async (req, res) => {
  const { funnelName } = req.params;
  const { days = 30 } = req.query;

  const funnelQuery = `
    WITH funnel_data AS (
      SELECT 
        step_number,
        step_name,
        COUNT(DISTINCT user_id) as users,
        COUNT(DISTINCT session_id) as sessions
      FROM funnel_events
      WHERE funnel_name = $1
      AND created_at >= CURRENT_DATE - INTERVAL '1 day' * $2
      GROUP BY step_number, step_name
    ),
    conversion_rates AS (
      SELECT 
        step_number,
        step_name,
        users,
        sessions,
        ROUND((users::numeric / FIRST_VALUE(users) OVER (ORDER BY step_number)) * 100, 2) as conversion_rate,
        ROUND((users::numeric / LAG(users) OVER (ORDER BY step_number)) * 100, 2) as step_conversion_rate
      FROM funnel_data
    )
    SELECT * FROM conversion_rates
    ORDER BY step_number
  `;

  const result = await query(funnelQuery, [funnelName, days]);

  res.json({
    success: true,
    data: {
      funnel_name: funnelName,
      period_days: days,
      steps: result.rows,
      overall_conversion: result.rows[result.rows.length - 1]?.conversion_rate || 0
    }
  });
});

// Helper functions
async function _exportUserActivity(startDate, endDate, neighborhoodId) {
  const result = await query(
    `SELECT 
      u.id,
      u.email,
      u.first_name || ' ' || u.last_name as name,
      COUNT(DISTINCT p.id) as posts_created,
      COUNT(DISTINCT pc.id) as comments_made,
      COUNT(DISTINCT e.id) as events_hosted,
      COUNT(DISTINCT el.id) as listings_created
    FROM users u
    LEFT JOIN posts p ON u.id = p.user_id AND p.created_at BETWEEN $1 AND $2
    LEFT JOIN post_comments pc ON u.id = pc.user_id AND pc.created_at BETWEEN $1 AND $2
    LEFT JOIN events e ON u.id = e.host_id AND e.created_at BETWEEN $1 AND $2
    LEFT JOIN exchange_listings el ON u.id = el.user_id AND el.created_at BETWEEN $1 AND $2
    WHERE u.neighborhood_id = $3
    GROUP BY u.id`,
    [startDate, endDate, neighborhoodId]
  );
  return result.rows;
}

async function _exportCommunityStats(startDate, endDate, neighborhoodId) {
  const result = await query(
    `SELECT 
      metric_date as date,
      total_users,
      active_users,
      new_users,
      total_posts,
      total_events,
      total_exchanges,
      engagement_rate
    FROM community_metrics
    WHERE neighborhood_id = $1
    AND metric_date BETWEEN $2 AND $3
    ORDER BY metric_date`,
    [neighborhoodId, startDate, endDate]
  );
  return result.rows;
}

async function _exportEnvironmentalImpact(startDate, endDate, neighborhoodId) {
  const result = await query(
    `SELECT 
      date,
      exchanges_count,
      items_reused,
      co2_saved_kg,
      water_saved_liters,
      carpool_trips,
      carpool_distance_km
    FROM environmental_impact
    WHERE neighborhood_id = $1
    AND date BETWEEN $2 AND $3
    ORDER BY date`,
    [neighborhoodId, startDate, endDate]
  );
  return result.rows;
}

function _calculateTrend(values) {
  if (values.length < 2) return 'stable';
  const recent = values.slice(0, Math.floor(values.length / 2));
  const older = values.slice(Math.floor(values.length / 2));
  const recentAvg = _calculateAverage(recent);
  const olderAvg = _calculateAverage(older);
  const change = ((recentAvg - olderAvg) / olderAvg) * 100;
  return change > 5 ? 'increasing' : change < -5 ? 'decreasing' : 'stable';
}

function _calculateAverage(values) {
  return values.reduce((sum, val) => sum + (val || 0), 0) / values.length;
}

function _calculateSafetyScore(metrics) {
  const resolutionScore = metrics.avg_resolution_minutes < 10 ? 100 : metrics.avg_resolution_minutes < 30 ? 80 : 60;
  const responseScore = (metrics.resolved_alerts / metrics.total_alerts) * 100;
  const responderScore = Math.min(100, metrics.unique_responders * 10);
  return Math.round((resolutionScore + responseScore + responderScore) / 3);
}

module.exports = {
  getCommunityStats,
  getCommunityHealth,
  getUserActivity,
  getEnvironmentalImpact,
  getTrendingContent,
  createReport,
  getReports,
  updateReport,
  getDashboard,
  exportData,
  getEngagementMetrics,
  getGrowthMetrics,
  getSafetyMetrics,
  trackEvent,
  getFunnel
};